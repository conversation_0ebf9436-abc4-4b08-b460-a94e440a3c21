// src/components/EditScriptModal.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FiX } from 'react-icons/fi';
// Import shared types from the parent page component
import type { EditingPayload } from '../pages/SessionManagementPage';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (payload: EditingPayload, content: string) => void | Promise<void>;
  payload: EditingPayload;
}

// Styled Components
const ModalBackdrop = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  width: 90%;
  max-width: 800px;
  height: 80vh;
  background-color: #2d2d2d;
  color: #f8f8f2;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  h3 { margin: 0; }
  span {
    background-color: #444;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #f8f8f2;
  font-size: 1.5rem;
  cursor: pointer;
`;

const StyledTextArea = styled.textarea`
  flex-grow: 1;
  padding: 1rem;
  border: none;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  background-color: transparent;
  color: inherit;
  &:focus { outline: none; }
`;

const ModalFooter = styled.div`
  padding: 1rem;
  border-top: 1px solid #444;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const SaveButton = styled.button`
  padding: 10px 20px;
  border: none;
  background-color: ${({ theme }) => theme.success};
  color: white;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
`;

const CancelButton = styled.button`
  padding: 10px 20px;
  border: none;
  background-color: #555;
  color: white;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
`;

const EditScriptModal: React.FC<ModalProps> = ({ isOpen, onClose, onSave, payload }) => {
  const [content, setContent] = useState('');

  useEffect(() => {
    if (payload) {
        const scriptData = payload.session[payload.lang] || {
            id: payload.session.id,
            title: `NEW: ${payload.session.id} (${payload.lang})`,
            type: payload.session.category,
            description: "",
            duration: 15,
            script: []
        };
        setContent(JSON.stringify(scriptData, null, 2));
    }
  }, [payload]);

  if (!isOpen) return null;

  const handleSaveClick = () => {
    try {
      JSON.parse(content);
      onSave(payload, content);
    } catch (e) {
      alert("Invalid JSON format. Please correct it before saving.");
    }
  };

  return (
    <ModalBackdrop>
      <ModalContent>
        <ModalHeader>
          <h3>
            Editing: {payload.session.id} <span>{payload.lang.toUpperCase()}</span>
          </h3>
          <CloseButton onClick={onClose}><FiX /></CloseButton>
        </ModalHeader>
        <StyledTextArea
          value={content}
          onChange={(e) => setContent(e.target.value)}
        />
        <ModalFooter>
          <CancelButton onClick={onClose}>Cancel</CancelButton>
          <SaveButton onClick={handleSaveClick}>Save Changes</SaveButton>
        </ModalFooter>
      </ModalContent>
    </ModalBackdrop>
  );
};

export default EditScriptModal;