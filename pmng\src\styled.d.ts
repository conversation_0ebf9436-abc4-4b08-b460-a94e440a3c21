// pmng/src/styled.d.ts
import 'styled-components';

declare module 'styled-components' {
  // This interface defines the shape of our theme object.
  // It must match the keys in the `adminTheme` object in App.tsx.
  export interface DefaultTheme {
    name: string;
    primary: string;
    secondary: string;
    success: string;
    danger: string;
    background: string;
    surface: string;
    border: string;
    text: string;
    textSecondary: string;
    accent: string;
    textLight: string;
    cardShadow: string;
    headerShadow: string;
    borderSlight: string;
    shadowSmall: string;
    inputBackground: string;
    errorColor: string;
    successColor: string;
    disabledBackground: string;
    disabledText: string;
    tableHeaderBackground: string;
    tableRowEvenBackground: string;
    tableRowHoverBackground: string;
    tableBorder: string;
  }
}