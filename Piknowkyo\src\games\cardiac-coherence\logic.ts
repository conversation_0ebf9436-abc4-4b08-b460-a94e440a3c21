// src/games/cardiac-coherence/logic.ts

// Defines the different phases of the breathing cycle.
export enum GamePhase {
    Inhale = 'inhale',
    HoldIn = 'holdIn',
    Exhale = 'exhale',
    HoldOut = 'holdOut',
    Ready = 'ready', // Initial state before starting
    Finished = 'finished',
  }
  
  // Default timings for the adult mode (in seconds).
  export const ADULT_TIMINGS = {
    inhale: 5,
    holdIn: 1, // Short hold
    exhale: 5,
    holdOut: 1,
  };
  
  // Default timings for the child mode (in seconds), often shorter.
  export const CHILD_TIMINGS = {
    inhale: 4,
    holdIn: 1,
    exhale: 4,
    holdOut: 1,
  };
  
  // Represents the complete state of the game at any moment.
  export interface CardiacCoherenceState {
    phase: GamePhase;
    progress: number; // A value from 0 to 1 representing the progress within the current phase.
    elapsedTime: number; // Total time elapsed in the current phase.
    cycleCount: number; // How many breathing cycles have been completed.
    isFirstCycle: boolean; // To show initial instructions only once.
  }
  
  // Settings for a game session, chosen by the user before starting.
  export interface GameSettings {
    duration: number; // Total duration of the session in seconds.
    mode: 'adult' | 'child';
    timings: {
      inhale: number;
      exhale: number;
      holdIn: number;
      holdOut: number;
    };
  }
  
  /**
   * Creates the initial state for the game.
   * @returns The starting CardiacCoherenceState.
   */
  export const initializeState = (): CardiacCoherenceState => ({
    phase: GamePhase.Ready,
    progress: 0,
    elapsedTime: 0,
    cycleCount: 0,
    isFirstCycle: true,
  });
  
  /**
   * Calculates the next state of the game based on the time delta.
   * This is the core game engine.
   * @param currentState The current state of the game.
   * @param settings The settings for the current session.
   * @param deltaTime The time elapsed since the last update (in seconds).
   * @returns The new state of the game.
   */
  export const updateState = (
    currentState: CardiacCoherenceState,
    settings: GameSettings,
    deltaTime: number
  ): CardiacCoherenceState => {
    let { phase, elapsedTime, progress, cycleCount, isFirstCycle } = currentState;
  
    // If the game is ready, the first update starts it.
    if (phase === GamePhase.Ready) {
      return { ...currentState, phase: GamePhase.Inhale };
    }
  
    if (phase === GamePhase.Finished) {
      return currentState;
    }
  
    elapsedTime += deltaTime;
  
    let phaseDuration = 0;
    switch (phase) {
      case GamePhase.Inhale:  phaseDuration = settings.timings.inhale; break;
      case GamePhase.HoldIn:  phaseDuration = settings.timings.holdIn; break;
      case GamePhase.Exhale:  phaseDuration = settings.timings.exhale; break;
      case GamePhase.HoldOut: phaseDuration = settings.timings.holdOut; break;
    }
  
    // Check if the current phase is complete.
    if (elapsedTime >= phaseDuration) {
      elapsedTime = 0; // Reset for the next phase.
      
      // Transition to the next phase in the cycle.
      switch (phase) {
        case GamePhase.Inhale:  phase = GamePhase.HoldIn; break;
        case GamePhase.HoldIn:  phase = GamePhase.Exhale; break;
        case GamePhase.Exhale:  phase = GamePhase.HoldOut; break;
        case GamePhase.HoldOut:
          phase = GamePhase.Inhale;
          cycleCount += 1;
          // The first cycle is over after the first hold-out.
          if (isFirstCycle) {
              isFirstCycle = false;
          }
          break;
      }
    }
  
    // Recalculate phase duration for progress calculation, in case the phase just changed.
    switch (phase) {
      case GamePhase.Inhale:  phaseDuration = settings.timings.inhale; break;
      case GamePhase.HoldIn:  phaseDuration = settings.timings.holdIn; break;
      case GamePhase.Exhale:  phaseDuration = settings.timings.exhale; break;
      case GamePhase.HoldOut: phaseDuration = settings.timings.holdOut; break;
    }
    
    // Calculate progress (0 to 1). For hold phases, progress can be considered static (either 0 or 1).
    if (phase === GamePhase.Inhale) {
      progress = Math.min(1, elapsedTime / phaseDuration);
    } else if (phase === GamePhase.Exhale) {
      progress = 1 - Math.min(1, elapsedTime / phaseDuration);
    } else {
      // For hold phases, progress stays at the boundary (1 for holdIn, 0 for holdOut).
      progress = phase === GamePhase.HoldIn ? 1 : 0;
    }
  
    return { phase, progress, elapsedTime, cycleCount, isFirstCycle };
  };