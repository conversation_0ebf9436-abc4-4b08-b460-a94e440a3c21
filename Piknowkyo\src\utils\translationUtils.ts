// src/utils/translationUtils.ts

import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';

/**
 * Fetches the full translation object for a given language from Firestore.
 * This function replaces the logic that was previously in contentService.
 * @param lang The language code (e.g., 'en', 'fr', 'es').
 * @returns A promise that resolves to the translation data object.
 */
export const fetchTranslations = async (lang: string): Promise<any> => {
  console.log(`[translationUtils] Fetching translations for language: '${lang}' from Firestore...`);
  try {
    const firestoreRef = doc(db, 'AppLanguage', lang);
    const firestoreDoc = await getDoc(firestoreRef);

    if (!firestoreDoc.exists()) {
      console.error(`[translationUtils] Translations for language '${lang}' not found in Firestore.`);
      // Return an empty object to prevent the app from crashing.
      // i18next will use fallback keys.
      return {};
    }

    const firestoreData = firestoreDoc.data();
    // We remove the timestamps, as i18next doesn't need them.
    delete firestoreData.createdAt;
    delete firestoreData.updatedAt;

    console.log(`[translationUtils] Successfully fetched translations for '${lang}'.`);
    return firestoreData;

  } catch (error) {
    console.error(`[translationUtils] Failed to fetch translations for '${lang}':`, error);
    // In case of an error, return an empty object so the app can continue.
    return {};
  }
};