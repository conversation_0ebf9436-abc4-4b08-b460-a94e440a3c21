{"navigation": {"home": "Accueil", "sessions": "Séances", "games": "<PERSON><PERSON>", "journal": "Journal", "stats": "Statistiques", "blog": "Blog", "profile": "Profil", "monetization": "Premium", "settings": "Paramètres", "about": "À Propos", "audio-assets": "Mes Audios", "recommendation": "Recommandation", "favorites": "<PERSON><PERSON>", "lexicon": "Lexique"}, "lexicon": {"relatedConcepts": "Concepts liés", "title": "Lexique", "subtitle": "Explorez les concepts, les émotions et les techniques de notre approche.", "searchPlaceholder": "Rechercher un terme...", "allCategories": "<PERSON>ut", "noResults": "Aucune définition trouvée. Essayez d'ajuster votre recherche ou votre filtre.", "categories": {"basic_emotions": "Émotions de base", "sentiments": "Sentiments", "cognitive_patterns": "<PERSON><PERSON><PERSON><PERSON> cognitifs", "somatic_sensations": "Sensations somatiques", "desired_outcomes": "Résultats souhaités", "sensory_channels": "Canaux sensoriels", "modalities": "Modalités", "durations": "<PERSON><PERSON><PERSON>", "intensities": "Intensités", "techniques": "Techniques", "energetic_systems": "Systèmes énergétiques", "spiritual_concepts": "Concepts spirituels"}}, "trial": {"banner": {"message": "Votre essai se termine le {{date}} ({{days}} jours restants)"}}, "common": {"welcome": "Bienvenue sur PiKnowKyo", "ok": "OK", "cancel": "Annuler", "save": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "restart": "Recommencer", "previous": "Précédent", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "days": "jours", "unknown": "une date inconnue"}, "home": {"title": "Bienvenue sur PiKnowKyo", "subtitle": "Votre espace pour cultiver paix intérieure et épanouissement.", "exploreButton": "Explorer les Séances", "welcomeText": "Commencez votre parcours vers un mieux-être. Choisissez une pratique ou explorez vos outils personnalisés.", "quickAccess": "Accès Rapide", "learnMore": "En savoir plus sur PiKnowKyo", "welcomeUser": "<PERSON> retour, {{name}} ! Prêt pour votre prochaine étape ?", "customizeQuickAccess": "Personnaliser l'Accès Rapide", "modal": {"title": "Personnaliser l'Accès Rapide", "toolsSection": "Outils & Actions", "pagesSection": "Pages Principales", "categoriesSection": "Catégories de Séances", "gamesSection": "<PERSON><PERSON>", "favoritesSection": "Vos <PERSON>ances Favorites", "searchPlaceholder": "Rechercher des séances...", "searchGamesPlaceholder": "Rechercher des jeux..."}}, "sessions": {"title": "Explorer les Séances", "meditation": "Méditation", "hypnosis": "Hypnose", "affirmations": "Affirmations", "custom": "<PERSON><PERSON>", "description": "Découvrez notre collection de séances guidées pour votre épanouissement personnel.", "searchPlaceholder": "Rechercher une séance...", "allTypes": "Tous types", "allDurations": "<PERSON>utes durées", "durationLabel": "<PERSON><PERSON><PERSON>", "type": "Type", "category": "<PERSON><PERSON><PERSON><PERSON>", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noSessionsFound": "Aucune séance trouvée correspondant à vos critères.", "noCategoriesAvailable": "Aucune catégorie de session n'est disponible pour le moment.", "noResultsInGroup": "Aucune session dans cette catégorie ne correspond à vos critères.", "clearFilters": "Effacer les filtres", "filterBy": "Filtrer par", "sessionType": "Type de séance", "gridView": "Vue Grille", "listView": "<PERSON><PERSON>", "noResultsMatchCriteria": "Aucune séance ne correspond à vos critères.", "noSessionsAvailable": "Aucune session disponible pour le moment.", "story": "Histoire", "favoritesFilter": "Favoris uniquement", "noResults": "Aucune séance trouvée pour cette catégorie ou ce filtre.", "noResults.filtered": "Aucune séance ne correspond à vos filtres actuels.", "recommendation.button": "Obt<PERSON><PERSON> une Recommandation", "filter": {"searchPlaceholder": "Rechercher dans toutes les séances...", "showAll": "Toutes", "showFavorites": "<PERSON><PERSON><PERSON>", "filteringByTag": "Filtrage par étiquette :", "duration": {"all": "<PERSON>utes durées"}, "allCategories": "Toutes les catégories", "toggleView": "Changer de vue", "clear": "Effacer les filtres"}, "filters": {"title": "Filtrer les Séances", "showFavorites": "Afficher les favoris uniquement", "reset": "Réinitialiser les filtres", "apply": "Appliquer"}, "card": {"new": "Nouveau", "toggleFavorite": "A<PERSON>ter/Retirer des favoris"}, "viewModes": {"grid": "Grille", "list": "Liste"}, "duration": {"label": "<PERSON><PERSON><PERSON>", "under15": "Moins de 15 min", "15to30": "15 - 30 min", "over30": "Plus de 30 min"}, "mood": {"label": "<PERSON><PERSON>", "all": "Toutes les humeurs", "calm": "Calme", "energized": "Énergique", "focused": "Concentré(e)", "stressed": "Stressé(e)"}, "toggleView": "Changer de vue"}, "games": {"title": "Mini-Jeux de Développement Personnel", "intro": "Testez et améliorez vos compétences avec nos mini-jeux amusants et stimulants.", "zenTetris": {"title": "Zen Tetris", "description": "Une version relaxante du célèbre jeu de blocs. Améliorez votre concentration et votre gestion du stress.", "rules": "Placez les pièces qui tombent pour compléter des lignes horizontales. Plus vous éliminez de lignes d'un coup, plus vous gagnez de points. Le jeu accélère progressivement.", "controls": "Contr<PERSON>les tactiles", "tips": "Restez calme, planifiez vos mouvements et essayez de créer des combos pour maximiser votre score.", "touchTap": "Tap rapide : Rotation", "touchLeft": "Glisser gauche : <PERSON><PERSON><PERSON><PERSON> à gauche", "touchRight": "Glisser droite : <PERSON><PERSON><PERSON><PERSON> à droite", "touchDown": "Glisser bas : <PERSON><PERSON> douce", "touchButtons": "Boutons de contrôle en bas"}, "cardiacCoherence": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Un exercice de respiration guidée pour synchroniser votre cœur et votre esprit. Améliorez votre concentration et réduisez le stress.", "setup": {"title": "Configurer la session", "mode": "Mode", "adult": "Adulte", "child": "<PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "useTTS": "Guide vocal (TTS)", "useSoundEffects": "Effets sonores"}, "getReady": "Pré<PERSON>ez-vous...", "inhale": "Inspirez...", "exhale": "Expirez...", "hold": "<PERSON><PERSON><PERSON>...", "finished": "Session terminée", "finishedTitle": "Session terminée", "finishedMessage": "Bravo ! Vous avez terminé votre session de respiration."}, "estimatedDuration": "<PERSON><PERSON><PERSON> estimée", "personalBest": "Record personnel", "savedGameProgress": "<PERSON><PERSON>", "maxLevels": "Ce jeu contient {{maxLevels}} niveaux de difficulté.", "yourBestScore": "Votre meilleur score sur ce jeu est de {{score}} points.", "moveLeft": "<PERSON>éplacer à gauche", "moveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "softDrop": "<PERSON><PERSON> douce", "rotate": "Rotation", "level": "Niveau", "lines": "<PERSON><PERSON><PERSON>", "nextPiece": "<PERSON><PERSON><PERSON>", "info": "Infos", "keywords": "Mots-clés", "continueGame": "Continuer la partie", "newGame": "Nouvelle partie", "gameInfo": "Informations sur le jeu", "gameRules": "<PERSON><PERSON><PERSON> du <PERSON>eu", "gameOver": "<PERSON><PERSON> termin<PERSON>", "finalScore": "Score final", "newRecord": "Nouveau record !", "playAgain": "Rejouer", "backToGames": "Retour aux jeux", "pause": "Pause", "resume": "Reprendre", "quit": "<PERSON><PERSON><PERSON>", "gameOverSummary": "Félicitations ! Votre score final est de {{score}} points et vous avez atteint le niveau {{level}} en {{time}} secondes."}, "game": {"start": "Commencer", "resume": "Reprendre", "pauseButton": "Mettre en pause", "orientationHint": "Tournez pour une meilleure expérience", "modal": {"rulesTitle": "<PERSON><PERSON><PERSON>", "pausedTitle": "<PERSON><PERSON> en Pause", "gameOverTitle": "Partie Terminée !", "return": "Retour", "restart": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Reprendre", "start": "Commencer", "pausedMessage": "Votre partie est en pause. Reprenez quand vous êtes prêt.", "gameOverMessage": "Bien joué ! Votre score final est de {{score}} et vous avez atteint le niveau {{level}}.", "gameOverSummary": "Félicitations ! Votre score final est de {{score}} points et vous avez atteint le niveau {{level}} en {{time}} secondes."}, "zenTetris": {"ruleMoveLeft": "<PERSON>éplacer à gauche", "ruleMoveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "ruleSoftDrop": "Chute douce (soft drop)", "ruleRotate": "Rotation", "rulePause": "Pause", "rules1": "Empilez les blocs pour former des lignes complètes et marquez des points. La vitesse augmente avec les niveaux !", "rules2": "Contrôles :"}, "controls": {"keyboard": "<PERSON><PERSON><PERSON>", "touch": "Tactile"}, "info": "Infos", "level": "Niveau", "lines": "<PERSON><PERSON><PERSON>", "score": "Score", "time": "Temps", "nextPiece": "<PERSON><PERSON><PERSON>", "moveLeft": "<PERSON>éplacer à gauche", "moveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "rotate": "Rotation", "softDrop": "<PERSON><PERSON> douce"}, "journal": {"title": "Journal de Suivi", "trackingJournal": "Journal de Suivi", "yourNotes": "Votre Journal pour cette Séance", "noEntries": "Aucune entrée de journal pour le moment.", "addEntry": "A<PERSON><PERSON>z votre réflexion sur cette séance...", "description": "Retrouvez ici toutes vos notes personnelles, classées par séance. Réfléchissez à vos expériences et suivez votre progression.", "noNotesYet": "Votre journal est encore vide.", "startSessionPrompt": "Commencez une séance et prenez des notes pour voir vos réflexions ici.", "unknownSession": "Séance (ID: {{id}})", "noteSingular": "{{count}} note", "notesPlural": "{{count}} notes", "seeAllNotes": "Voir toutes les {{count}} notes...", "loginToUse": "Connectez-vous pour utiliser la fonction journal.", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "deleteConfirmText": "Êtes-vous sûr de vouloir supprimer définitivement cette entrée de journal ?", "previousEntriesTitle": "Entrées précédentes"}, "rating": {"loginToRate": "Connectez-vous pour évaluer cette session."}, "and": " et ", "stats": {"title": "Vos Statistiques de Bien-être", "sessionsFollowed": "<PERSON><PERSON><PERSON><PERSON>", "daysDaysStreakDesc": "Jours où vous vous êtes connecté et avez terminé une activité.", "duration": {"hours": "h", "min": "min", "h": "h"}, "timesExploredSingular": "fois exploré", "timesExploredPlural": "fois explorés", "and": " et ", "affinityAnalysis.noConcepts": "rien de spécifique", "affinityAnalysis.intro": "Basé sur vos séances terminées, voici un aperçu de votre parcours et de vos affinités:", "affinityAnalysis.summary": "Vous avez le plus souvent exploré des thèmes liés à : ", "affinityAnalysis.emotions": "Émotions et Sentiments comme {{list}}.", "affinityAnalysis.outcomes": "Résultats souhaités tels que {{list}}.", "affinityAnalysis.techniques": "Techniques utilisées dont {{list}}.", "affinityAnalysis.somatic": "Sensations somatiques explorées incluant {{list}}.", "affinityAnalysis.cognitive": "Modèles cognitifs abordés tels que {{list}}.", "affinityAnalysis.modalities": "Modalités de bien-être comme {{list}}.", "affinityAnalysis.energetic": "Concepts énergétiques incluant {{list}}.", "affinityAnalysis.duration": "Durées de sessions typiques comme {{list}}.", "affinityAnalysis.intensity": "Intensités de sessions telles que {{list}}.", "affinityAnalysis.noDetails": "Pas encore de détails spécifiques pour cette catégorie. Continuez à explorer !", "affinityAnalysis.overallTopConcepts": "Vos concepts les plus explorés (toutes catégories confondues) sont : {{list}}.", "affinityAnalysis.moreDetailsLink": "Voir tous les concepts explorés pour plus de détails.", "journalEntries": "Vos Entrées de Journal", "showAllJournalEntries": "Voir toutes les {{count}} entrées", "showAllActivityHistory": "Voir tout l'historique d'activité", "noJournalEntries": "Vous n'avez pas encore d'entrées de journal. Réalisez des sessions et prenez des notes pour voir vos réflexions ici !", "viewEntry": "Voir l'entrée", "forSession": "pour la session", "sessionsCompleted": "Séances complétées", "daysStreak": "Jours consécutifs", "totalMinutes": "Minutes totales", "noData": "Vous n'avez encore terminé aucune séance. Commencez une séance pour voir vos statistiques ici !", "sessionsCompletedDesc": "Nombre total de séances que vous avez terminées.", "completionsPlural": "terminées", "completionsSingular": "terminée", "activityHistory": "Historique d'Activité", "noActivityHistory": "Aucun historique d'activité disponible. Commencez une session ou un jeu pour le voir ici !", "completedOn": "<PERSON><PERSON><PERSON><PERSON> le", "sleepPatterns": "Cycles de Sommeil", "heartRateVariability": "Variabilité de la Fréquence Cardiaque", "exerciseTracking": "Suivi de l'Exercice", "nutritionHydration": "Nutrition & Hydratation", "futureFeature": "Fonctionnalité future", "comingSoon": "Bientôt disponible", "description": "<PERSON><PERSON>z votre parcours, célébrez vos progrès et découvrez vos tendances.", "sessionsFollowedDesc": "Nombre de sessions uniques avec des notes.", "totalTime": "Temps Total en Séance", "totalTimeDesc": "Temps cumulé estimé.", "favoriteSession": "<PERSON><PERSON><PERSON>", "favoriteSessionDesc": "La plus notée.", "notesWritten": "Total Notes Écrites", "notesWrittenDesc": "Nombre de réflexions enregistrées.", "typesFollowed": "Répartition par Type de Séance", "timePerSession": "<PERSON><PERSON><PERSON> par S<PERSON>ance (Estimé)", "noTypesYet": "Aucun type de séance spécifique suivi pour le moment.", "noTimePerSession": "Aucune donnée de temps par séance disponible.", "timesPlural": "fois", "timesSingular": "fois", "notesPlural": "notes", "noteSingular": "note", "gameHighScores": "Meilleurs Scores des Jeux", "noGameData": "Vous n'avez encore établi aucun record dans les jeux. Allez jouer pour voir vos scores ici !", "levelLabel": "Niveau", "yourAffinityProfile": "Votre Profil d'Affinité", "affinityDescription": "Les concepts et émotions que vous avez le plus explorés à travers vos sessions.", "noAffinityData": "Aucune donnée d'affinité disponible. Complétez des sessions pour voir votre profil ici !", "timesExplored": "fois exploré", "healthMetrics": "Mesures de Santé & Bien-être", "futureDevMessage": "Cette section intégrera bientôt les données des appareils connectés pour fournir des informations sur vos cycles de sommeil, la variabilité de votre fréquence cardiaque, l'exercice, la nutrition, l'hydratation, et bien plus encore. Restez à l'écoute pour une vue holistique complète de votre bien-être !"}, "blog": {"title": "Journal Communautaire", "description": "Partagez vos expériences, découvertes et inspirations avec la communauté PiKnowKyo. Tous les messages sont anonymes.", "searchPlaceholder": "Rechercher des messages...", "allCategories": "Toutes catégories", "writeNewPost": "Écrire un nouveau message", "postPlaceholder": "Votre message (sera publié anonymement)...", "category": "<PERSON><PERSON><PERSON><PERSON>", "publishing": "Publication...", "publish": "Publier", "loginToPost": "Vous devez être connecté pour publier un message.", "noPostsYet": "Aucun message pour le moment dans cette catégorie ou correspondant à votre recherche.", "noPostsFound": "Aucun message trouvé pour cette catégorie ou ce terme de recherche.", "unsyncedPostTooltip": "Ce message est sauvegardé localement et sera synchronisé lorsque vous serez en ligne.", "unsyncedCommentTooltip": "Ce commentaire est sauvegardé localement et sera synchronisé lorsque vous serez en ligne.", "like": "<PERSON><PERSON>", "comments": "Commentaires", "addComment": "Ajouter un commentaire", "commentPlaceholder": "Votre commentaire (anonyme)...", "postComment": "Publier le commentaire", "noCommentsYet": "Aucun commentaire pour le moment. Soyez le premier à commenter !", "backToBlog": "Retour au blog", "postNotFound": "Message introuvable", "commentsSectionTitle": "Commentaires", "yourCommentPlaceholder": "Votre commentaire...", "sending": "Envoi...", "sendComment": "Envoyer", "loginToComment": "Connectez-vous pour ajouter un commentaire.", "sampleAuthor": "Auteur Anonyme", "samplePostContent": "Contenu détaillé du message. Ce message parle de l'importance de la pleine conscience dans notre quotidien stressant et comment de simples exercices peuvent apporter une grande paix intérieure.", "sampleCommenter1": "Commentateur1", "sampleCommenter2": "AutrePersonne", "sampleComment1": "Super message !", "sampleComment2": "<PERSON><PERSON><PERSON> in<PERSON>, merci du partage.", "anonymousUser": "Utilisateur Anonyme", "unknownDate": "Date inconnue", "categories": {"général": "Général", "gratitude": "Gratitude", "défis": "<PERSON><PERSON><PERSON><PERSON>", "inspirations": "Inspirations", "questions": "Questions"}}, "about": {"title": "À propos", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, votre compagnon de croissance personnelle et de bien-être.", "features": {"customizable": "Séances entièrement personnalisables selon vos besoins.", "journal": "Journal personnel pour suivre votre progression et vos réflexions.", "stats": "Statistiques détaillées pour visualiser votre évolution.", "games": "Mini-jeux pour développer vos compétences cognitives."}, "philosophy": {"title": "Notre Philosophie : Le Voyage de Pi à Kyo", "pi": {"title": "Pi (π)", "description": "<PERSON>'infini, le mystère sacré de l'univers et l'harmonie fondamentale qui nous unit tous. C'est le point de départ, l'ouverture à l'inconnu."}, "know": {"title": "Know (<PERSON><PERSON><PERSON><PERSON>)", "description": "L'exploration, l'apprentissage structuré et la clarté mentale. C'est l'acquisition des outils et des compréhensions pour naviguer le chemin."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "L'enseignement, la sagesse incarnée, l'illumination et le partage altruiste de la lumière découverte. C'est l'aboutissement et le rayonnement."}, "conclusion": "PiKnowKyo est plus qu'une application, c'est une boussole pour votre croissance intérieure, inspirée par"}, "tools": {"title": "Nos Outils pour Votre Épanouissement", "description": "Nous offrons une gamme variée de séances et d'outils conçus pour vous accompagner sur votre chemin de croissance personnelle :", "hypnosis": "Hypnose Évolutive pour explorer votre subconscient et initier des changements profonds.", "meditation": "Méditations Guidées pour cultiver la pleine conscience, la paix intérieure et la résilience émotionnelle.", "affirmations": "Affirmations Positives pour reprogrammer vos pensées et renforcer votre confiance en vous.", "nlp": "Training PNL (Programmation Neuro-Linguistique) pour améliorer votre communication et atteindre vos objectifs.", "stories": "Histoires Métaphoriques pour stimuler votre imagination et faciliter l'intégration de nouvelles perspectives."}, "experience": {"title": "Une Expérience Holistique Conçue pour Vous", "audio": "Audio 100% configurable (musique, ambiance, voix, binaural).", "guidedPaths": "Parcours guidés et création de séances sur mesure.", "community": "Communauté bienveillante et classement anonyme (optionnel).", "blog": "Blog interne avec articles, conseils et ressources inspirantes.", "multilang": "Support multi-langue et thèmes clair/sombre personnalisables.", "notifications": "Notifications de motivation douces pour vous accompagner."}, "monetization": {"title": "Monétisation Éthique :", "description": "Nous proposons un essai gratuit, un abonnement optionnel pour un accès complet, des publicités minimales et non-intrusives (contournables avec l'abonnement), et la possibilité de dons pour soutenir notre mission."}, "community": {"title": "Rejoignez Notre Communauté", "description": "PiKnowKyo est conçu pour ceux qui valorisent la connaissance de soi, l'organisation de leurs pensées et leur productivité personnelle dans une optique de mieux-être. Que vous soyez étudiant, professionnel, chercheur, ou simplement un esprit curieux en quête d'harmonie, notre application est votre alliée.", "contact": "Pour toute question, suggestion ou si vous avez besoin d'assistance, n'hésitez pas à nous envoyer un courriel à :", "website": "Vous pouvez également visiter notre site web", "moreInfo": "pour plus d'informations"}}, "monetization": {"title": "<PERSON><PERSON><PERSON>rez <PERSON>re Expérience", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les fonctionnalités pour maximiser votre parcours de bien-être, ou continuez avec notre généreux plan gratuit.", "plans": {"free": {"name": "Plan G<PERSON>uit"}, "premium": {"name": "Piknowkyo Premium"}, "billing": {"month": "mois"}}, "features": {"free": {"meditations": "Accès illimité aux séances de méditations et d'histoires", "musicAndTTS": "Musique de fond et voix TTS basiques", "stats": "Statistiques de progression", "journal": "Journal de suivi", "adUnlock": "Regarder une publicité pour déverrouiller une session pendant 1 heure", "support": "Support via les réseaux sociaux"}, "premium": {"allSessions": "Accès illimité à TOUTES les séances (hypnose, PNL, etc.)", "cloudTTS": "Voix TTS du cloud", "ambientSounds": "Sons d'ambiance et binauraux avancés", "stats": "Statistiques de progression", "games": "Accès aux mini-jeux", "journal": "Journal de suivi", "noAds": "Expérience sans publicité"}}, "status": {"title": "État de l'Abonnement", "trialActive": "Vous bénéficiez actuellement d'un essai Premium.", "renewsOn": "Votre abonnement Premium se renouvelle le <strong>{{date}}</strong>.", "cancelsOn": "Votre abonnement Premium expirera le <strong>{{date}}</strong>.", "freePlan": "Vous utilisez actuellement le plan Gratuit.", "cancelsOnDateTime": "<PERSON><PERSON><PERSON> accès prendra fin le <strong>{{date}} à {{heure}}</strong>.", "renewsOnDateTime": "Votre abonnement se renouvelle le <strong>{{date}} à {{heure}}</strong>."}, "trial": {"endsIn_one": "Votre essai se termine dans {{count}} jour.", "endsIn_other": "Votre essai se termine dans {{count}} jours.", "ended": "Votre période d'essai est terminée."}, "actions": {"manage": "<PERSON><PERSON>rer l'abonnement", "upgradeNow": "Mettre à niveau maintenant", "currentPlan": "Plan actuel", "subscribe": "<PERSON>'abonner", "subscribeError": "Échec de l'obtention de l'URL de paiement Stripe. Veuillez réessayer.", "manageError": "Échec de l'obtention de l'URL du portail client Stripe. Veuillez réessayer."}, "toast": {"checkoutSuccess": "Abonnement réussi ! Votre plan devrait être mis à jour sous peu.", "checkoutCanceled": "Processus d'abonnement annulé."}}, "sessionDetails": {"yourNotes": "Votre Journal pour cette Séance", "viewInLexicon": "Voir '{{term}}' dans le Lexique", "averageRating": "<PERSON> moyenne", "noRatingsYet": "Aucune note pour l'instant. Soyez le premier à noter !", "description": "Description", "expectedBenefits": "Bénéfices Attendus", "keywords": "Mots-clés", "startSession": "Commencer la Séance", "backToSessions": "Retour aux Séances", "audioConfigGlobal": "Configuration Audio de la Séance", "userReviews": "Avis des Utilisateurs", "previousNotes": "Notes précédentes", "voiceConfigTitle": "Configuration de la Voix", "yourRating": "Votre Évaluation", "tagsTitle": "Étiquettes", "lastUpdated": "<PERSON><PERSON><PERSON> mise à jour le {{date}}", "tagTooltip": "Voir la définition dans le Lexique", "benefitsTitle": "Bénéfices", "dnd": {"label": "<PERSON><PERSON> (Application)", "permissionNeededInfo": "Activer demandera la permission pour les notifications afin d'optimiser ce mode.", "permissionDeniedWarning": "Permission de notification refusée. Le mode NPD de l'application est actif, mais les notifications système ne sont pas affectées."}}, "units": {"minutes": "min", "points": "pts", "notAvailable": "N/D", "seconds": "sec", "min": "min", "ratings_one": "avis", "ratings_other": "avis"}, "menu": {"navigation": "Navigation", "account": "<PERSON><PERSON><PERSON>"}, "notFound": {"message": "<PERSON><PERSON><PERSON><PERSON>, la page que vous recherchez n'existe pas.", "backHome": "Retour à l'accueil"}, "quiz": {"title": "Quiz", "description": "Sélectionnez un quiz pour commencer à tester vos connaissances !", "comingSoon": "Les quiz arrivent bient<PERSON>t ! Restez connectés."}, "history": {"title": "Historique", "description": "Consultez vos résultats et votre progression.", "comingSoon": "L'historique sera bientôt disponible."}, "categories": {"title": "Catégories", "description": "Choisissez une catégorie pour explorer les quiz associés.", "comingSoon": "Les catégories arrivent bi<PERSON><PERSON><PERSON> !"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "Ce navigateur ne supporte pas les notifications."}, "pseudoGenerator": {"adjectives": {"light": "<PERSON><PERSON><PERSON>", "wind": "Vent", "ocean": "<PERSON><PERSON><PERSON>", "mountain": "<PERSON><PERSON><PERSON>", "star": "<PERSON><PERSON><PERSON>", "forest": "<PERSON><PERSON><PERSON>", "river": "Rivière", "sun": "<PERSON><PERSON>", "moon": "<PERSON><PERSON>", "aurora": "Au<PERSON>re"}, "nouns": {"serene": "<PERSON><PERSON>", "calm": "Calme", "wise": "Sage", "peaceful": "Paisible", "clairvoyant": "Clairvoyant", "harmonious": "<PERSON><PERSON><PERSON><PERSON>", "awakened": "<PERSON><PERSON><PERSON><PERSON>", "free": "Libre", "creative": "<PERSON><PERSON><PERSON><PERSON>", "intuitive": "Intuitif"}}, "app": {"name": "Piknowkyo", "theme_light": "Passer au thème clair", "theme_dark": "Passer au thème sombre", "logo_alt": "Logo Piknowkyo"}, "auth": {"common": {"email_placeholder": "<PERSON><PERSON><PERSON>", "password_placeholder": "<PERSON><PERSON>", "or_separator": "OU", "please_wait_loading": "Veuillez patienter...", "success_redirect": "Connexion ou inscription ré<PERSON>ie ! Redirection..."}, "login": {"subtitle": "Bienvenue de retour !", "button": "Se connecter", "button_loading": "Connexion en cours...", "google_button": "Se connecter avec Google", "google_button_loading": "Connexion avec Google...", "error_invalid_credentials": "Email ou mot de passe incorrect.", "error_google_popup_closed": "La fenêtre de connexion Google a été fermée. Veuillez réessayer.", "error_google_popup_cancelled": "Une requête de popup Google est déjà en cours ou a été annulée. Veuillez réessayer.", "error_general": "Échec de la connexion. Veuillez réessayer.", "toggle_signup": "Pas encore de compte ? S'inscrire"}, "signup": {"subtitle": "<PERSON><PERSON><PERSON> votre compte !", "confirm_password_placeholder": "Confirm<PERSON> le Mot de Passe", "button": "S'inscrire", "button_loading": "Inscription en cours...", "google_button": "S'inscrire avec Google", "google_button_loading": "Inscription avec Google...", "error_password_mismatch": "Les mots de passe ne correspondent pas.", "error_email_in_use": "Cet email est déjà utilisé. Veuillez vous connecter.", "error_weak_password": "Le mot de passe est trop faible (minimum 6 caractères).", "error_general": "Échec de l'inscription. Veuillez réessayer.", "toggle_login": "Déjà un compte ? Se connecter"}, "logout": {"button": "Se déconnecter", "button_aria_label": "Se déconnecter de votre compte"}}, "preferences": {"language": {"question": "Choisissez votre langue préférée"}, "notifications": {"question": "Souh<PERSON>ez-vous recevoir des notifications de motivation ?"}, "premium": {"question": "Voulez-vous tester les fonctionnalités premium gratuitement (avec pubs non intrusives) ?"}, "yes": "O<PERSON>", "no": "Non", "thanks": "<PERSON><PERSON><PERSON> !", "validate": "Valider mes préférences"}, "questionnaire": {"goal": {"question": "Quel est votre objectif principal ?", "relaxation": "Détente", "confidence": "Confiance en soi", "stress": "Gestion du stress", "spirituality": "Spiritualité", "other": "<PERSON><PERSON>"}, "experience": {"question": "<PERSON><PERSON>-vous déjà pratiqué l'hypnose ou la méditation ?", "never": "<PERSON><PERSON>", "sometimes": "<PERSON><PERSON><PERSON>", "regularly": "Régulièrement"}, "audio": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON>-vous une séance avec musique, sons naturels, ou silence ?", "music": "Musique", "nature": "Sons naturels", "silence": "Silence"}, "thanks": "<PERSON><PERSON><PERSON> !", "viewSuggestions": "Voir mes suggestions"}, "notificationTest": {"heading": "Test de notifications", "platform": "Plateforme actuelle :", "status": "Statut des notifications web :", "title": "Test de notification", "body": "Ceci est un test de notification depuis PiKnowKyo", "sendButton": "Envoyer une notification de test"}, "actions": {"back": "Retour", "backToBlog": "Retour au blog", "backToHome": "Retour à l'accueil", "backToSessionDetails": "Retour aux détails", "backToSessions": "Retour aux séances", "backToSettings": "Retour aux paramètres", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirm": "Confirmer la <PERSON>", "deleting": "Suppression...", "enterFullscreen": "Mode plein écran", "exitFullscreen": "<PERSON><PERSON><PERSON> le plein écran", "ok": "OK", "pause": "Pause", "play": "Lecture", "preview": "Prévisualiser", "restart": "Recommencer", "startSession": "Commencer la séance", "stopPreview": "<PERSON><PERSON><PERSON><PERSON>", "stopTest": "<PERSON><PERSON><PERSON><PERSON> le test", "testSound": "Tester le son", "testVoice": "Tester la voix", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde...", "previewSound": "Prévisualiser le son"}, "dictionary": {"basic_emotions": {"anger": {"name": "<PERSON><PERSON>"}, "sadness": {"name": "Tristesse"}, "joy": {"name": "<PERSON><PERSON>"}, "fear": {"name": "<PERSON><PERSON>"}, "calm": {"name": "Calme"}}, "somatic_sensations": {"chest_oppression": {"name": "Oppression thoracique"}, "knotted_stomach": {"name": "Estomac noué"}, "shoulder_neck_tension": {"name": "Tension épaules et cou"}, "heaviness_limbs": {"name": "Lourdeur dans les membres"}, "racing_heart": {"name": "Cœur qui s'emballe"}}, "cognitive_patterns": {"mental_rumination": {"name": "Ruminations mentales"}, "future_anxiety": {"name": "Anxiété concernant l'avenir"}, "critical_inner_dialogue": {"name": "Dialogue intérieur critique"}, "mental_fog": {"name": "<PERSON><PERSON><PERSON><PERSON> mental"}, "comparison": {"name": "Comparaison aux autres"}}, "desired_outcomes": {"deep_calm": {"name": "Calme profond"}, "letting_go": {"name": "<PERSON><PERSON><PERSON> prise"}, "grounding": {"name": "Ancrage"}, "stress_reduction": {"name": "Réduction du stress"}, "increased_self_esteem": {"name": "Confiance en soi accrue"}, "joyful_living": {"name": "Vivre joyeusement"}}}, "audioAssets": {"title": "Gestion des fichiers audio", "musicTitle": "Musiques", "ambientTitle": "Sons d'ambiance", "noMusics": "Aucune musique disponible", "noAmbiants": "Aucun son d'ambiance disponible", "selectFile": "Sélectionner un fichier", "changeFile": "Changer le <PERSON>er", "uploadMusicPrompt": "Téléverser une nouvelle musique", "uploadAmbientPrompt": "<PERSON><PERSON><PERSON><PERSON>r un nouveau son d'ambiance", "uploading": "Téléversement...", "uploadSuccess": "Fichier {{fileName}} téléversé avec succès !", "uploadError": "Erreur lors du téléversement", "previewError": "Impossible de lire l'aperçu audio", "cannotDeleteDefault": "Les fichiers par défaut ne peuvent pas être supprimés", "confirmDeleteTitle": "Confirmer la <PERSON>", "confirmDeleteMessage": "Êtes-vous sûr de vouloir supprimer ce fichier ?", "deleteSuccess": "Fichier supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de <PERSON>"}, "audioConfig": {"musicTrack": "Piste musicale", "musicTitle": "Musique de Fond", "musicTooltip": "Choisissez une musique d'ambiance pour accompagner votre séance. Vous pouvez ajuster le volume.", "ambientSound": "Ambiance sonore :", "ambientTitle": "Ambiance sonore", "ambientTooltip": "Ajoutez des sons naturels ou d'ambiance pour créer l'atmosphère parfaite.", "brainwavePresets": "Ondes Cérébrales (Battement) :", "testVoiceText": "Ceci est un test de la voix sélectionnée.", "testVoiceError": "Le test n'a pas pu être effectué.", "binauralBeats": "Sons Binauraux / Isochrones", "binauralTooltip": "<PERSON><PERSON><PERSON>rez des sons pour influencer vos ondes cérébrales. Un casque est requis pour un effet binaural optimal.", "binauralBeatsSetup": {"volume": "Volume Binaural :"}, "baseFrequency": "Fréquence de Base (Hz)", "baseFrequencyPreset": "Préréglage Fréquence de Base :", "beatFrequency": "<PERSON><PERSON><PERSON> de Battement (Hz)", "beatFrequencyPreset": "Préréglage Fréquence <PERSON> Battement (Onde Cérébrale) :", "customOption": "<PERSON><PERSON><PERSON><PERSON>", "customFrequencyDescPlaceholder": "Fréquence personnalisée sélectionnée ou aucun préréglage actif.", "selectPresetToSeeDescription": "Sélectionnez un préréglage pour voir sa description.", "headphonesRequired": "Casque requis pour l'effet binaural", "baseFreqPresets": {"solfeggio": {"category": "Fré<PERSON><PERSON>", "174": {"label": "174 Hz - Fondation", "desc": "Associée à l'ancrage, la sécurité et le soulagement de la douleur. Aide à créer une fondation stable."}, "285": {"label": "285 Hz - Régénération Tissulaire", "desc": "Liée à la guérison et à la régénération des tissus au niveau cellulaire. Favorise le rajeunissement."}, "396": {"label": "396 Hz (Ut) - Libération Culpabilité & Peur", "desc": "Aide à libérer la culpabilité, la peur et les blocages subconscients. Soutient l'ancrage et l'autonomisation."}, "417": {"label": "417 Hz (Re) - Facilitation du Changement", "desc": "Aide à nettoyer les expériences traumatisantes et à faciliter le changement positif. Purifie l'énergie négative."}, "528": {"label": "528 Hz (Mi) - Transformation & Miracles", "desc": "Connue comme la 'fréquence de l'amour'. Associée à la réparation de l'ADN, à l'énergie et à la clarté."}, "639": {"label": "639 Hz (Fa) - Connexion & Relations", "desc": "Favorise l'harmonie dans les relations, la compréhension, la tolérance et l'amour. Améliore la communication."}, "741": {"label": "741 Hz (Sol) - Éveil Intuition & Expression", "desc": "Liée au nettoyage des toxines, à l'éveil de l'intuition et à la promotion de l'expression de soi."}, "852": {"label": "852 Hz (La) - Retour à l'Ordre Spirituel", "desc": "Aide à éveiller la force intérieure et la réalisation de soi. Connecte à un ordre spirituel supérieur."}, "963": {"label": "963 Hz (Si) - Conscience Divine & Unité", "desc": "Associée à l'éveil à l'état parfait, à l'unité et à la connexion avec la conscience divine."}}, "chakras": {"category": "Fréquences des Chakras", "root": {"label": "Chakra Racine (~256 Hz)", "desc": "Lié à l'ancrage, la sécurité, les instincts de survie et la vitalité physique."}, "sacral": {"label": "Chakra Sacré (~288 Hz)", "desc": "Gouverne la créativité, les émotions, la sexualité et le plaisir."}, "solarPlexus": {"label": "Chakra Plexus Solaire (~320 Hz)", "desc": "Centre du pouvoir personnel, de l'estime de soi et de la volonté."}, "heart": {"label": "Chakra du Cœur (~341.3 Hz)", "desc": "<PERSON>é à l'amour, la compassion, le pardon et l'équilibre émotionnel."}, "throat": {"label": "Chakra de la Gorge (~384 Hz)", "desc": "Centre de la communication, de l'expression de soi et de la vérité."}, "thirdEye": {"label": "Chakra du 3ème Œil (~426.7 Hz)", "desc": "Gouverne l'intuition, la perspicacité, la sagesse et les capacités psychiques."}, "crown": {"label": "Chakra Coronal (~480 Hz)", "desc": "Connecte à la spiritualité, à la conscience divine et à l'illumination."}}, "planetary": {"category": "Fré<PERSON><PERSON> Planétaires", "om": {"label": "OM / <PERSON><PERSON> (136.10 Hz) - Alignement", "desc": "La fréquence OM, correspondant à la période orbitale de la Terre. Utilisée pour un ancrage profond et l'alignement spirituel."}, "sun": {"label": "Soleil (126.22 Hz) - Vitalité", "desc": "Favorise la vitalité, la joie et le sens de soi. Associé à l'intuition et à la force vitale."}, "earth": {"label": "Terre (Jour) (194.18 Hz) - Ancrage", "desc": "Pour l'ancrage, la stabilité et la connexion avec l'énergie de la Terre. Favorise l'équilibre physique."}, "moon": {"label": "Lune (210.42 Hz) - Émotions", "desc": "Lié aux émotions, à la sensibilité et au cycle féminin. Soutient le flux émotionnel."}}, "organsDetailed": {"category": "Fréquences Organes & Glandes (Détaillé)", "pineal": {"label": "<PERSON><PERSON><PERSON> (662 Hz)", "desc": "Une fréquence spécifiquement citée pour la résonance de la glande pinéale, distincte des fréquences spirituelles plus larges."}, "pituitary": {"label": "G<PERSON><PERSON> (636 Hz)", "desc": "Soutient la fonction pituitaire, l'équilibre hormonal et l'intuition supérieure."}, "brain_general": {"label": "Cerveau (Résonance Générale - 330 Hz)", "desc": "Une fréquence de résonance générale parfois associée à la santé et à l'activité globale du cerveau."}}, "otherNotable": {"category": "Autres Fréquences Notables", "432hz": {"label": "432 Hz - Accordage Naturel", "desc": "Considérée par certains comme une fréquence d'accordage plus naturelle, alignée avec l'univers. Favorise le calme."}}}, "beatPresets": {"delta": {"category": "Ondes Delta (0.5-4 Hz) - Sommeil Profond", "2_5hz": {"label": "2.5 Hz - Soulagement Douleur & Relaxation", "desc": "Associé à la libération d'endorphines, peut aider à soulager la douleur et induire une relaxation profonde."}}, "theta": {"category": "On<PERSON> (4-8 Hz) - Méditation Profonde", "5hz": {"label": "5 Hz - Intuition & Souvenir des Rêves", "desc": "Stimule l'intuition, la créativité ; idéal pour la méditation profonde et l'accès aux souvenirs de rêves."}, "7_83hz": {"label": "7.83 Hz - <PERSON><PERSON><PERSON><PERSON> (Terre)", "desc": "Résonance Schumann principale. Favorise l'ancrage, la réduction du stress et un sentiment de connexion."}}, "alpha": {"category": "Ondes Alpha (8-12 Hz) - Concentration Détendu", "10hz": {"label": "10 Hz - Pic Alpha (Apprentissage & Sérénité)", "desc": "Pic Alpha. Améliore l'apprentissage, la mémoire, la sérénité et réduit l'anxiété. Élévateur d'humeur."}}, "beta": {"category": "<PERSON><PERSON> (12-38 Hz) - Pensée Active", "14hz": {"label": "14 Hz - Concentration Active (SMR)", "desc": "Augmente la concentration, la vigilance ; idéal pour la résolution de problèmes et la pensée analytique."}}, "gamma": {"category": "Ondes Gamma (38Hz+) - Performance de Pointe", "40hz": {"label": "40 Hz - Performance Optimale & Perspicacité", "desc": "Traitement de l'information de haut niveau, perception accrue, résolution de problèmes complexes."}}, "spiritualStates": {"category": "États Spirituels & Conscience", "pinealActivationTheta": {"label": "Harmon<PERSON> (Thêta - 7.5Hz)", "desc": "Ondes Thêta pour une méditation profonde visant à harmoniser et stimuler doucement la glande pinéale."}, "chakraCleansing": {"label": "Nettoyage Chakras (Thêta - 6Hz)", "desc": "Ondes Thêta pour soutenir la méditation profonde pour le nettoyage énergétique et l'alignement des chakras."}, "astralProjection": {"label": "Aide Projection Astrale (Thêta - 6.5Hz)", "desc": "Ondes Thêta souvent associées à l'induction d'états propices aux expériences hors du corps."}, "kundaliniSupport": {"label": "<PERSON><PERSON><PERSON> (Alpha/Thêta - 8Hz)", "desc": "Fréquence frontière Alpha-Thêta pour soutenir les pratiques méditatives visant à éveiller l'énergie Kundalini."}, "merkabaMeditation": {"label": "Méditation Merkaba (Alpha - 10.5Hz)", "desc": "Ondes Alpha pour aider dans les méditations d'activation du Merkaba (corps de lumière)."}}, "cognitiveEnhancement": {"category": "Amélioration Cognitive & Bien-être", "creativityBoostTheta": {"label": "<PERSON><PERSON> (Thêta - 5.5Hz)", "desc": "Ondes Thêta pour améliorer la perspicacité, l'inspiration et la résolution créative de problèmes."}, "anxietyReductionAlpha": {"label": "<PERSON><PERSON><PERSON> Anxi<PERSON> (Alpha - 10Hz)", "desc": "Ondes Alpha de pointe pour promouvoir le calme, réduire le stress et soulager l'anxiété."}, "sleepImprovementDelta": {"label": "Amélioration Sommeil (Delta - 2Hz)", "desc": "Ondes Delta pour entraîner le cerveau vers des schémas de sommeil profond et réparateur."}}}, "music": {"none": "Aucune"}, "ambient": {"none": "Aucun"}, "noDescription": "Pas de description disponible", "presets": {"chakras": "Chakras", "organs": "Organes", "otherNotable": "Autres fréquences notables"}, "webAudioNotSupported": "Web Audio API non supportée par votre navigateur.", "ttsTitle": "Synthèse Vocale", "ttsTooltip": "Ajustez le volume de la voix du guide. Le type de voix et la langue sont gérés dans les paramètres généraux de l'application.", "musicSound": "Musique :", "volume": "Volume", "selectPreset": "-- <PERSON><PERSON> un préréglage --", "selectState": "-- Choisir un état --", "targetFrequencyInfo": "Oreille G: {{leftEar}} Hz, Oreille D: {{rightEar}} Hz"}, "errors": {"missingPostId": "ID du message manquant.", "paymentError": "Une erreur s'est produite lors du traitement de votre paiement.", "manageSubscriptionError": "Impossible d'accéder à la gestion de votre abonnement.", "postNotFound": "Message non trouvé.", "cantLoadPost": "Impossible de charger le message.", "cantLoadComments": "Impossible de charger les commentaires.", "cantAddComment": "<PERSON><PERSON>ur lors de l'ajout du commentaire.", "cantAddPost": "Erreur lors de la publication du message.", "cantLoadSessions": "Impossible de charger les données des sessions.", "encryptionFailed": "Échec du chiffrement", "cantLoadJournal": "Impossible de charger les entrées du journal.", "cantLoadUserData": "Impossible de charger vos donn<PERSON>.", "blogLoadLocalFailed": "Échec du chargement des données du blog depuis le stockage local.", "blogSyncFailed": "Échec de la synchronisation avec le serveur.", "postSaveFailed": "Votre message n'a pas pu être sauvegardé localement.", "commentSaveFailed": "Votre commentaire n'a pas pu être sauvegardé localement.", "commentLoadFailed": "Échec du chargement des commentaires.", "userNotAuthenticated": "Vous devez être connecté pour téléverser des fichiers.", "cantLoadAssets": "Impossible de charger les fichiers audio.", "cloudVoicesPremium": "Les voix cloud sont une fonctionnalité premium. Veuillez sélectionner le fournisseur Navigateur ou mettre à niveau.", "cantLoadDictionary": "Impossible de charger les données du dictionnaire."}, "warnings": {"decryptionFailed": "Impossible de déchiffrer les données. Elles pourraient être corrompues ou provenir d'une session antérieure.", "missingEncryptionKey": "Alerte de sécurité : La clé de chiffrement n'est pas définie. Les données seront stockées en clair."}, "player": {"sessionEnded": "Fin de la séance.", "readyToStart": "Prêt à commencer...", "audioSettings": "Volumes", "volumeControls": "Réglages des Volumes", "music": "Musique", "ambient": "Ambiance", "voice": "Voix", "binaural": "Battements binauraux"}, "settings": {"title": "Paramètres", "appLanguage": "Langue de l'application", "appLanguageInfo": "Cela change la langue de l'ensemble de l'application.", "audio": "Audio", "theme": "Thème", "lightMode": "Mode clair", "darkMode": "Mode sombre", "language": "<PERSON><PERSON>", "voice": "Voix", "autoVoice": "Voix automatique", "testVoice": "Tester la voix", "saveConfig": "Sauvegarder la configuration", "ttsSectionTitle": "<PERSON>ynth<PERSON>e vocale (TTS)", "ttsProvider": "Fournisseur TTS", "ttsTestText": "<PERSON>ci est un test de synthèse vocale.", "ttsTestError": "Erreur lors du test de la voix", "downloadingVoice": "Téléchargement de la voix...", "voiceDownloaded": "Voix téléchargée", "noVoiceForSelection": "Aucune voix disponible pour cette sélection", "noSpecificVoiceForLang": "Aucune voix spécifique pour cette langue. Voici toutes les voix disponibles:", "explanationsTitle": "Explications", "audioAssetsManagementTitle": "Gestion des fichiers audio", "audioAssetsInfo": "<PERSON><PERSON><PERSON> vos musiques et sons d'ambiance personnalisés", "goToAudioAssets": "<PERSON><PERSON><PERSON> les fichiers audio", "ttsProviderBrowser": "Navigateur (Hors ligne & Rapide)", "ttsProviderCloud": "Google Cloud (Haute Qualité)", "premiumOnly": "Premium Uniquement", "providerLabels": {"browser": "Navigateur", "cloud": "Cloud (IA)"}, "ttsProviderInfo": {"browser": "Utilise les voix intégrées du navigateur", "cloud": "Voix IA de haute qualité (Bientôt...)"}, "modal": {"saveSuccessTitle": "Configuration sauvegardée", "saveSuccessMessage": "Vos paramètres ont été sauvegardés avec succès", "testErrorTitle": "Erreur de test"}}, "test": {"newSongTitle": "Titre de la nouvelle chanson", "addNewSong": "Ajouter une nouvelle chanson"}, "sync": {"offline": "<PERSON><PERSON> ligne", "syncing": "Synchronisation...", "error": "Erreur de sync ({{count}})", "pending": "{{count}} en attente", "synchronized": "Synchronisé", "syncedMinutesAgo": "Sync il y a {{minutes}}min", "syncedHoursAgo": "Sync il y a {{hours}}h", "online": "En ligne", "clickToSync": "Cliquer pour synchroniser"}, "loading": {"user": "Chargement des informations utilisateur...", "profile": "Chargement du profil...", "blog": "Chargement du blog...", "comments": "Chargement des commentaires...", "post": "Chargement du message...", "content": "Chargement du contenu...", "stats": "Chargement des statistiques...", "sessions": "Chargement des séances...", "session": "Chargement de la séance...", "journal": "Chargement de votre journal...", "default": "Chargement...", "authenticating": "Authentification...", "language": "Configuration de la langue...", "initializing": "Initialisation des données...", "category": "Chargement...", "pseudo": "Génération en cours...", "audioAssets": "Chargement des fichiers audio...", "categories": "Chargement des catégories...", "voices": "Chargement des voix..."}, "plans": {"free": {"title": "Plan G<PERSON>uit", "price": "0$", "currentPlan": "Votre Plan Actuel", "switchToFree": "Passer au plan Gratuit"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Facturé mensuellement, annulez à tout moment.", "manageSub": "Gérer l'Abonnement", "subscribe": "Passer à Premium"}, "billing": {"month": "mois"}}, "features": {"free": {"baseMeditations": "Accès aux méditations et histoires de base", "backgroundMusic": "Musique de fond et voix TTS basiques", "stats": "Statistiques de progression", "blog": "Accès au blog communautaire"}, "premium": {"allSessions": "Accès illimité à TOUTES les séances (hypnose, PNL, etc.)", "ambientSounds": "Sons d'ambiance et binauraux avancés", "customSessions": "Création de séances personnalisées", "games": "Accès aux mini-jeux de pleine conscience", "journal": "Journal de suivi détaillé", "motivationNotifs": "Notifications de motivation personnalisées", "calendar": "Calendrier et programmes personnalisés (à venir)", "customAudio": "Possibilité d'utiliser vos propres sons et musiques", "noAds": "Expérience sans publicité", "prioritySupport": "Support prioritaire"}}, "legal": {"privacy": "Politique de confidentialité", "terms": "Te<PERSON><PERSON> et conditions"}, "profile": {"title": "Mon Profil", "notConnectedTitle": "<PERSON><PERSON>", "pleaseLogin": "Veuillez vous connecter pour accéder à votre profil.", "publicPseudo": "Pseudo public", "regeneratePseudo": "Générer un nouveau pseudo", "preferencesTitle": "Préférences", "appLanguage": "Langue de l'application", "grammaticalGenderLabel": "Comment p<PERSON><PERSON><PERSON><PERSON>-vous que l'on s'adresse à vous dans les scripts ?", "grammaticalGenderInfo": "Cela nous aidera à adapter certains textes pour une expérience plus personnalisée.", "accountActionsTitle": "Gestion du Compte", "logout": "Déconnexion", "deleteAccount": "Supprimer mon compte", "deleteConfirmTitle": "Confirmer la Suppression", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer votre compte ? Toutes vos données, y compris votre progression et vos notes de journal, seront définitivement effacées. Cette action est irréversible.", "accountDeletedSuccess": "Votre compte et toutes vos données ont été supprimés.", "memberSince": "Membre depuis le {{date}}", "statsTitle": "Votre Activité", "stats": {"sessionsCompleted": "Séances complétées", "daysStreak": "Jours consécutifs", "totalMinutes": "Minutes totales", "noData": "Vous n'avez encore terminé aucune séance. Commencez une séance pour voir vos statistiques ici !", "sessionsCompletedDesc": "Nombre total de séances que vous avez terminées.", "completionsPlural": "terminées", "completionsSingular": "terminée"}}, "gender": {"masculine": "Au masculin", "feminine": "Au féminin", "neutral": "Neutre"}, "sessionTypes": {"hypnosis": "Hypnose", "meditation": "Méditation", "training": "Entraînement", "story": "Histoire", "journaling": "Journaling", "visualization": "Visualisation", "relaxation": "Relaxation", "coaching": "Coaching", "sleep induction": "Induction au Sommeil", "sleep-induction": "Induction au Sommeil", "roleplay": "<PERSON><PERSON>", "affirmation": "Affirmation", "gratitude practice": "<PERSON><PERSON><PERSON> de la Gratitude", "gratitude-practice": "<PERSON><PERSON><PERSON> de la Gratitude", "breathwork": "Travail Respiratoire", "motivational speech": "Discours de Motivation", "motivational-speech": "Discours de Motivation", "guided imagery": "<PERSON><PERSON>", "guided-imagery": "<PERSON><PERSON>", "problem solving": "Résolution de Problèmes", "problem-solving": "Résolution de Problèmes", "creative writing": "Écriture Créative", "creative-writing": "Écriture Créative", "mindful movement": "Mouvement Conscient", "mindful-movement": "Mouvement Conscient", "self-compassion": "Auto-Compassion", "focus enhancement": "Amélioration de la Concentration", "focus-enhancement": "Amélioration de la Concentration", "silence": "Silence", "beginner": "Débutant", "work_break": "Pause au travail"}, "sessionTypesDescriptions": {"hypnosis": "Une séance guidée pour induire un état de relaxation et de suggestibilité profonds, en utilisant un ton lent et répétitif pour réduire le stress ou cibler des objectifs subconscients comme la confiance ou le changement d'habitudes.", "meditation": "Une séance guidée de pleine conscience ou de relaxation axée sur la respiration, la conscience corporelle ou le calme mental, avec un ton apaisant et des pauses fréquentes pour renforcer la présence.", "training": "Une séance énergique pour motiver et guider un entraînement physique ou mental, délivrant des instructions claires et des encouragements pour des activités comme des séances d'entraînement ou des tâches de productivité.", "story": "Une séance narrative immersive, telle qu'une histoire de fantasy ou d'aventure, avec des descriptions vivantes et un ton captivant pour engager l'imagination de l'auditeur.", "journaling": "Une séance introspective guidant l'auditeur à travers des questions ou des invites de réflexion, avec un ton calme et encourageant et des pauses pour permettre la rédaction de réponses.", "visualization": "Une séance guidée pour créer une imagerie mentale vive, comme la visualisation d'objectifs ou de scènes apaisantes, en utilisant un ton descriptif et immersif pour améliorer la concentration.", "relaxation": "Une séance axée sur la détente physique et mentale, utilisant un ton doux et un rythme lent pour libérer les tensions et favoriser un repos profond.", "coaching": "Une séance de motivation offrant des conseils et des stratégies pour la croissance personnelle ou professionnelle, avec un ton optimiste et responsabisant pour inspirer l'action.", "sleep induction": "Une séance apaisante conçue pour aider l'auditeur à s'endormir, utilisant un ton lent et calmant avec une imagerie douce et une cadence progressive pour encourager le repos.", "roleplay": "Une séance narrative interactive plaçant l'auditeur dans un rôle (p. ex. explorateur, détective), avec un ton dynamique et engageant et des pauses pour des réponses imaginées.", "affirmation": "Une séance délivrant des déclarations positives et responsabilisantes pour renforcer la confiance ou l'état d'esprit, utilisant un ton clair et édifiant avec des pauses pour laisser les affirmations s'intégrer.", "gratitude practice": "Une séance guidant l'auditeur à réfléchir aux choses pour lesquelles il est reconnaissant, avec un ton chaleureux et réfléchi et des pauses pour encourager une connexion émotionnelle profonde.", "breathwork": "Une séance guidant des techniques de respiration spécifiques pour réduire le stress ou augmenter l'énergie, avec un ton régulier et rythmé et des instructions claires pour le rythme de la respiration.", "motivational speech": "Une séance inspirante délivrant un discours puissant pour stimuler la détermination et la concentration, en utilisant un ton passionné et édifiant pour dynamiser l'auditeur.", "guided imagery": "Une séance menant l'auditeur à travers des scènes mentales détaillées (p. ex. une plage paisible), avec un ton descriptif et apaisant pour améliorer la relaxation ou la créativité.", "problem solving": "Une séance guidant l'auditeur à travers des étapes structurées pour aborder un défi personnel ou professionnel, avec un ton clair et de soutien et des pauses pour la réflexion.", "creative writing": "Une séance fournissant des invites ou des scénarios pour inspirer l'écriture créative, avec un ton imaginatif et encourageant et des pauses pour que l'auditeur écrive.", "mindful movement": "Une séance guidant des mouvements physiques doux (p. ex. yoga ou étirements) avec un ton calme et instructif, synchronisée avec des signaux de respiration.", "self-compassion": "Une séance favorisant la bienveillance envers soi-même à travers des réflexions guidées et des affirmations, avec un ton chaleureux et nourrissant pour promouvoir la guérison émotionnelle.", "silence": "Une séance de silence complet, permettant à l'auditeur de se concentrer intérieurement, de méditer ou de simplement profiter de sons ambiants, de battements binauraux, de musique ou d'une combinaison de ceux-ci sur une durée définie.", "focus enhancement": "Une séance conçue pour améliorer la concentration et la clarté mentale, utilisant un ton régulier et motivant avec des techniques comme l'ancrage ou des intervalles de concentration chronométrés."}, "premium": {"actions": {"subscribe": "S'abonner au Premium", "watchAd": "Voir pub pour débloquer", "watch": "Regarder la publicité", "loadingAd": "Chargement de la pub...", "stopPreview": "<PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribeShort": "<PERSON>'abonner", "manageShort": "<PERSON><PERSON><PERSON>", "watchShort": "Voir Pub"}, "ads": {"comingSoon": "Cette fonctionnalité sera bientôt disponible ! Merci de votre patience."}, "features": {"advancedSessions": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'accès exclusif à des séances avancées comme l'hypnose et les entraînements spécialisés."}, "ambient": {"title": "D<PERSON><PERSON><PERSON>quez les Sons d'Ambiance", "description": "Améliorez vos sessions avec une bibliothèque de sons d'ambiance apaisants comme la pluie, les forêts ou les océans."}, "binaural": {"title": "D<PERSON><PERSON><PERSON>quez les Sons Binauraux", "description": "Accédez à un large éventail de fréquences pour influencer vos ondes cérébrales pour une méditation profonde, la concentration ou la relaxation."}, "custom": {"titleInList": "Téléverser un audio personnalisé"}}}, "main_menu": {"open_menu_aria_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu"}, "adReward": {"unlockItemTitle": "Déverrouiller la fonctionnalité", "unlockItemDescription": "Regardez une courte publicité pour déverrouiller cette fonctionnalité pour une durée limitée.", "unlockNow": "Déverrouiller avec une pub", "watching": "Visionnage en cours..."}, "subscription": {"title": "Statut de l'Abonnement", "upgradePrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les fonctionnalités en passant à un plan premium.", "upgradeButton": "Passer à Premium", "manage": "<PERSON><PERSON>rer l'abonnement", "status": {"free": "Plan G<PERSON>uit", "premium": "Premium Actif", "trial": "<PERSON><PERSON><PERSON>"}, "trial": {"endsIn": "Votre essai se termine dans {{count}} jours.", "endsToday": "Votre essai se termine aujourd'hui !", "ended": "Votre période d'essai est terminée."}}, "recommendationKeywords": {"calm": "calme|s<PERSON><PERSON>ni<PERSON>|paix|apaiser|détente|relax|anxiété|stress", "focus": "concentr|focus|attention|alerte|clarté|performance|objectif", "energy": "énerg|motiv|vitalité|dynami|force|puissance", "comfort": "réconfort|apaiser|triste|compassion|douceur|émotion", "creativity": "créativ|inspir|idée|imag|solution|problème", "sleep": "sommeil|dormir|nuit|insomnie|endormir", "approach": {"guided": "guid|histoire|voix|hypno|méditation", "explore": "explor|pensée|journal|écrire|question", "visualize": "visualis|imagin|rêve|objectif|lieu", "body": "corps|respir|mouvement|sensation|physique"}}, "recommendationAssistant": {"title": "Assistant <PERSON><PERSON><PERSON>", "results_title": "D'après notre conversation, voici une suggestion pour vous :", "default_reason": "Cette session est conçue pour vous aider à vous recentrer et à trouver le calme.", "q_initial": ["Bienvenue. Pour vous aider à trouver ce dont vous avez besoin, dites-moi, comment vous sentez-vous en ce moment ?", "Bonjour. <PERSON><PERSON><PERSON><PERSON> la session idéale pour vous. Quelle est l'émotion principale présente pour vous aujourd'hui ?"], "affirm_propose_somatic": ["Merci pour ce partage. Connectons-nous au corps. Souvent, les émotions se manifestent par des sensations physiques.", "Je vous entends. Explorons comment ce sentiment apparaît dans votre corps."], "q_somatic_location": ["Où ce sentiment semble-t-il se centrer dans votre corps ?", "Si vous scannez votre corps, où cette sensation est-elle la plus présente ?"], "q_verification": ["<PERSON><PERSON>, une sensation de {{context}}. Est-ce que cela vous semble juste ?", "D'accord, donc nous remarquons {{context}}. Est-ce une description précise ?"], "affirm_propose_cognitive": ["D'accord, explorons le mental. Nos pensées et nos émotions sont profondément liées.", "Compris. Examinons les pensées qui pourraient accompagner ce sentiment."], "q_cognitive_pattern": ["Lequel de ces schémas de pensée résonne le plus avec ce que vous vivez ?", "À quoi ressemble la voix dans votre tête en ce moment ?"], "affirm_propose_behavioral": ["C'est utile. <PERSON><PERSON><PERSON> comment ce sentiment pourrait influencer vos actions.", "D'accord. <PERSON><PERSON><PERSON>, nos sentiments se traduisent par des comportements spécifiques. Voyons cela."], "q_behavioral_pattern": ["Comment ce sentiment s'est-il manifesté dans vos actions récemment ?", "L'un de ces comportements récents vous semble-t-il familier ?"], "affirm_propose_contextual": ["Compris. L'endroit où nous sommes peut influencer ce que nous ressentons. Explorons le contexte.", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, considérons les situations où ce sentiment a tendance à apparaître."], "q_contextual_trigger": ["Dans quel domaine de votre vie ce sentiment semble-t-il le plus souvent déclenché ?", "Quand ce sentiment fait-il habituellement surface ?"], "affirm_propose_metaphorical": ["D'accord, essayons une approche différente. Parfois, les images parlent plus que les mots.", "Prenons un peu de recul et utilisons notre imagination."], "q_metaphorical_image": ["Si ce sentiment était un paysage ou un objet, lequel de ceux-ci serait-il ?", "Laquelle de ces images représente le mieux votre état intérieur actuel ?"], "affirm_propose_energetic": ["Merci. Passons à un niveau plus subtil. Parlons de votre énergie.", "D'accord. Au-delà des pensées et des sentiments, il y a votre énergie vitale."], "q_energetic_sensation": ["Comment déc<PERSON><PERSON>z-vous votre énergie personnelle en ce moment ?", "Laquelle de ces options décrit le mieux votre état énergétique actuel ?"], "q_outcome": ["C'est très éclairant. Merci. Pour finir, quel serait le résultat idéal pour vous après une session ?", "D'accord, avec tout cela en tête, quel état espérez-vous cultiver ?"], "opt_idk": "Je ne suis pas sûr(e)", "opt_idk_or_other": "Aucun de ces choix / <PERSON><PERSON> sûr(e)", "opt_surprise_me": "Surprenez-moi", "opt_yes": "<PERSON><PERSON>, c'est exact", "opt_no": "Non, pas tout à fait", "opt_pivot_cognitive_from_somatic": "Ce n'est pas vraiment dans mon corps, plutôt dans mes pensées...", "opt_pivot_metaphorical_from_cognitive": "C'est difficile à décrire avec des mots, c'est plus une image...", "opt_behavior_procrastination": "Re<PERSON>re les choses à plus tard", "opt_behavior_isolation": "M'isoler des autres", "opt_behavior_irritability": "Être facilement agacé(e)", "opt_behavior_avoidance": "<PERSON><PERSON><PERSON> les situations difficiles", "opt_context_work": "Au travail ou en lien avec celui-ci", "opt_context_relationships": "Dans mes relations", "opt_context_home": "À la maison", "opt_context_internal": "Ça vient de l'intérieur, peu importe le contexte", "opt_metaphor_storm": "Une tempête intérieure", "opt_metaphor_fog": "Un brouillard épais", "opt_metaphor_weight": "Un poids lourd", "opt_metaphor_knot": "Un nœud serré", "opt_energy_stuck": "Bloquée ou stagnante", "opt_energy_overactive": "En surrégime ou agitée", "opt_energy_drained": "Vid<PERSON> ou épuisée", "opt_energy_scattered": "Dispersée ou peu concentrée"}, "voiceSelector": {"groupTitle": {"en-AU": "<PERSON><PERSON><PERSON> (Australie)", "en-GB": "<PERSON><PERSON><PERSON> (Royaume-Uni)", "en-IN": "<PERSON><PERSON><PERSON> (Inde)", "en-US": "<PERSON><PERSON><PERSON> (États-Unis)", "es-ES": "Espagnol (Espagne)", "es-US": "Espagnol (États-Unis)", "fr-CA": "<PERSON><PERSON><PERSON> (Canada)", "fr-FR": "<PERSON><PERSON><PERSON> (France)"}}}