* open google ai studio, paste the following in the system instruction:
----------------------
considerant le <PERSON>chier en attachement, revise cette session et ajuste juste les tags et les categorie des recommendation qui s'applique, donne moi juste les key/value a ajouter qui manque, voici une example :
{
  "id": "guided_quantum_healing_journey",
  // This is the unique identifier for the session, written in lowercase with underscores to describe the session's focus.
  "title": "Guided Quantum Healing Journey",
  // A meaningful and engaging title for the session, clearly indicating its purpose and appeal.
  "description": "This 30-minute guided meditation script takes you on a transformative journey through quantum energy fields, using visualization techniques inspired by <PERSON><PERSON> and <PERSON><PERSON>. It helps you align your mind and body with healing frequencies, fostering emotional balance and cellular rejuvenation through soothing imagery and affirmations.",
  // A concise paragraph describing the session, including its purpose, techniques used, and overall experience.
  "benefits": "Emotional balance, Cellular rejuvenation, Increased energy, Improved focus, Reduced anxiety, Enhanced well-being",
  // A comma-separated list of key benefits, highlighting specific outcomes the user can expect from the session.
  "durationMinutes": 30,
  // The duration of the session in minutes, represented as a number.
  "type": "meditation",
  // The type of session, such as 'meditation', 'hypnosis', or 'visualization', defining its category.
  "language": "en",
  // The language of the session, using a two-letter code (e.g., 'en' for English, 'fr' for French).
  "createdAt": "2025-06-10",
  // The creation date of the session in 'YYYY-MM-DD' format.
  "tags": ["healing", "quantum", "meditation", "energy"]
  // An array of relevant keywords describing the session's themes or focus areas.
}
----------------------
* Choisir gemini flash lite comme model
* ajouter en piece jointe le fichier definition.json
* copier la portion script d'une séance
* Then paste juste la portion script et remplacé la portion metadata avant le script dans chaque session avec le résultats
