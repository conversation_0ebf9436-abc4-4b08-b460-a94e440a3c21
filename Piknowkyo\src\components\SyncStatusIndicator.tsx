// src/components/SyncStatusIndicator.tsx
import React from 'react';
import styled, { useTheme, keyframes } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNetworkStatus } from '../services/useNetworkStatus';
import AppIcon from './AppIcon';

// Define the rotation animation
const rotateAnimation = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

// A simple wrapper for the icon, which handles animation and color
// The $color prop is now optional to handle potentially undefined theme colors.
const StatusIconWrapper = styled.div<{ $color?: string; $isAnimating: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ $color }) => $color || 'currentColor'};
  animation: ${({ $isAnimating }) => $isAnimating ? `${rotateAnimation} 1.2s linear infinite` : 'none'};
`;

const SyncStatusIndicator: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const online = useNetworkStatus();

  // Placeholder values until sync functionality is implemented
  const isSyncing = false;
  const syncStatus: 'idle' | 'success' = 'success';
  const syncErrors: any[] = [];
  const lastSyncTimestamp = Date.now(); // Using current time for demonstration

  const getStatus = () => {
    if (!online) {
      return {
        iconName: 'sync-offline',
        color: theme.textMuted,
        tooltip: t('sync.offline'),
        isAnimating: false,
      };
    }

    if (isSyncing) {
      return {
        iconName: 'syncing',
        color: theme.primary,
        tooltip: t('sync.syncing'),
        isAnimating: true,
      };
    }

    if (syncErrors.length > 0) {
      return {
        iconName: 'sync-error',
        color: theme.errorColor,
        tooltip: t('sync.error'),
        isAnimating: false,
      };
    }
    
    // Default to 'ok' status if online and no errors/syncing
    // We can add more detailed tooltip text based on last sync time.
    let tooltipText = t('sync.synchronized');
    if (lastSyncTimestamp) {
        const timeDiff = Date.now() - lastSyncTimestamp;
        const minutes = Math.floor(timeDiff / 60000);
        if (minutes < 1) {
            tooltipText = t('sync.synchronized');
        } else if (minutes < 60) {
            tooltipText = t('sync.syncedMinutesAgo', { minutes });
        } else {
            const hours = Math.floor(minutes / 60);
            tooltipText = t('sync.syncedHoursAgo', { hours });
        }
    }

    return {
      iconName: 'sync-ok',
      color: theme.successColor,
      tooltip: tooltipText,
      isAnimating: false,
    };
  };

  const { iconName, color, tooltip, isAnimating } = getStatus();

  return (
    <StatusIconWrapper
      title={tooltip}
      $color={color}
      $isAnimating={isAnimating}
    >
      <AppIcon name={iconName} size={24} />
    </StatusIconWrapper>
  );
};

export default SyncStatusIndicator;