// src/pages/PlayerPage.tsx

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled, { keyframes, css } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore, SessionScript } from '../store/useAppStore';
import { getAuth } from 'firebase/auth';
// Removed direct react-icons/fi imports
import AppIcon from '../components/AppIcon'; // Import AppIcon
import { ttsPlay, ttsStop, TTSConfig, TTSProvider } from '../services/tts';
import type { SessionAudioConfig } from '../models';

// --- Helper Functions and Styled Components ---
const isFullScreen = () => document.fullscreenElement != null;
const requestFullScreen = (element: HTMLElement) => {
  if (element.requestFullscreen) element.requestFullscreen().catch(err => console.error("Fullscreen error:", err));
  else if ((element as any).mozRequestFullScreen) (element as any).mozRequestFullScreen();
  else if ((element as any).webkitRequestFullscreen) (element as any).webkitRequestFullscreen();
  else if ((element as any).msRequestFullscreen) (element as any).msRequestFullscreen();
};
const exitFullScreen = () => {
  if (document.exitFullscreen) document.exitFullscreen().catch(err => console.error("Exit fullscreen error:", err));
  else if ((document as any).mozCancelFullScreen) (document as any).mozCancelFullScreen();
  else if ((document as any).webkitExitFullscreen) (document as any).webkitExitFullscreen();
  else if ((document as any).msExitFullscreen) (document as any).msExitFullscreen();
};

const relaxingGradient = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const PlayerPageContainer = styled.div<{ $isPlaying?: boolean }>`
  display: flex; flex-direction: column; align-items: center; justify-content: center;
  height: 100vh; width: 100vw; padding: 1rem; text-align: center; color: #ffffff;
  position: fixed; top: 0; left: 0; overflow: hidden; z-index: 2000;
  background: ${({ theme, $isPlaying }) =>
    $isPlaying
      ? `linear-gradient(-45deg, ${theme.accent || '#B084CC'}, ${theme.primary || '#8A63D2'}, #23a6d5, #23d5ab)`
      : theme.background
  };
  background-size: ${({ $isPlaying }) => $isPlaying ? '400% 400%' : 'cover'};
  animation: ${({ $isPlaying }) => $isPlaying ? css`${relaxingGradient} 18s ease infinite` : 'none'};
  transition: background 0.5s ease-in-out;
`;
const ContentOverlay = styled.div`
  position: relative; z-index: 1; display: flex; flex-direction: column;
  align-items: center; justify-content: space-around; height: 100%;
  width: 100%; max-width: 800px; padding: 2rem 1rem;
  background: rgba(0, 0, 0, 0.2); border-radius: 10px;
`;
const TopControls = styled.div`
  position: absolute; top: 1.5rem; left: 1.5rem; right: 1.5rem;
  display: flex; justify-content: space-between; align-items: center; z-index: 10;
`;
const BackButtonPlayer = styled.button`
  background: rgba(255,255,255,0.15); color: white; border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%; width: auto; padding: 0 1rem; height: 44px; font-size: 0.9rem;
  display: flex; align-items: center; justify-content: center; cursor: pointer;
  transition: background-color 0.2s; gap: 0.5rem;
  &:hover { background: rgba(255,255,255,0.25); }
`;
const FullscreenButton = styled.button`
  background: rgba(255,255,255,0.15); color: white; border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%; width: 44px; height: 44px; font-size: 1.3rem;
  display: flex; align-items: center; justify-content: center; cursor: pointer;
  transition: background-color 0.2s;
  &:hover { background: rgba(255,255,255,0.25); }
`;
const SessionTitle = styled.h1`
  font-size: 2rem; margin-bottom: 0.5rem; text-shadow: 0 2px 5px rgba(0,0,0,0.5);
  color: ${({ theme }) => theme.textLight || '#f0f0f0'};
`;
const CurrentScriptText = styled.p`
  font-size: 1.4rem; font-weight: 300; line-height: 1.9; margin: 1rem 0; padding: 1rem;
  min-height: 150px; max-width: 90%; color: ${({ theme }) => theme.textLight || '#e0e0e0'};
  text-shadow: 0 1px 3px rgba(0,0,0,0.4); overflow-y: auto; max-height: 40vh;
`;
const ControlsContainer = styled.div`
  display: flex; flex-direction: column; align-items: center; gap: 1rem; margin-top: 1rem;
`;
const MainControls = styled.div`
  display: flex; gap: 1.5rem; align-items: center;
  button {
    background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.4);
    border-radius: 50%; width: 70px; height: 70px; font-size: 2.2rem; display: flex;
    align-items: center; justify-content: center; cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2); transition: background-color 0.2s, transform 0.1s;
    &:hover { background: rgba(255,255,255,0.3); }
    &:active { transform: scale(0.95); }
    &.restart-button {
      width: 50px; height: 50px; font-size: 1.5rem; background: rgba(255,255,255,0.15);
       &:hover { background: rgba(255,255,255,0.25); }
    }
  }
`;
const VolumePopupButton = styled.button`
  background: rgba(255,255,255,0.15); color: #e0e0e0; border: 1px solid rgba(255,255,255,0.3);
  border-radius: 20px; padding: 0.6rem 1rem; font-size: 0.9rem; cursor: pointer;
  display: flex; align-items: center; gap: 0.5rem; box-shadow: 0 2px 6px rgba(0,0,0,0.1);
   &:hover { background: rgba(255,255,255,0.25); }
`;
const VolumeControlPanel = styled.div<{ $show?: boolean }>`
  position: fixed; bottom: 100px; left: 50%; transform: translateX(-50%);
  background: ${({ theme }) => theme.surface}; color: ${({ theme }) => theme.text};
  border-radius: 12px; padding: 1.5rem; box-shadow: 0 0px 20px rgba(0,0,0,0.2);
  z-index: 15; width: 300px; max-width: 90vw;
  opacity: ${({ $show }) => $show ? 1 : 0};
  visibility: ${({ $show }) => $show ? 'visible' : 'hidden'};
  transition: opacity 0.3s, visibility 0s linear ${({ $show }) => $show ? '0s' : '0.3s'};
  border: 1px solid ${({ theme }) => theme.border};
  h4 {
    margin-top: 0; margin-bottom: 1rem; font-size: 1.1rem;
    color: ${({ theme }) => theme.primary}; text-align: left;
  }
`;
const VolumeSliderGroup = styled.div`
  margin-bottom: 1rem; text-align: left;
  &:last-child { margin-bottom: 0; }
  label {
    display: flex; align-items: center; gap: 0.5rem; font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary}; margin-bottom: 0.3rem;
  }
  input[type="range"] { width: 100%; }
`;
const LoadingMessage = styled.div`
  display: flex; flex-direction: column; align-items: center; justify-content: center;
  padding: 3rem; text-align: center; color: ${({ theme }) => theme.textLight || '#fff'};
  svg { font-size: 2.5rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;
const ErrorMessageStyled = styled.div`
  display: flex; flex-direction: column; align-items: center; justify-content: center;
  padding: 2rem; text-align: center; min-height: 50vh;
  color: ${({ theme }) => theme.textLight || '#fff'};
  p { color: ${({ theme }) => theme.errorColor || '#ff6b6b'}; font-size: 1.1rem; margin-bottom: 1rem; }
  a { color: ${({ theme }) => theme.textLight || '#fff'}; text-decoration: underline; font-weight: 500; &:hover { opacity: 0.8; } }
`;

const PlayerPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  const uid = getAuth().currentUser?.uid || '';
  const addCompletedSession = useAppStore(state => state.addCompletedSession);
  const addActivityTime = useAppStore(state => state.addActivityTime);
  const preferences = useAppStore(state => state.preferences);
  const audioConfig = useAppStore(state => state.sessionAudioConfig);
  const allScripts = useAppStore(state => state.sessions.scripts);
  const setAudioConfig = useAppStore(state => state.setSessionAudioConfig);

  const playerPageRef = useRef<HTMLDivElement>(null);
  const volumeControlPanelRef = useRef<HTMLDivElement>(null);
  const musicAudioRef = useRef<HTMLAudioElement>(null);
  const ambientAudioRef = useRef<HTMLAudioElement>(null);
  const ttsAbortController = useRef<AbortController | null>(null);
  const scriptPlayerTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});
  const mountedRef = useRef(true);
  const completionDispatchedRef = useRef(false);
  const sessionStartTimeRef = useRef<number | null>(null);

  const [session, setSession] = useState<SessionScript | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentScriptIndex, setCurrentScriptIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVolumeControls, setShowVolumeControls] = useState(false);
  const [isInFullScreen, setIsInFullScreen] = useState(isFullScreen());

  const { provider: ttsProvider, voice: ttsVoice, lang: ttsLang } = preferences?.ttsConfig || { provider: 'browser', voice: 'auto', lang: 'en' };
  const { enableMusic, music, enableAmbient, ambient, enableBinaural, binaural, voice } = audioConfig || {};

  const updateNestedConfig = <K extends keyof SessionAudioConfig>(key: K, nestedUpdates: Partial<NonNullable<SessionAudioConfig[K]>>) => {
    if (audioConfig) {
        const currentMainValue = audioConfig[key];
        const newMainValue = typeof currentMainValue === 'object' && currentMainValue !== null
            ? { ...currentMainValue, ...nestedUpdates }
            : nestedUpdates;
        setAudioConfig({ ...audioConfig, [key]: newMainValue });
    }
  };

  useEffect(() => {
    if (!sessionId || !allScripts) return;
    let foundSession: SessionScript | null = null;
    for (const category in allScripts) {
      const found = allScripts[category].find(s => s.id === sessionId);
      if (found) { foundSession = found; break; }
    }
    if (foundSession) {
      setSession(foundSession);
      setIsLoading(false);
    } else {
      setError(t('errors.cantLoadSession', 'Could not load session data. Please return to the sessions list.'));
      setIsLoading(false);
    }
  }, [sessionId, allScripts, t]);
  
  const toggleFullScreen = useCallback(() => {
    if (!playerPageRef.current) return;
    if (isFullScreen()) exitFullScreen();
    else requestFullScreen(playerPageRef.current);
  }, []);

  useEffect(() => {
    const handleChange = () => setIsInFullScreen(isFullScreen());
    document.addEventListener('fullscreenchange', handleChange);
    return () => document.removeEventListener('fullscreenchange', handleChange);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showVolumeControls && volumeControlPanelRef.current && !volumeControlPanelRef.current.contains(event.target as Node)) {
        setShowVolumeControls(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showVolumeControls]);
  
  const stopBinauralSound = useCallback(() => {
    if (audioCtxRef.current) {
      try {
        binauralOscs.current.left?.stop();
        binauralOscs.current.right?.stop();
        if (audioCtxRef.current.state !== 'closed') audioCtxRef.current.close().catch(() => {});
      } catch (e) {}
      audioCtxRef.current = null;
      binauralOscs.current = {};
    }
  }, []);

  const playBinauralSound = useCallback(() => {
    if (audioCtxRef.current?.state === 'running' || !binaural || typeof binaural.volume !== 'number' || typeof binaural.baseFreq !== 'number' || typeof binaural.beatFreq !== 'number') return;
    stopBinauralSound();
    const Ctx = window.AudioContext || (window as any).webkitAudioContext;
    if (!Ctx) return;
    const newCtx = new Ctx();
    audioCtxRef.current = newCtx;
    const gainNode = newCtx.createGain();
    gainNode.gain.setValueAtTime(binaural.volume, newCtx.currentTime);
    gainNode.connect(newCtx.destination);
    const left = newCtx.createOscillator();
    const right = newCtx.createOscillator();
    left.type = right.type = 'sine';
    left.frequency.setValueAtTime(binaural.baseFreq, newCtx.currentTime);
    right.frequency.setValueAtTime(binaural.baseFreq + binaural.beatFreq, newCtx.currentTime);
    const merger = newCtx.createChannelMerger(2);
    left.connect(merger, 0, 0); right.connect(merger, 0, 1);
    merger.connect(gainNode);
    left.start(); right.start();
    binauralOscs.current = { left, right, gain: gainNode };
  }, [binaural, stopBinauralSound]);
  
  const updateBinauralLiveParameters = useCallback(() => {
    if (audioCtxRef.current?.state === 'running' && binaural && typeof binaural.volume === 'number' && typeof binaural.baseFreq === 'number' && typeof binaural.beatFreq === 'number') {
      if (binauralOscs.current.gain) {
        binauralOscs.current.gain.gain.setValueAtTime(binaural.volume, audioCtxRef.current.currentTime);
      }
      if (binauralOscs.current.left && binauralOscs.current.right) {
        const now = audioCtxRef.current.currentTime;
        binauralOscs.current.left.frequency.setValueAtTime(binaural.baseFreq, now);
        binauralOscs.current.right.frequency.setValueAtTime(binaural.baseFreq + binaural.beatFreq, now);
      }
    }
  }, [binaural]);

  useEffect(() => {
    const scriptFinished = session?.script && currentScriptIndex >= session.script.length;

    if (scriptFinished && isPlaying && !completionDispatchedRef.current) {
      if (sessionId && uid) {
        addCompletedSession(uid, sessionId);
        if (sessionStartTimeRef.current !== null) {
          const durationSeconds = (Date.now() - sessionStartTimeRef.current) / 1000;
          const durationMinutes = Math.max(1, Math.round(durationSeconds / 60));
          addActivityTime(uid, durationMinutes);
          sessionStartTimeRef.current = null;
        }
      }
      completionDispatchedRef.current = true;
      setIsPlaying(false);
      ttsStop(ttsAbortController.current);
      return;
    }

    if (!isPlaying || !session?.script || scriptFinished) {
      if (!isPlaying && sessionStartTimeRef.current !== null) {
        const durationSeconds = (Date.now() - sessionStartTimeRef.current) / 1000;
        const durationMinutes = Math.max(0, Math.round(durationSeconds / 60));
        if (durationMinutes > 0) {
            addActivityTime(uid, durationMinutes);
        }
        sessionStartTimeRef.current = null;
      }
      return;
    }
    
    if (isPlaying && sessionStartTimeRef.current === null) {
        sessionStartTimeRef.current = Date.now();
    }

    let isCancelled = false;

    const playLine = async () => {
      if (isCancelled || !session.script) return;

      const currentLine = session.script[currentScriptIndex];
      ttsAbortController.current = new AbortController();
      const ttsConfig: TTSConfig = {
        volume: voice?.volume ?? 1.0,
        signal: ttsAbortController.current.signal,
        rate: currentLine.rate,
        pitch: currentLine.pitch,
      };

      try {
        await ttsPlay(ttsProvider, currentLine.text, ttsVoice, ttsLang, ttsConfig);
        if (isCancelled) return;
        scriptPlayerTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current && isPlaying && !isCancelled) {
            setCurrentScriptIndex(prev => prev + 1);
          }
        }, currentLine.pause || 1000);
      } catch (err) {
        // 'AbortError' is expected when speech is intentionally stopped.
        // We only log errors that are not AbortError.
        if (!(err instanceof Error && err.name === 'AbortError')) {
          console.error("TTS Playback Error:", err);
        }
      }
    };

    playLine();
      
    return () => {
      isCancelled = true;
      ttsStop(ttsAbortController.current);
      if (scriptPlayerTimeoutRef.current) {
        clearTimeout(scriptPlayerTimeoutRef.current);
      }
    };
  }, [isPlaying, currentScriptIndex, session, voice, ttsProvider, ttsVoice, ttsLang, sessionId, uid, addCompletedSession, addActivityTime]);
  
  useEffect(() => {
    const manageAudioFile = (audioElement: HTMLAudioElement | null, url: string | undefined, volume: number | undefined, enabled: boolean | undefined) => {
      if (!audioElement) return;
      if (enabled && url && typeof volume === 'number') {
        if (!audioElement.src || !audioElement.src.endsWith(url.split('/').pop()!)) {
            audioElement.src = url;
            audioElement.load();
        }
        audioElement.volume = volume;
        if (isPlaying) audioElement.play().catch(()=>{});
        else audioElement.pause();
      } else {
        audioElement.pause();
      }
    };
    manageAudioFile(musicAudioRef.current, music?.url, music?.volume, enableMusic);
    manageAudioFile(ambientAudioRef.current, ambient?.url, ambient?.volume, enableAmbient);
    if (enableBinaural) {
      if (isPlaying) playBinauralSound();
      else stopBinauralSound();
    } else {
      stopBinauralSound();
    }
  }, [isPlaying, music, enableMusic, ambient, enableAmbient, enableBinaural, playBinauralSound, stopBinauralSound]);
  
  useEffect(() => { if (musicAudioRef.current && typeof music?.volume === 'number') musicAudioRef.current.volume = music.volume; }, [music?.volume]);
  useEffect(() => { if (ambientAudioRef.current && typeof ambient?.volume === 'number') ambientAudioRef.current.volume = ambient.volume; }, [ambient?.volume]);
  useEffect(updateBinauralLiveParameters, [updateBinauralLiveParameters]);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      ttsStop(ttsAbortController.current);
      if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);
      musicAudioRef.current?.pause();
      ambientAudioRef.current?.pause();
      stopBinauralSound();
      if (isFullScreen()) exitFullScreen();
      if (uid && sessionStartTimeRef.current !== null) {
        const durationSeconds = (Date.now() - sessionStartTimeRef.current) / 1000;
        const durationMinutes = Math.max(0, Math.round(durationSeconds / 60));
        if (durationMinutes > 0) {
            addActivityTime(uid, durationMinutes);
        }
      }
    };
  }, [stopBinauralSound, uid, addActivityTime]);

  const handlePlayPause = () => {
    if (!session?.script?.length) return;
    setIsPlaying(prev => !prev);
  };

  const handleRestart = () => {
    ttsStop(ttsAbortController.current);
    setCurrentScriptIndex(0);
    setIsPlaying(false);
    completionDispatchedRef.current = false;
    sessionStartTimeRef.current = null;
  };

  if (isLoading) return <PlayerPageContainer><LoadingMessage><AppIcon name="loader" /> {t('loading.session', 'Loading...')}</LoadingMessage></PlayerPageContainer>;
  if (error) return <PlayerPageContainer><ErrorMessageStyled><p>{error}</p> <Link to="/sessions">{t('actions.backToSessions', 'Back to sessions')}</Link></ErrorMessageStyled></PlayerPageContainer>;
  if (!session) return <PlayerPageContainer><ErrorMessageStyled><p>{t('errors.sessionNotFound', 'Session not found.')}</p> <Link to="/sessions">{t('actions.backToSessions', 'Back to sessions')}</Link></ErrorMessageStyled></PlayerPageContainer>;

  const isScriptEnded = currentScriptIndex >= (session.script?.length || 0);
  const currentLineText = session.script && !isScriptEnded ? session.script[currentScriptIndex].text : (isScriptEnded ? t('player.sessionEnded', "Session ended.") : t('player.readyToStart', "Ready to start..."));
  const playPauseButtonTitle = isPlaying && !isScriptEnded ? t('actions.pause', "Pause") : t('actions.play', "Play");
  const playPauseButtonIcon = isPlaying && !isScriptEnded ? <AppIcon name="pause" /> : <AppIcon name="play" />;

  return (
    <PlayerPageContainer ref={playerPageRef} $isPlaying={isPlaying && !isScriptEnded}>
      <ContentOverlay>
        <TopControls>
          <BackButtonPlayer onClick={() => navigate(`/sessions/${sessionId}`)} title={t('actions.backToSessionDetails', "Back to session details")}>
            <AppIcon name="chevron-left" />
          </BackButtonPlayer>
          <FullscreenButton onClick={toggleFullScreen} title={isInFullScreen ? t('actions.exitFullscreen', "Exit fullscreen") : t('actions.enterFullscreen', "Enter fullscreen")}>
            {isInFullScreen ? <AppIcon name="minimize" /> : <AppIcon name="maximize" />}
          </FullscreenButton>
        </TopControls>
        <SessionTitle>{session.title}</SessionTitle>
        <CurrentScriptText>{currentLineText}</CurrentScriptText>
        <ControlsContainer>
          <VolumePopupButton onClick={() => setShowVolumeControls(prev => !prev)}>
            <AppIcon name="settings" /> {t('player.audioSettings', 'Audio Settings')}
          </VolumePopupButton>
          <VolumeControlPanel $show={showVolumeControls} ref={volumeControlPanelRef}>
            <h4>{t('player.volumeControls', 'Volume Controls')}</h4>
            {enableMusic && (
              <VolumeSliderGroup>
                <label><AppIcon name="music" /> {t('player.music', 'Music')}: {Math.round((music?.volume ?? 0) * 100)}%</label>
                <input type="range" min="0" max="1" step="0.01" value={music?.volume ?? 0.5} onChange={(e) => updateNestedConfig('music', { volume: Number(e.target.value) })} />
              </VolumeSliderGroup>
            )}
            {enableAmbient && (
              <VolumeSliderGroup>
                <label><AppIcon name="radio" /> {t('player.ambient', 'Ambient')}: {Math.round((ambient?.volume ?? 0) * 100)}%</label>
                <input type="range" min="0" max="1" step="0.01" value={ambient?.volume ?? 0.3} onChange={(e) => updateNestedConfig('ambient', { volume: Number(e.target.value) })} />
              </VolumeSliderGroup>
            )}
            {enableBinaural && (
              <VolumeSliderGroup>
                <label><AppIcon name="volume-2" /> {t('player.binaural', 'Binaural')}: {Math.round((binaural?.volume ?? 0) * 100)}%</label>
                <input type="range" min="0" max="1" step="0.01" value={binaural?.volume ?? 0.2} onChange={(e) => updateNestedConfig('binaural', { volume: Number(e.target.value) })} />
              </VolumeSliderGroup>
            )}
            <VolumeSliderGroup>
              <label><AppIcon name="message-circle" /> {t('player.voice', 'Voice')}: {Math.round((voice?.volume ?? 0) * 100)}%</label>
              <input type="range" min="0" max="1" step="0.01" value={voice?.volume ?? 1.0} onChange={(e) => updateNestedConfig('voice', { volume: Number(e.target.value) })} />
            </VolumeSliderGroup>
          </VolumeControlPanel>
          <MainControls>
            <button className="restart-button" onClick={handleRestart} title={t('actions.restart', "Restart")}>
              <AppIcon name="refresh-cw" />
            </button>
            <button onClick={handlePlayPause} title={playPauseButtonTitle} disabled={isScriptEnded}>
              {playPauseButtonIcon}
            </button>
          </MainControls>
        </ControlsContainer>
      </ContentOverlay>
      <audio ref={musicAudioRef} loop />
      <audio ref={ambientAudioRef} loop />
    </PlayerPageContainer>
  );
};

export default PlayerPage;