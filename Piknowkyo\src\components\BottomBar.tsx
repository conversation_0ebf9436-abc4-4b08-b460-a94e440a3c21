import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { FiHome, FiBook, FiMessageSquare, FiSettings } from 'react-icons/fi';
import { FaGamepad } from 'react-icons/fa';

const Bar = styled.nav`
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 64px;
  background: ${({ theme }) => theme.surface};
  border-top: 1px solid ${({ theme }) => theme.border};
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
`;

interface NavItemProps {
  $active: boolean;
}

const NavItem = styled(Link).withConfig({ shouldForwardProp: (prop) => prop !== '$active' })<NavItemProps>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: ${({ theme, $active }) => $active ? theme.primary : theme.textSecondary};
  text-decoration: none;
  font-size: 0.7rem;
  font-weight: ${({ $active }) => $active ? '500' : '400'};
  height: 100%;
  width: 20%;
  transition: all 0.2s ease;
  position: relative;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: ${({ $active }) => $active ? '30px' : '0'};
    height: 3px;
    background: ${({ theme }) => theme.primary};
    border-radius: 0 0 3px 3px;
    transition: width 0.3s ease;
  }

  &:hover {
    color: ${({ theme }) => theme.primary};
  }

  svg {
    margin-bottom: 4px;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateY(-2px);
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
`;

const Label = styled.span`
  font-size: 0.7rem;
  transition: all 0.2s ease;
`;

const BottomBar: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  return (
    <Bar>
      <NavItem to="/" $active={location.pathname === '/'}>
        <IconWrapper>
          <FiHome size={22} />
        </IconWrapper>
        <Label>{t('navigation.home')}</Label>
      </NavItem>
      <NavItem to="/sessions" $active={location.pathname.startsWith('/sessions')}>
        <IconWrapper>
          <FiBook size={22} />
        </IconWrapper>
        <Label>{t('navigation.sessions')}</Label>
      </NavItem>
      <NavItem to="/games" $active={location.pathname.startsWith('/games')}>
        <IconWrapper>
          <FaGamepad size={22} />
        </IconWrapper>
        <Label>{t('navigation.games')}</Label>
      </NavItem>
      <NavItem to="/journal" $active={location.pathname.startsWith('/journal')}>
        <IconWrapper>
          <FiMessageSquare size={22} />
        </IconWrapper>
        <Label>{t('navigation.journal')}</Label>
      </NavItem>

    </Bar>
  );
};

export default BottomBar;
