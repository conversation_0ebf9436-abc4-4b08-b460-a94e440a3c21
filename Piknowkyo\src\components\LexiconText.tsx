// src/components/LexiconText.tsx

import React, { useMemo } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store/useAppStore';
import { useShallow } from 'zustand/react/shallow';
import { DictionaryElement } from '../types/definitions';

// Styled component for the clickable lexicon term
const ClickableTerm = styled.span`
  color: ${({ theme }) => theme.primary}; /* Or any distinctive color */
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
  &:hover {
    opacity: 0.8;
  }
`;

interface LexiconTextProps {
  text: string;
}

/**
 * A component that renders a given text string, identifying and making lexicon terms clickable.
 * When a lexicon term is clicked, it opens the definition modal.
 */
const LexiconText: React.FC<LexiconTextProps> = ({ text }) => {
  const { i18n } = useTranslation();
  const currentLang = i18n.language as 'en' | 'fr' | 'es';

  // Efficiently get dictionaryMap and showDefinitionInModal action from Zustand
  const { dictionaryMap, showDefinitionInModal } = useAppStore(useShallow(state => ({
    dictionaryMap: state.definitions.dictionaryMap,
    showDefinitionInModal: state.showDefinitionInModal,
  })));

  // Memoize the parsed text to avoid re-parsing on every render
  const parsedText = useMemo(() => {
    if (!text || dictionaryMap.size === 0) {
      return [<React.Fragment key="initial">{text}</React.Fragment>];
    }

    const segments: React.ReactNode[] = [];
    let currentProcessingText = text; // Use a mutable copy for processing
    let currentAbsoluteIndex = 0; // Keep track of the absolute index in the original text

    // Convert dictionaryMap to an array of elements for easier iteration
    const lexiconEntries = Array.from(dictionaryMap.values());

    // Sort entries by length (longest first) to avoid matching substrings of longer terms
    // e.g., match "stress management" before "stress"
    lexiconEntries.sort((a, b) => {
        const nameA = a.name[currentLang] || a.key;
        const nameB = b.name[currentLang] || b.key;
        return nameB.length - nameA.length;
    });

    while (currentProcessingText.length > 0) {
      let foundMatch = false;
      let closestMatchRelativeIndex = -1;
      let matchedTermKey: string | null = null;
      let matchedTermContent: string = ''; // The actual string matched (e.g., "stress" or "Stress")

      for (const entry of lexiconEntries) {
        const termName = entry.name[currentLang];
        let regexPattern: RegExp | null = null;

        // Try to use regex_keywords if available and valid for the current language
        if (entry.regex_keywords && entry.regex_keywords[currentLang]) {
          try {
            const patternString = entry.regex_keywords[currentLang];
            const actualPattern = patternString.startsWith('/') && patternString.endsWith('/i') 
                                ? patternString.slice(1, -2) : patternString;
            regexPattern = new RegExp(`\\b(${actualPattern})\\b`, 'i'); // \b for word boundary
          } catch (e) {
            console.warn(`Invalid regex pattern for lexicon key ${entry.key} (${currentLang}):`, entry.regex_keywords[currentLang]);
            regexPattern = null;
          }
        }

        let match: RegExpExecArray | null = null;
        if (regexPattern) {
          match = regexPattern.exec(currentProcessingText);
        } else {
          // Fallback to simple indexOf if no valid regex_keywords
          const nameIndex = currentProcessingText.toLowerCase().indexOf(termName.toLowerCase());
          if (nameIndex !== -1) {
            // Manually create a RegExpExecArray-like object for consistency
            const matchedSubstr = currentProcessingText.substring(nameIndex, nameIndex + termName.length);
            match = [matchedSubstr] as RegExpExecArray; // Cast as RegExpExecArray
            match.index = nameIndex;
            match.input = currentProcessingText;
          }
        }
        
        if (match && (closestMatchRelativeIndex === -1 || match.index < closestMatchRelativeIndex)) {
          closestMatchRelativeIndex = match.index;
          matchedTermKey = entry.key;
          matchedTermContent = match[0]; // The actual string that was matched by regex or indexOf
          foundMatch = true;
        }
      }

      if (foundMatch && matchedTermKey !== null && closestMatchRelativeIndex !== -1) {
        // Add the text before the match
        if (closestMatchRelativeIndex > 0) {
          segments.push(
            <React.Fragment key={`text-${currentAbsoluteIndex}`}>{currentProcessingText.substring(0, closestMatchRelativeIndex)}</React.Fragment>
          );
        }

        // Add the clickable term
        segments.push(
          <ClickableTerm 
            key={`term-${currentAbsoluteIndex + closestMatchRelativeIndex}-${matchedTermKey}`} 
            onClick={() => showDefinitionInModal(matchedTermKey!)}
          >
            {matchedTermContent}
          </ClickableTerm>
        );

        // Update currentProcessingText to be the remainder after the matched term
        currentProcessingText = currentProcessingText.substring(closestMatchRelativeIndex + matchedTermContent.length);
        currentAbsoluteIndex += closestMatchRelativeIndex + matchedTermContent.length;
      } else {
        // No more matches found, add the rest of the text and break
        segments.push(
          <React.Fragment key={`remaining-${currentAbsoluteIndex}`}>{currentProcessingText}</React.Fragment>
        );
        currentProcessingText = '';
      }
    }

    return segments;
  }, [text, dictionaryMap, currentLang, showDefinitionInModal]);

  return <>{parsedText}</>;
};

export default LexiconText;