// src/components/DefinitionModal.tsx

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store/useAppStore';
import ReusableModal from './ReusableModal';
import AppIcon from './AppIcon';
import { DictionaryElement } from '../types/definitions';

// --- STYLED COMPONENTS (Unchanged) ---

const TTSButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${({ theme }) => theme.textMuted};
  padding: 0;
  margin-left: 0.75rem;
  display: inline-flex;
  align-items: center;
  transition: color 0.2s ease;

  &:hover {
    color: ${({ theme }) => theme.primary};
  }
`;

const RelatedConcepts = styled.div`
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.border};

  h4 {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    text-transform: uppercase;
    margin-bottom: 0.75rem;
  }
`;

const ConceptGrid = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const ConceptTag = styled.button`
  background: ${({ theme }) => theme.surfaceAlt};
  border: 1px solid ${({ theme }) => theme.border};
  color: ${({ theme }) => theme.primary};
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border-color: ${({ theme }) => theme.primary};
  }
`;

// --- COMPONENT IMPLEMENTATION ---

const DefinitionModal: React.FC = () => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language as 'en' | 'fr' | 'es';

  // --- THE FIX: Select each piece of state individually ---
  // This is the recommended way to select multiple values and avoid re-renders.
  const definitionKey = useAppStore(state => state.definitionModal.definitionKey);
  const hideDefinitionModal = useAppStore(state => state.hideDefinitionModal);
  const showDefinitionInModal = useAppStore(state => state.showDefinitionInModal);
  const dictionaryMap = useAppStore(state => state.definitions.dictionaryMap);
  const ttsConfig = useAppStore(state => state.preferences?.ttsConfig);

  // Local state for TTS functionality
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [definition, setDefinition] = useState<DictionaryElement | null>(null);

  // Stop speech when the modal closes or component unmounts
  useEffect(() => {
    return () => {
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Update the definition content when the key changes
  useEffect(() => {
    if (definitionKey) {
      // Always stop previous speech before showing a new definition
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
      }
      setDefinition(dictionaryMap.get(definitionKey) || null);
    }
  }, [definitionKey, dictionaryMap]);

  const handleToggleSpeech = useCallback(() => {
    if (!definition || !ttsConfig) return;

    if (isSpeaking) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
    } else {
      const name = definition.name[currentLang];
      const description = definition.description[currentLang];
      const textToRead = `${name}. ${description}`;
      
      const utterance = new SpeechSynthesisUtterance(textToRead);
      utterance.lang = ttsConfig.lang;
      utterance.volume = ttsConfig.volume;
      
      // Find and set the specific voice if available
      // Make sure voices are loaded before trying to use them
      let voices = window.speechSynthesis.getVoices();
      if (voices.length === 0) {
        window.speechSynthesis.onvoiceschanged = () => {
          voices = window.speechSynthesis.getVoices();
          const selectedVoice = voices.find(v => v.name === ttsConfig.voice);
          if (selectedVoice) {
            utterance.voice = selectedVoice;
          }
          window.speechSynthesis.speak(utterance);
        };
      } else {
        const selectedVoice = voices.find(v => v.name === ttsConfig.voice);
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
        window.speechSynthesis.speak(utterance);
      }
      
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);
      
      setIsSpeaking(true);
    }
  }, [isSpeaking, definition, ttsConfig, currentLang]);

  const handleConceptClick = (key: string) => {
    showDefinitionInModal(key);
  };
  
  if (!definitionKey || !definition) {
    return null;
  }

  const relatedItems = [
    ...(definition.composition || []),
    ...(definition.related_energetic_system || [])
  ];

  return (
    <ReusableModal
      isOpen={!!definitionKey}
      onClose={hideDefinitionModal}
      title={definition.name[currentLang]}
      titleIcon={
        <TTSButton onClick={handleToggleSpeech} aria-label={isSpeaking ? 'Stop reading' : 'Read definition'}>
          <AppIcon name={isSpeaking ? 'stop-circle' : 'volume-2'} size={22} />
        </TTSButton>
      }
    >
      <p>{definition.description[currentLang]}</p>
      
      {relatedItems.length > 0 && (
        <RelatedConcepts>
          <h4>{t('lexicon.relatedConcepts', 'Related Concepts')}</h4>
          <ConceptGrid>
            {relatedItems.map(itemKey => {
              const key = itemKey.split(':')[1];
              const relatedDef = dictionaryMap.get(key);
              if (!relatedDef) return null;
              
              return (
                <ConceptTag key={key} onClick={() => handleConceptClick(key)}>
                  {relatedDef.name[currentLang]}
                </ConceptTag>
              );
            })}
          </ConceptGrid>
        </RelatedConcepts>
      )}
    </ReusableModal>
  );
};

export default DefinitionModal;