// pmng/src/App.tsx
import { useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import type { DefaultTheme } from 'styled-components';
import { onAuthStateChanged, signOut } from 'firebase/auth'; // MODIFIED: Imported signOut
import type { User } from 'firebase/auth';
import { auth } from './firebase';
import { TranslationProvider } from './hooks/useTranslation';

// Components and Pages
import MainLayout from './components/MainLayout';
import LoginPage from './LoginPage';
import UserManagementPage from './pages/UserManagementPage';
import SessionManagementPage from './pages/SessionManagementPage';
import AISettingsPage from './pages/AISettingsPage';
import GlobalStyle from './styles/GlobalStyle';
import TranslationManagementPage from './pages/TranslationManagementPage';
import ScriptGenerationPage from './pages/ScriptGenerationPage';

const adminTheme: DefaultTheme = {
  name: 'admin',
  primary: '#007bff',
  secondary: '#6c757d',
  success: '#28a745',
  danger: '#dc3545',
  background: '#f4f7f9',
  surface: '#ffffff',
  border: '#dee2e6',
  text: '#212529',
  textSecondary: '#6c757d',
  accent: '#007bff',
  textLight: '#fff',
  cardShadow: '0 2px 4px rgba(0,0,0,0.1)',
  headerShadow: '0 2px 4px rgba(0,0,0,0.1)',
  borderSlight: '#e9ecef',
  shadowSmall: '0 1px 2px rgba(0,0,0,0.05)',
  inputBackground: '#fff',
  errorColor: '#dc3545',
  successColor: '#28a745',
  disabledBackground: '#e9ecef',
  disabledText: '#adb5bd',
  tableHeaderBackground: '#e9ecef',
  tableRowEvenBackground: '#f8f9fa',
  tableRowHoverBackground: '#e0e0e0',
  tableBorder: '#dee2e6',
};

// NEW: Get the admin email from environment variables
const ADMIN_EMAIL = import.meta.env.VITE_ADMIN_EMAIL;
if (!ADMIN_EMAIL) {
  throw new Error("VITE_ADMIN_EMAIL is not defined in your .env file. Please set it to the authorized admin email.");
}

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // MODIFIED: Added authorization logic inside onAuthStateChanged
    const unsubscribe = onAuthStateChanged(auth, (currentUser: User | null) => {
      if (currentUser) {
        // Check if the logged-in user's email matches the allowed admin email
        if (currentUser.email === ADMIN_EMAIL) {
          setUser(currentUser); // Authorized user
        } else {
          // User is logged in but not authorized for this admin panel
          console.warn(`Access denied for ${currentUser.email}. This email is not the configured admin.`);
          alert("Access Denied: This email address is not authorized to access the admin panel.");
          signOut(auth); // Sign out the unauthorized user
          setUser(null);
        }
      } else {
        // No user is logged in
        setUser(null);
      }
      setIsLoading(false);
    });
    return () => unsubscribe();
  }, []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <TranslationProvider>
      <ThemeProvider theme={adminTheme}>
        <GlobalStyle />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={user ? <Navigate to="/" /> : <LoginPage />} />
            <Route path="/" element={user ? <MainLayout user={user} /> : <Navigate to="/login" />}>
              <Route index element={<h1>Welcome Admin!</h1>} />
              <Route path="users" element={<UserManagementPage />} />
              <Route path="sessions" element={<SessionManagementPage />} />
              <Route path="translations" element={<TranslationManagementPage />} />
              <Route path="ai-settings" element={<AISettingsPage />} />
              <Route path="script-generation" element={<ScriptGenerationPage />} />
            </Route>
            <Route path="*" element={<Navigate to="/" />} />
          </Routes>
        </BrowserRouter>
      </ThemeProvider>
    </TranslationProvider>
  );
}

export default App;