// src/lib/conversationFlow.ts

import { Dictionary, DictionaryElement } from '../types/definitions';

type TFunction = (key: string, options?: any) => string;

export type ConversationAnswer = {
  stepId: string;
  key: string;
  type: 'basic_emotion' | 'somatic_sensation' | 'cognitive_pattern' | 'desired_outcome';
};

interface ConversationStep {
  getQuestion: (t: TFunction, history: ConversationAnswer[]) => string;
  getOptions: (dictionary: Dictionary, lang: 'en' | 'fr' | 'es', t: TFunction) => (DictionaryElement & { label: string })[];
  handleAnswer: (answerKey: string, history: ConversationAnswer[]) => { nextStepId: string, savedAnswer: ConversationAnswer | null };
}

const IDK_OPTION_KEY = 'idk_or_other';

export const conversationGraph: Record<string, ConversationStep | null> = {
  start: {
    getQuestion: (t) => t('recommendationAssistant.q_initial'),
    getOptions: (dictionary, lang, t) => [
      ...dictionary.basic_emotions.map(e => ({ ...e, label: e.name[lang] })),
      // FIX: Add an escape hatch option
      { key: IDK_OPTION_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk') }
    ],
    handleAnswer: (answerKey) => ({
      nextStepId: 'ask_sensation',
      savedAnswer: answerKey === IDK_OPTION_KEY ? null : { stepId: 'start', key: answerKey, type: 'basic_emotion' },
    }),
  },

  ask_sensation: {
    getQuestion: (t) => t('recommendationAssistant.q_sensation'),
    getOptions: (dictionary, lang, t) => [
      ...dictionary.somatic_sensations
        .filter(s => ['chest_oppression', 'knotted_stomach', 'shoulder_neck_tension', 'shallow_breathing', 'heaviness_limbs', 'constriction'].includes(s.key))
        .map(e => ({ ...e, label: e.name[lang] })),
      { key: 'pivot_cognitive', name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_pivot_cognitive') },
      // FIX: Add an escape hatch option
      { key: IDK_OPTION_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    handleAnswer: (answerKey) => {
      if (answerKey === 'pivot_cognitive') return { nextStepId: 'ask_cognitive', savedAnswer: null };
      if (answerKey === IDK_OPTION_KEY) return { nextStepId: 'ask_outcome', savedAnswer: null };
      return {
        nextStepId: 'ask_outcome',
        savedAnswer: { stepId: 'ask_sensation', key: answerKey, type: 'somatic_sensation' },
      };
    },
  },

  ask_cognitive: {
    getQuestion: (t) => t('recommendationAssistant.q_cognitive'),
    getOptions: (dictionary, lang, t) => [
      ...dictionary.cognitive_patterns
        .filter(p => ['mental_rumination', 'future_anxiety', 'critical_inner_dialogue', 'mental_fog'].includes(p.key))
        .map(e => ({ ...e, label: e.name[lang] })),
      { key: 'pivot_sensation', name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_pivot_sensation') },
      // FIX: Add an escape hatch option
      { key: IDK_OPTION_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    handleAnswer: (answerKey) => {
       if (answerKey === 'pivot_sensation') return { nextStepId: 'ask_sensation', savedAnswer: null };
       if (answerKey === IDK_OPTION_KEY) return { nextStepId: 'ask_outcome', savedAnswer: null };
       return {
        nextStepId: 'ask_outcome',
        savedAnswer: { stepId: 'ask_cognitive', key: answerKey, type: 'cognitive_pattern' },
      };
    },
  },

  ask_outcome: {
    getQuestion: (t) => t('recommendationAssistant.q_outcome'),
    getOptions: (dictionary, lang, t) => [
      ...dictionary.desired_outcomes
      .filter(o => ['deep_calm', 'letting_go', 'grounding', 'stress_reduction', 'emotional_resilience', 'energy_clearing', 'inner_peace'].includes(o.key))
      .map(e => ({ ...e, label: e.name[lang] })),
      // FIX: Add an escape hatch option
      { key: IDK_OPTION_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_surprise_me') }
    ],
    handleAnswer: (answerKey) => ({
      nextStepId: 'end_recommendation',
      savedAnswer: answerKey === IDK_OPTION_KEY ? null : { stepId: 'ask_outcome', key: answerKey, type: 'desired_outcome' },
    }),
  },

  end_recommendation: null,
};