// src/services/firestoreService.ts

import { doc, setDoc, addDoc, collection, serverTimestamp } from 'firebase/firestore';
import CryptoJS from 'crypto-js';
import { db } from '../firebase'; // Import our configured Firestore instance

// --- Environment Variable Check ---
const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
if (!SHARED_KEY) {
  throw new Error("VITE_SHARED_ENCRYPTION_KEY is not defined in the environment variables.");
}

/**
 * Encrypts and saves a document to a user's private collection.
 * The user's UID is used as the encryption key.
 * @param uid - The user's unique ID.
 * @param data - The JSON-serializable data to save.
 * @returns A Promise that resolves when the document is saved.
 */
export const savePrivateDoc = async (uid: string, data: any) => {
  try {
    // Encrypt the data using the user's UID as the key
    const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(data), uid).toString();

    // The document will have a new, auto-generated ID
    const docPayload = {
      encrypted: encryptedData,
      createdAt: serverTimestamp(), // Optional: track creation time
      owner: uid, // Optional: useful for security rules and queries
    };
    
    const collectionRef = collection(db, `users/${uid}/privateData`);
    const docRef = await addDoc(collectionRef, docPayload);
    
    console.log(`Private document saved with ID: ${docRef.id}`);
    return docRef;

  } catch (error) {
    console.error("Error saving private document:", error);
    throw new Error("Failed to save private document."); // Re-throw to handle it in the UI
  }
};

/**
 * Encrypts and saves a document to the shared 'ExampleCollection'.
 * The shared encryption key from environment variables is used.
 * @param data - The JSON-serializable data to save.
 * @returns A Promise that resolves when the document is saved.
 */
export const saveSharedDoc = async (data: any) => {
  try {
    // Encrypt the data using the shared key
    const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(data), SHARED_KEY).toString();
    
    // The document will have a new, auto-generated ID
    const docPayload = {
      encrypted: encryptedData,
      createdAt: serverTimestamp(), // Optional: track creation time
    };

    const collectionRef = collection(db, 'ExampleCollection');
    const docRef = await addDoc(collectionRef, docPayload);

    console.log(`Shared document saved with ID: ${docRef.id}`);
    return docRef;

  } catch (error) {
    console.error("Error saving shared document:", error);
    throw new Error("Failed to save shared document."); // Re-throw to handle it in the UI
  }
};

/**
 * Encrypts and saves/updates a document with a specific ID in a user's private collection.
 * @param uid - The user's unique ID.
 * @param docId - The specific ID of the document to save or update.
 * @param data - The JSON-serializable data to save.
 * @returns A Promise that resolves when the document is saved.
 */
export const setPrivateDoc = async (uid: string, docId: string, data: any) => {
    try {
      const encryptedData = CryptoJS.AES.encrypt(JSON.stringify(data), uid).toString();
  
      const docPayload = {
        encrypted: encryptedData,
        updatedAt: serverTimestamp(), // Use updatedAt for updates
        owner: uid,
      };
      
      const docRef = doc(db, `users/${uid}/privateData`, docId);
      await setDoc(docRef, docPayload, { merge: true }); // Use merge to avoid overwriting other fields
      
      console.log(`Private document with ID ${docId} was set/updated.`);
  
    } catch (error) {
      console.error(`Error setting private document ${docId}:`, error);
      throw new Error("Failed to set private document.");
    }
  };