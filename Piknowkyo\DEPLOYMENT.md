# Piknowkyo Deployment Guide

This document outlines the step-by-step process for deploying the Piknowkyo application to staging and production environments.

## Prerequisites
- Node.js v18+ installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Firebase project set up for staging and production
- Stripe account with API keys for both environments

## Environment Setup
1. Create environment files:
   - `.env.production` with production Firebase and Stripe credentials
   - `.env.staging` with staging Firebase and Stripe credentials
2. Install project dependencies:
   ```bash
   cd Piknowkyo
   npm install
   ```

## Deployment Process

### Staging Deployment
1. Run the deployment script:
   ```bash
   ./deploy.sh staging
   ```
2. Verify deployment at: https://piknowkyo-staging.web.app

### Production Deployment
1. Run the deployment script:
   ```bash
   ./deploy.sh production
   ```
2. Verify deployment at: https://piknowkyo-production.web.app

## Continuous Integration (CI/CD) Setup
For automated deployments, add the following steps to your CI pipeline:

```yaml
name: Deploy to Firebase

on:
  push:
    branches:
      - main
    paths:
      - 'Piknowkyo/**'

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          
      - name: Install dependencies
        run: |
          cd Piknowkyo
          npm install
          
      - name: Install Firebase CLI
        run: npm install -g firebase-tools
          
      - name: Deploy to Firebase
        run: ./deploy.sh ${{ github.event_name == 'push' && 'production' || 'staging' }}
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
```

## Verification
After deployment, verify:
1. Application loads without errors
2. Firebase services are properly connected
3. Stripe payment processing works
4. All features function as expected