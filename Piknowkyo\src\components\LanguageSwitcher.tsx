// src/components/LanguageSwitcher.tsx

import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { FiGlobe } from 'react-icons/fi';
// --- STEP 1: Import the context hook and language type ---
import { useLang, Language } from '../LangProvider';

const LanguageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const LanguageSelect = styled.select`
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
  }

  option {
    background: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.text};
  }
`;

const LanguageIcon = styled(FiGlobe)`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 1.1rem;
`;

const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();
  // --- STEP 2: Get the setLang function from our context ---
  const { setLang } = useLang();

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = event.target.value as Language; // Cast the value to our Language type
    // --- STEP 3: Use the setLang function instead of i18n.changeLanguage ---
    // This will correctly update the state in LangProvider, which will then handle
    // loading new translations and updating i18next.
    setLang(newLanguage);
  };

  return (
    <LanguageContainer>
      <LanguageIcon />
      {/* The value should still come from i18n.language as it's the most reactive source for the UI */}
      <LanguageSelect value={i18n.language} onChange={handleLanguageChange}>
        <option value="fr">{t('languages.french')}</option>
        <option value="en">{t('languages.english')}</option>
        <option value="es">{t('languages.spanish')}</option>
      </LanguageSelect>
    </LanguageContainer>
  );
};

export default LanguageSwitcher;