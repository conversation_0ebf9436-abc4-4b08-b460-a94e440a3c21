import localforage from 'localforage';
// CORRECTED: Import 'auth' and 'db' from the correct firebase setup file.
import { auth, db } from '../firebase'; 
import { collection, doc, getDocs, writeBatch } from 'firebase/firestore';
// CORRECTED: 'CompletedSession' is now imported from models.ts
import { CompletedSession } from '../models';

// Define a unique key for storing completed sessions in localForage
const COMPLETED_SESSIONS_KEY = 'piknowkyo_user_activity_completed_sessions';

/**
 * Retrieves all completed session records from the local database.
 * @returns A promise that resolves to an array of CompletedSession objects.
 */
export const getCompletedSessions = async (): Promise<CompletedSession[]> => {
  try {
    const sessions = await localforage.getItem<CompletedSession[]>(COMPLETED_SESSIONS_KEY);
    return sessions || [];
  } catch (error) {
    console.error("Error fetching completed sessions from localForage:", error);
    return [];
  }
};

/**
 * Adds a new completed session record to the local database.
 * @param completion - The CompletedSession object to add.
 */
export const addCompletedSession = async (completion: CompletedSession): Promise<void> => {
  try {
    const sessions = await getCompletedSessions();
    sessions.push(completion);
    await localforage.setItem(COMPLETED_SESSIONS_KEY, sessions);
  } catch (error) {
    console.error("Error adding completed session to localForage:", error);
  }
};

/**
 * Performs a two-way sync between the local database (localForage) and the remote (Firestore).
 * - Pushes local-only records to Firestore.
 * - Pulls remote-only records to localForage.
 */
export const syncCompletedSessions = async (): Promise<void> => {
  const user = auth.currentUser;

  if (!user) {
    console.log("Sync skipped: No user is authenticated.");
    return; // Cannot sync without a user
  }

  if (!navigator.onLine) {
    console.log("Sync skipped: Application is offline.");
    return; // Cannot sync offline
  }

  console.log("Starting user activity sync...");
  const batch = writeBatch(db);
  const remoteCollectionRef = collection(db, 'users', user.uid, 'completedSessions');

  try {
    // 1. Get both local and remote data concurrently
    const [localSessions, remoteSnapshot] = await Promise.all([
      getCompletedSessions(),
      getDocs(remoteCollectionRef)
    ]);

    const remoteSessions: CompletedSession[] = remoteSnapshot.docs.map(doc => doc.data() as CompletedSession);

    // Use Sets for efficient ID lookup
    const localSessionIds = new Set(localSessions.map(s => s.id));
    const remoteSessionIds = new Set(remoteSessions.map(s => s.id));

    // 2. Find local records that need to be pushed to remote
    localSessions.forEach(localSession => {
      if (!remoteSessionIds.has(localSession.id)) {
        console.log(`Syncing local completion to remote: ${localSession.sessionId} (${localSession.id})`);
        const remoteDocRef = doc(remoteCollectionRef, localSession.id);
        batch.set(remoteDocRef, localSession);
      }
    });

    // 3. Find remote records that need to be added locally
    const mergedSessions = [...localSessions];
    remoteSessions.forEach(remoteSession => {
      if (!localSessionIds.has(remoteSession.id)) {
        console.log(`Syncing remote completion to local: ${remoteSession.sessionId} (${remoteSession.id})`);
        mergedSessions.push(remoteSession);
      }
    });

    // 4. Commit all changes
    await batch.commit(); // Push new local records to Firestore
    await localforage.setItem(COMPLETED_SESSIONS_KEY, mergedSessions); // Save the fully synced list locally

    console.log("User activity sync completed successfully.");

  } catch (error) {
    console.error("An error occurred during user activity sync:", error);
    // Depending on your error handling strategy, you might want to re-throw the error
    // so the calling thunk can catch it and update the Redux state.
    throw error;
  }
};