// src/games/zen-tetris/styles.ts

import styled, { css, keyframes } from 'styled-components';

// Animation pour l'icône de rotation
const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// Dimensions du jeu
export const CELL_SIZE = 24; // Taille d'une cellule en pixels (servira de base pour next piece, etc.)
export const BOARD_ASPECT_RATIO = 10 / 20; // Largeur / Hauteur (10 colonnes / 20 rangées)

// Couleurs des pièces (utilisent les index de logic.ts)
// Mapper ces index vers des couleurs Hex qui s'accordent avec votre thème Zen
export const getPieceColor = (index: number, theme: any) => {
  switch (index) {
    case 1: return theme.zenTetrisPiece1 || '#2ecc71'; // I - vert clair
    case 2: return theme.zenTetrisPiece2 || '#3498db'; // J - bleu clair
    case 3: return theme.zenTetrisPiece3 || '#9b59b6'; // L - violet
    case 4: return theme.zenTetrisPiece4 || '#f1c40f'; // O - jaune
    case 5: return theme.zenTetrisPiece5 || '#e67e22'; // S - orange
    case 6: return theme.zenTetrisPiece6 || '#e74c3c'; // T - rouge
    case 7: return theme.zenTetrisPiece7 || '#1abc9c'; // Z - cyan
    default: return theme.zenTetrisBackgroundCell || '#34495e'; // Cellule vide
  }
};

export const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 100vh;
  background: radial-gradient(circle at center, ${({ theme }) => theme.background || '#1a1a2e'} 0%, ${({ theme }) => theme.surface || '#16213e'} 100%);
  color: ${({ theme }) => theme.textLight || 'white'};
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
  padding: 1rem;
  box-sizing: border-box;

  /* Hide scrollbars on mobile */
  -webkit-overflow-scrolling: touch;

  /* Responsive visibility classes */
  .desktop-only {
    display: flex;
    @media (max-width: 768px) {
      display: none;
    }
  }

  .mobile-only {
    display: none;
    @media (max-width: 768px) {
      display: flex;
    }
  }
`;

export const GameArea = styled.div`
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  max-width: 95vw;
  box-sizing: border-box;
  margin-bottom: 6rem; /* Space for mobile controls */

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    gap: 0.5rem;
    margin-bottom: 8rem; /* More space for mobile controls */
    max-width: 98vw;
  }

  @media (max-width: 480px) {
    padding: 0.5rem;
    margin-bottom: 9rem;
  }

  /* Landscape orientation on mobile */
  @media (max-width: 768px) and (orientation: landscape) {
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 6rem;
    padding: 0.5rem;
    gap: 1rem;
  }
`;

// Définir une interface pour les props de TetrisBoard
interface TetrisBoardProps {
  rows: number;
  cols: number;
}

// Appliquer l'interface au styled component
export const TetrisBoard = styled.div<TetrisBoardProps>`
  display: grid;
  grid-template-rows: repeat(${({ rows }) => rows}, 1fr);
  grid-template-columns: repeat(${({ cols }) => cols}, 1fr);

  width: min(70vw, 300px);
  max-height: calc(min(70vw, 300px) / ${BOARD_ASPECT_RATIO});
  aspect-ratio: ${BOARD_ASPECT_RATIO};

  border: 4px solid ${({ theme }) => theme.primary || '#8A63D2'};
  background-color: ${({ theme }) => theme.zenTetrisBoardBackground || '#34495e'};
  border-radius: 8px;
  overflow: hidden;

  /* Touch-friendly on mobile */
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  @media (max-width: 768px) {
    width: min(85vw, 280px);
    max-height: calc(min(85vw, 280px) / ${BOARD_ASPECT_RATIO});
    border-width: 3px;
  }

  @media (max-width: 480px) {
    width: min(90vw, 250px);
    max-height: calc(min(90vw, 250px) / ${BOARD_ASPECT_RATIO});
    border-width: 2px;
  }

  /* Landscape mode adjustments */
  @media (max-width: 768px) and (orientation: landscape) {
    width: min(50vh, 280px);
    max-height: calc(min(50vh, 280px) / ${BOARD_ASPECT_RATIO});
  }
`;

export const Cell = styled.div<{ $colorIndex: number }>`
  // Les cellules n'ont plus de taille fixe ici, elles s'adaptent au 1fr
  box-sizing: border-box;
  background-color: ${({ $colorIndex, theme }) => getPieceColor($colorIndex, theme)};
  border: 1px solid rgba(0, 0, 0, 0.2); // Sépateur de cellules
  box-shadow: inset 0 0 5px rgba(255,255,255,0.1), inset 0 0 2px rgba(0,0,0,0.2);
  transition: background-color 0.1s ease-out;
`;

export const SidePanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding-left: 1rem;
  color: ${({ theme }) => theme.textLight || 'white'};
  min-width: 150px;
  max-width: 200px;

  @media (max-width: 768px) {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 0;
    margin-top: 1rem;
    min-width: auto;
    max-width: auto;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  @media (max-width: 480px) {
    gap: 0.25rem;
    margin-top: 0.5rem;
  }
`;

export const NextPieceDisplay = styled.div`
  background-color: rgba(0, 0, 0, 0.4);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border || 'rgba(255,255,255,0.2)'};
  text-align: center;
  flex-shrink: 0; // Empêche de rétrécir sur mobile

  h4 {
    margin-top: 0;
    margin-bottom: 0.8rem;
    color: ${({ theme }) => theme.accent || '#B084CC'};
  }

  .preview-grid {
    display: grid;
    // Utilise CELL_SIZE pour la taille de la prévisualisation, qui reste fixe
    grid-template-rows: repeat(4, ${CELL_SIZE}px);
    grid-template-columns: repeat(4, ${CELL_SIZE}px);
    margin: 0 auto;
    width: ${CELL_SIZE * 4}px;
    height: ${CELL_SIZE * 4}px;
    // Centrer sur mobile si ça devient un élément de rangée
    @media (max-width: 768px) {
        margin: 0; /* Annule le margin auto qui ne fait pas de sens en flex-direction row */
        transform: scale(0.8); /* Rendre plus petit sur mobile si nécessaire */
        transform-origin: center center;
    }
  }

  @media (max-width: 768px) {
    padding: 0.5rem;
    flex-basis: 30%;
    font-size: 0.8rem;
    h4 { font-size: 0.9rem; margin-bottom: 0.3rem; }
  }

  @media (max-width: 480px) {
    padding: 0.3rem;
    flex-basis: 45%;
    font-size: 0.7rem;
    h4 { font-size: 0.8rem; margin-bottom: 0.2rem; }
  }
`;

export const InfoDisplay = styled.div`
  background-color: rgba(0, 0, 0, 0.4);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border || 'rgba(255,255,255,0.2)'};
  text-align: left;
  line-height: 1.6;
  flex-grow: 1; // Permet de prendre de l'espace disponible sur mobile

  h4 {
    margin-top: 0;
    margin-bottom: 0.8rem;
    color: ${({ theme }) => theme.accent || '#B084CC'};
  }

  p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
  }

  @media (max-width: 768px) {
    padding: 0.5rem;
    flex-basis: 30%;
    font-size: 0.8rem;
    h4 { font-size: 0.9rem; margin-bottom: 0.3rem; }
    p { font-size: 0.7rem; }
  }

  @media (max-width: 480px) {
    padding: 0.3rem;
    flex-basis: 45%;
    font-size: 0.7rem;
    h4 { font-size: 0.8rem; margin-bottom: 0.2rem; }
    p { font-size: 0.6rem; gap: 0.3rem; }
  }
`;

export const PauseButton = styled.button`
  position: absolute;
  top: 1.5rem; // Utiliser rem pour la position
  right: 1.5rem; // Utiliser rem
  background: rgba(0,0,0,0.4);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 1.3rem;
  transition: background-color 0.2s, transform 0.1s;
  &:hover {
    background: rgba(0,0,0,0.6);
    transform: scale(1.05);
  }
  &:active {
    transform: scale(0.95);
  }

  @media (max-width: 768px) {
    top: 0.5rem;
    right: 0.5rem;
    width: 36px; /* Plus petit sur mobile */
    height: 36px;
    font-size: 1rem;
  }
`;

export const MainControls = styled.div`
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 10;
  justify-content: center;

  button {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border: none;
    border-radius: 8px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 60px;
    height: 50px;

    &:hover {
      opacity: 0.9;
      transform: translateY(-2px);
    }
    &:active {
      transform: scale(0.98) translateY(0);
    }
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

export const GameOverOverlay = styled.div`
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 0 0 10px black;
`;

// Mobile Controls
export const MobileControls = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: ${({ theme }) => theme.surface};
  border-top: 2px solid ${({ theme }) => theme.border};
  box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
  z-index: 1000;
  backdrop-filter: blur(10px);

  .control-row {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    justify-content: center;
  }

  .control-row:first-child {
    gap: 3rem;
    max-width: 280px;
    width: 100%;
    justify-content: space-between;
  }

  @media (min-width: 769px) {
    display: none !important;
  }

  /* Landscape mode adjustments */
  @media (max-width: 768px) and (orientation: landscape) {
    position: fixed;
    bottom: 0;
    right: 0;
    left: auto;
    width: auto;
    flex-direction: row;
    gap: 1rem;
    padding: 0.5rem 1rem;
    border-top: none;
    border-left: 2px solid ${({ theme }) => theme.border};

    .control-row {
      flex-direction: column;
      gap: 0.75rem;
    }

    .control-row:first-child {
      gap: 0.75rem;
      max-width: none;
      width: auto;
      justify-content: center;
    }
  }
`;

export const ControlButton = styled.button`
  background: ${({ theme }) => theme.primary || '#8A63D2'};
  color: ${({ theme }) => theme.textLight || 'white'};
  border: 2px solid transparent;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.6rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, ${({ theme }) => theme.primary}, ${({ theme }) => theme.secondary || theme.primary});
    z-index: -1;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:active {
    transform: scale(0.92);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);

    &::before {
      opacity: 1;
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  }

  &.rotate-button {
    background: ${({ theme }) => theme.accent || '#B084CC'};
    width: 80px;
    height: 80px;
    font-size: 2rem;
    border: 3px solid ${({ theme }) => theme.surface};
  }

  &.drop-button {
    background: #2ecc71;
  }

  &.move-button {
    background: ${({ theme }) => theme.primary || '#8A63D2'};
  }

  @media (max-width: 480px) {
    width: 60px;
    height: 60px;
    font-size: 1.4rem;

    &.rotate-button {
      width: 70px;
      height: 70px;
      font-size: 1.8rem;
    }
  }

  @media (max-width: 768px) and (orientation: landscape) {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;

    &.rotate-button {
      width: 55px;
      height: 55px;
      font-size: 1.4rem;
    }
  }
`;