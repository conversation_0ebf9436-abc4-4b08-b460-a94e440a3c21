// src/services/tts.ts

import { getAuth, getIdToken } from 'firebase/auth';

export type TTSProvider = 
  | 'browser' 
  | 'cloud';

export interface TTSConfig {
  volume?: number;      // 0.0 to 1.0
  rate?: number;        // 0.1 to 10
  pitch?: number;       // 0 to 2
  signal?: AbortSignal; // To cancel speech
}

// --- Browser TTS Implementation ---
let browserVoicesCache: SpeechSynthesisVoice[] | null = null;
let currentUtterance: SpeechSynthesisUtterance | null = null;

export function getBrowserVoices(): Promise<SpeechSynthesisVoice[]> {
  return new Promise((resolve, reject) => {
    if (browserVoicesCache) {
      return resolve(browserVoicesCache);
    }
    if (typeof window.speechSynthesis === 'undefined') {
      return reject(new Error('Speech Synthesis not supported by this browser.'));
    }
    const getAndResolveVoices = () => {
      const voices = window.speechSynthesis.getVoices();
      if (voices.length > 0) {
        browserVoicesCache = voices;
        resolve(voices);
        return true;
      }
      return false;
    };
    if (getAndResolveVoices()) {
      return;
    }
    window.speechSynthesis.onvoiceschanged = () => {
      getAndResolveVoices();
    };
    setTimeout(() => {
      if (!getAndResolveVoices()) {
        reject(new Error('Failed to load browser voices in time.'));
      }
    }, 2000);
  });
}

export async function ttsBrowser(
  text: string, 
  voiceName: string,
  lang: string, 
  config: TTSConfig
): Promise<void> {
  return new Promise(async (resolve, reject) => {
    if (!window.speechSynthesis) {
      return reject(new Error("Web Speech API not supported."));
    }
    if (config.signal?.aborted) {
      return reject(new DOMException('Aborted', 'AbortError'));
    }
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
    }
    
    await new Promise(r => setTimeout(r, 50));

    const utter = new SpeechSynthesisUtterance(text);
    currentUtterance = utter;
    utter.lang = lang;
    utter.volume = config.volume ?? 1;
    utter.rate = config.rate ?? 1;
    utter.pitch = config.pitch ?? 1;

    try {
      const voices = await getBrowserVoices();
      let selectedVoice: SpeechSynthesisVoice | undefined;
      
      if (voiceName && voiceName !== 'auto') {
        selectedVoice = voices.find(v => v.name === voiceName);
        console.log(`[ttsBrowser] User requested voice: "${voiceName}". Found:`, selectedVoice ? selectedVoice.name : 'Not Found');
      }
      
      if (!selectedVoice) {
        selectedVoice = voices.find(v => v.lang === lang) || voices.find(v => v.lang.startsWith(lang));
        console.log(`[ttsBrowser] Auto-selected voice for lang "${lang}":`, selectedVoice ? selectedVoice.name : 'No suitable voice found');
      }

      if (selectedVoice) {
        utter.voice = selectedVoice;
      } else {
        console.warn(`[ttsBrowser] No voice found for name "${voiceName}" or language "${lang}". The browser will use its default.`);
      }
    } catch (error) {
      console.error("[ttsBrowser] Could not get browser voices for playback:", error);
    }
    
    const onEnd = () => {
      if (currentUtterance === utter) currentUtterance = null;
      resolve();
    };
    const onError = (event: SpeechSynthesisErrorEvent) => {
      if (currentUtterance === utter) currentUtterance = null;
      if (event.error !== 'canceled' && event.error !== 'interrupted') {
        console.error(`[ttsBrowser] SpeechSynthesisErrorEvent: ${event.error}`);
        reject(new Error(`TTS Error: ${event.error}`));
      } else {
        resolve();
      }
    };
    utter.onend = onEnd;
    utter.onerror = onError;

    const signalListener = () => {
      window.speechSynthesis.cancel();
      if (currentUtterance === utter) currentUtterance = null;
    };
    if (config.signal) {
      if (config.signal.aborted) {
        signalListener();
        return;
      }
      config.signal.addEventListener('abort', signalListener, { once: true });
      const cleanupSignalListener = () => config.signal?.removeEventListener('abort', signalListener);
      utter.onend = () => { cleanupSignalListener(); onEnd(); };
      utter.onerror = (event) => { cleanupSignalListener(); onError(event); };
    }
    
    window.speechSynthesis.speak(utter);
  });
}


// --- Cloud TTS Implementation ---
let currentCloudAudio: HTMLAudioElement | null = null;

export async function ttsCloud(text: string, voiceName: string, lang: string, config: TTSConfig): Promise<void> {
  return new Promise(async (resolve, reject) => {
    if (config.signal?.aborted) {
      return reject(new DOMException('Aborted', 'AbortError'));
    }

    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) {
      return reject(new Error("User not authenticated for Cloud TTS."));
    }

    const functionUrl = import.meta.env.VITE_FIREBASE_TTS_FUNCTION_URL;
    if (!functionUrl) {
      return reject(new Error("Firebase TTS function URL is not configured."));
    }

    try {
      const token = await getIdToken(user);
      
      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          text: text,
          lang: lang,
          voiceName: voiceName,
        }),
        signal: config.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Cloud TTS failed: ${response.status} ${errorText}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      const audio = new Audio(audioUrl);
      currentCloudAudio = audio;
      audio.volume = config.volume ?? 1;

      const cleanup = () => {
        URL.revokeObjectURL(audioUrl);
        if (currentCloudAudio === audio) {
          currentCloudAudio = null;
        }
      };

      audio.onended = () => {
        cleanup();
        resolve();
      };

      audio.onerror = (e) => {
        console.error("Cloud audio playback error:", e);
        cleanup();
        reject(new Error("Failed to play cloud audio."));
      };

      audio.play().catch(e => {
        cleanup();
        reject(e);
      });

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return resolve();
      }
      console.error("Error fetching Cloud TTS:", error);
      reject(error);
    }
  });
}

// --- Main Player & Stopper ---

export async function ttsPlay(
  provider: TTSProvider,
  text: string,
  voice: string,
  lang: string,
  config: TTSConfig = {}
): Promise<void> {
  ttsStop(config.signal ? null : undefined);

  switch (provider) {
    case 'cloud':
      if (voice === 'auto') {
        console.warn("Cloud TTS requested with 'auto' voice. This is not supported. Please select a specific voice.");
        return Promise.reject(new Error("Cloud TTS requires a specific voice, not 'auto'."));
      }
      return ttsCloud(text, voice, lang, config);
    case 'browser':
    default:
      return ttsBrowser(text, voice, lang, config);
  }
}

export function ttsStop(abortController?: AbortController | null): void {
  // Stop browser speech
  if (window.speechSynthesis && window.speechSynthesis.speaking) {
    window.speechSynthesis.cancel();
  }
  if (currentUtterance) {
    currentUtterance = null;
  }

  // Stop cloud audio playback
  if (currentCloudAudio) {
    // --- FIX ---
    // Detach event listeners to prevent the 'onerror' from firing on intentional stop.
    // This avoids a harmless error being reported as a playback failure.
    currentCloudAudio.onended = null;
    currentCloudAudio.onerror = null;
    
    currentCloudAudio.pause();
    currentCloudAudio.src = ''; // Detach the source to stop download if any
    currentCloudAudio = null;
  }

  // Abort any ongoing fetch request or process if a controller is passed
  if (abortController && !abortController.signal.aborted) {
    abortController.abort();
  }
}