// src/hooks/useUserPreferences.ts
import { useState, useEffect } from 'react';
import { doc, onSnapshot, updateDoc, DocumentData } from 'firebase/firestore';
import { db } from '../firebase';
import { useAuth } from './useAuth';

// Define interfaces for our Firestore documents for type safety
export interface AppPreferences {
  appLanguage: 'fr' | 'en' | 'es';
  appTheme: 'light' | 'dark';
  notificationsEnabled: boolean;
  favoriteSessions: string[];
}

export interface AudioPreferences {
  enableMusic: boolean;
  musicSelectionId: string;
  musicVolume: number;
  enableAmbient: boolean;
  ambientSelectionId: string;
  ambientVolume: number;
  enableBinaural: boolean;
  binauralConfig: {
    baseFreq: number;
    beatFreq: number;
    volume: number;
  };
  ttsConfig: {
    provider: 'browser' | 'cloud';
    voice: string;
    lang: string;
    volume: number;
  };
}

/**
 * Hook to manage user preferences from Firestore in real-time.
 * It handles fetching, updating, and offline persistence via Firestore's cache.
 */
export const useUserPreferences = () => {
  const { user } = useAuth();
  const [appPreferences, setAppPreferences] = useState<AppPreferences | null>(null);
  const [audioPreferences, setAudioPreferences] = useState<AudioPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    const userId = user.uid;
    setLoading(true);

    const appPrefsRef = doc(db, 'users', userId, 'preferences', 'app');
    const audioPrefsRef = doc(db, 'users', userId, 'preferences', 'audio');

    // Listener for App Preferences
    const unsubscribeApp = onSnapshot(appPrefsRef, (docSnap) => {
      if (docSnap.exists()) {
        setAppPreferences(docSnap.data() as AppPreferences);
      } else {
        console.warn(`App preferences document does not exist for user ${userId}. It will be created on first login.`);
      }
      // Consider loading complete even if one is missing, as they might be created at different times.
      if (audioPreferences !== undefined) setLoading(false);
    }, (err) => {
      console.error("Error fetching app preferences:", err);
      setError(err);
      setLoading(false);
    });

    // Listener for Audio Preferences
    const unsubscribeAudio = onSnapshot(audioPrefsRef, (docSnap) => {
      if (docSnap.exists()) {
        setAudioPreferences(docSnap.data() as AudioPreferences);
      } else {
        console.warn(`Audio preferences document does not exist for user ${userId}.`);
      }
      if (appPreferences !== undefined) setLoading(false);
    }, (err) => {
      console.error("Error fetching audio preferences:", err);
      setError(err);
      setLoading(false);
    });

    // Cleanup listeners on component unmount or user change
    return () => {
      unsubscribeApp();
      unsubscribeAudio();
    };
  }, [user]);

  // Function to update app preferences
  const updateAppPreferences = async (data: Partial<AppPreferences>) => {
    if (!user) throw new Error("User not authenticated to update preferences.");
    const appPrefsRef = doc(db, 'users', user.uid, 'preferences', 'app');
    try {
      await updateDoc(appPrefsRef, data);
    } catch (err) {
      console.error("Failed to update app preferences:", err);
      throw err;
    }
  };

  // Function to update audio preferences
  const updateAudioPreferences = async (data: Partial<AudioPreferences> | { 'ttsConfig.provider': string, 'ttsConfig.voice': string, 'ttsConfig.lang': string }) => {
    if (!user) throw new Error("User not authenticated to update preferences.");
    const audioPrefsRef = doc(db, 'users', user.uid, 'preferences', 'audio');
    try {
      // The `data` argument here can accept dot notation for nested objects.
      await updateDoc(audioPrefsRef, data as DocumentData);
    } catch (err) {
      console.error("Failed to update audio preferences:", err);
      throw err;
    }
  };

  return {
    appPreferences,
    audioPreferences,
    loading,
    error,
    updateAppPreferences,
    updateAudioPreferences,
  };
};