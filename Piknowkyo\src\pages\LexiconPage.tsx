// src/pages/LexiconPage.tsx

import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store/useAppStore';
import { DictionaryElement } from '../types/definitions';
import AppIcon from '../components/AppIcon';
import { Dictionary } from '../types/definitions';

// --- STYLED COMPONENTS (Unchanged) ---

const PageContainer = styled.div`
  padding: 1rem 1.5rem 3rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 2rem;
  h1 {
    font-size: 2.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.5rem;
  }
  p {
    font-size: 1.1rem;
    color: ${({ theme }) => theme.textSecondary};
    max-width: 600px;
    margin: 0 auto;
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  position: sticky;
  top: 60px; /* Adjust based on your header height */
  background: ${({ theme }) => theme.background};
  padding: 1rem 0;
  z-index: 10;
`;

const SearchInputContainer = styled.div`
  position: relative;
  input {
    width: 100%;
    padding: 0.8rem 1rem 0.8rem 2.5rem;
    border-radius: 25px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
    font-size: 1rem;
    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.primary};
      box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}30;
    }
  }
  .search-icon {
    position: absolute;
    left: 0.8rem;
    top: 50%;
    transform: translateY(-50%);
    color: ${({ theme }) => theme.textMuted};
  }
`;

const CategoryFilter = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
`;

const CategoryButton = styled.button<{ $isActive: boolean }>`
  padding: 0.5rem 1.2rem;
  border-radius: 20px;
  border: 1px solid ${({ theme, $isActive }) => $isActive ? theme.primary : theme.border};
  background: ${({ theme, $isActive }) => $isActive ? theme.primary : 'transparent'};
  color: ${({ theme, $isActive }) => $isActive ? theme.textLight : theme.text};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    background: ${({ theme, $isActive }) => !$isActive && theme.surfaceAlt};
    border-color: ${({ theme }) => theme.primary};
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
`;

const DefinitionCard = styled.button`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  text-align: left;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  color: ${({ theme }) => theme.text};

  h3 {
    font-size: 1.25rem;
    margin: 0 0 0.5rem 0;
    color: ${({ theme }) => theme.primary};
  }

  p {
    font-size: 0.95rem;
    color: ${({ theme }) => theme.textSecondary};
    margin: 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow};
    border-color: ${({ theme }) => theme.primary};
  }
`;

const NoResults = styled.div`
  text-align: center;
  padding: 3rem;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 1.1rem;
`;

// --- COMPONENT IMPLEMENTATION ---

const LexiconPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language as 'en' | 'fr' | 'es';

  // --- FIX 1: Select state slices individually to prevent infinite loops ---
  const dictionary = useAppStore(state => state.definitions.dictionary);
  const showDefinitionInModal = useAppStore(state => state.showDefinitionInModal);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = useMemo(() => {
    if (!dictionary) return [];
    // Filter out any non-array properties like comments
    return Object.keys(dictionary).filter(key => Array.isArray(dictionary[key as keyof Dictionary]));
  }, [dictionary]);

  const filteredAndSortedDefinitions = useMemo(() => {
    if (!dictionary) return [];

    let items: DictionaryElement[] = [];

    if (selectedCategory === 'all') {
      // --- FIX 2: Safely get all definition items ---
      // We filter to ensure we only process arrays, preventing crashes from comments or other properties.
      items = Object.values(dictionary)
        .filter(value => Array.isArray(value))
        .flat();
    } else {
      const categoryItems = dictionary[selectedCategory as keyof typeof dictionary];
      if (Array.isArray(categoryItems)) {
        items = categoryItems;
      }
    }

    if (searchTerm) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      items = items.filter(item => 
        item.name[currentLang].toLowerCase().includes(lowerCaseSearchTerm) ||
        item.description[currentLang].toLowerCase().includes(lowerCaseSearchTerm)
      );
    }
    
    // Sort items alphabetically by name in the current language
    return items.sort((a, b) => a.name[currentLang].localeCompare(b.name[currentLang]));

  }, [dictionary, selectedCategory, searchTerm, currentLang]);
  
  return (
    <PageContainer>
      <Header>
        <h1>{t('lexicon.title', 'Lexicon')}</h1>
        <p>{t('lexicon.subtitle', 'Explore the concepts, emotions, and techniques of our approach.')}</p>
      </Header>

      <ControlsContainer>
        <SearchInputContainer>
          <AppIcon name="search" size={20} className="search-icon" />
          <input 
            type="search"
            placeholder={t('lexicon.searchPlaceholder', 'Search for a term...')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchInputContainer>
        <CategoryFilter>
          <CategoryButton $isActive={selectedCategory === 'all'} onClick={() => setSelectedCategory('all')}>
            {t('lexicon.allCategories', 'All')}
          </CategoryButton>
          {categories.map(catKey => (
            <CategoryButton 
              key={catKey}
              $isActive={selectedCategory === catKey} 
              onClick={() => setSelectedCategory(catKey)}
            >
              {t(`lexicon.categories.${catKey}`, catKey.replace(/_/g, ' '))}
            </CategoryButton>
          ))}
        </CategoryFilter>
      </ControlsContainer>
      
      {filteredAndSortedDefinitions && filteredAndSortedDefinitions.length > 0 ? (
        <Grid>
          {filteredAndSortedDefinitions.map(item => (
            <DefinitionCard key={item.key} onClick={() => showDefinitionInModal(item.key)}>
              <h3>{item.name[currentLang]}</h3>
              <p>{item.description[currentLang]}</p>
            </DefinitionCard>
          ))}
        </Grid>
      ) : (
        <NoResults>
          <p>{t('lexicon.noResults', 'No definitions found. Try adjusting your search or filter.')}</p>
        </NoResults>
      )}
    </PageContainer>
  );
};

export default LexiconPage;