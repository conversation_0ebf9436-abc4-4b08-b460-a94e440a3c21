// src/games/common/models.ts

import React from 'react';

// --- Interfaces for the page GamesPage ---
export interface GameInfo {
  id: string; // Unique game ID (e.g., "zen-tetris")
  titleKey: string; // i18next key for the game title
  descriptionKey: string; // i18next key for the game description
  component: React.FC<any>; // The main React component for the game
  maxLevels: number; // Total levels for this game
  estimatedDurationMinutes: number; // Estimated duration of a session (optional)
  tags?: string[]; // Keywords for the game (optional)
  icon: string; // The string name of the icon for AppIcon
}

// --- Interfaces for props passed to a standard GameComponent ---
export interface GameProps {
  userId: string;
  onGameEnd: (score: number, levelReached: number, finalTimeSeconds: number) => void;
  onGameQuit: () => void;
  onPauseChange: (isPaused: boolean) => void;
  initialGameState?: SavedGameState | null; // Can be null
}

// --- Interfaces for saving progress ---
export interface SavedGameState {
  score: number;
  level: number;
  currentTimerSeconds: number;
  specificGameState: any; // Game-specific data (e.g., Tetris grid state)
  lastPlayed: Date;
}

/**
 * Interface to store a user's personal best score for a specific game.
 */
export interface PersonalBestGameScore {
  score: number;
  level: number;
  // If you want to store the timestamp when the best score was achieved, uncomment the line below:
  timestamp: Date; 
}

// --- Other game-related utility interfaces ---