// src/pages/AudioAssetsConfigPage.tsx

import React, { useState, useEffect, useRef, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { getStorage, ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';

// --- NEW IMPORTS ---
import { useAppStore } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import { storage as firebaseStorage } from '../firebase';
import { AudioAsset } from '../store/useAppStore';
import ReusableModal from '../components/ReusableModal';
import PremiumGate from '../components/PremiumGate';

// --- ICONS ---
import { FiChevronLeft, FiMusic, FiRadio, FiUpload, FiPlay, FiTrash2, FiLoader, FiAlertCircle, FiSquare } from 'react-icons/fi';

// --- STYLED COMPONENTS ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const BackButton = styled.button`
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.primary};
  border: 1px solid ${({ theme }) => theme.borderSlight};
  border-radius: 50%;
  width: 44px;
  height: 44px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 1.5rem;
  box-shadow: ${({ theme }) => theme.shadowSmall};
  transition: background-color 0.2s, color 0.2s, transform 0.1s;

  &:hover {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    transform: scale(1.05);
  }
  &:focus {
    outline: 2px solid ${({ theme }) => theme.primary};
    outline-offset: 2px;
  }
`;

const PageTitle = styled.h1`
  font-size: 2.2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  svg {
      opacity: 0.8;
  }
`;

const AssetSection = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem;
  padding: 1.5rem;
`;

const SectionHeader = styled.h2`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.75rem;
  svg {
    color: ${({ theme }) => theme.primary};
  }
`;

const AssetList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 1.5rem;
`;

const AssetItem = styled.li`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 10px;
  padding: 0.8rem 1.2rem;
  margin-bottom: 0.8rem;
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
  border: 1px solid ${({ theme }) => theme.border};

  &:last-child {
    margin-bottom: 0;
  }
`;

const AssetName = styled.span`
  flex-grow: 1;
  margin-right: 1rem;
  color: ${({ theme }) => theme.text};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  word-break: break-word;
  svg {
      color: ${({theme}) => theme.accent || theme.primary};
      flex-shrink: 0;
  }
`;

const AssetActions = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' }>`
  background: ${({ theme, $variant }) => 
    $variant === 'danger' ? (theme.errorColor || '#d9534f') : 
    $variant === 'secondary' ? (theme.surfaceAlt || '#e9ecef') : 
    (theme.primary || '#007bff')
  };
  color: ${({ theme, $variant }) => 
    $variant === 'secondary' ? (theme.textSecondary || '#495057') : (theme.textLight || '#fff')
  };
  border: ${({ theme, $variant }) => $variant === 'secondary' ? `1px solid ${theme.border || '#ced4da'}` : 'none'};
  border-radius: 6px;
  padding: 0.5rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s, background-color 0.2s, color 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  min-width: 36px;

  &:hover { opacity: 0.85; }
  &:disabled { 
    background: ${({ theme }) => theme.disabled || '#ccc'} !important;
    color: ${({ theme }) => theme.textMuted || '#6c757d'} !important;
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const UploadForm = styled.div`
  border-top: 1px dashed ${({ theme }) => theme.border};
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  text-align: center;

  p {
    color: ${({ theme }) => theme.textSecondary};
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  input[type="file"] {
    display: none;
  }

  label.custom-file-upload {
    background: ${({ theme }) => theme.secondary || theme.accent};
    color: ${({ theme }) => theme.textLight};
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    transition: opacity 0.2s;

    &:hover { opacity: 0.9; }
  }

  .file-name {
    margin-top: 0.8rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.text};
    font-weight: 500;
    word-break: break-all;
  }

  .upload-status {
    margin-top: 0.8rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textMuted};
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    progress {
      flex-grow: 1;
      max-width: 200px;
      height: 8px;
      border-radius: 4px;
      &::-webkit-progress-bar { background-color: ${({ theme }) => theme.surfaceAlt}; border-radius: 4px; }
      &::-webkit-progress-value { background-color: ${({ theme }) => theme.primary}; border-radius: 4px; }
      &::-moz-progress-bar { background-color: ${({ theme }) => theme.primary}; border-radius: 4px; }
    }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};
  min-height: 50vh;

  svg {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'}; 
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => `${theme.errorColor}1A` || '#ff00001A'};
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'};
  border-radius: 8px;
  p {
    margin: 0;
  }
`;


const AudioAssetsConfigPage: React.FC = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const theme = useContext(ThemeContext) as DefaultTheme;

    const { user } = useAuth();
    const audioAssets = useAppStore((state) => state.audioAssets);
    const subscription = useAppStore((state) => state.subscription);
    const addAudioAsset = useAppStore((state) => state.addAudioAsset);
    const deleteAudioAsset = useAppStore((state) => state.deleteAudioAsset);

    const [musicFileToUpload, setMusicFileToUpload] = useState<File | null>(null);
    const [ambientFileToUpload, setAmbientFileToUpload] = useState<File | null>(null);
    const [uploadingMusic, setUploadingMusic] = useState(false);
    const [uploadingAmbient, setUploadingAmbient] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [currentlyPlayingUrl, setCurrentlyPlayingUrl] = useState<string | null>(null);
    const currentPlayingAudio = useRef<HTMLAudioElement | null>(null);
    const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
    const [assetToDelete, setAssetToDelete] = useState<{ asset: AudioAsset; type: 'musics' | 'ambiants' } | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const musics = audioAssets?.musics ?? [];
    const ambiants = audioAssets?.ambiants ?? [];
    const isLoading = !audioAssets || !subscription;

    useEffect(() => {
        return () => {
            if (currentPlayingAudio.current) {
                currentPlayingAudio.current.pause();
                currentPlayingAudio.current = null;
            }
        };
    }, []);

    const handlePreview = (url: string) => {
        if (currentPlayingAudio.current && currentlyPlayingUrl === url) {
            currentPlayingAudio.current.pause();
            setCurrentlyPlayingUrl(null);
        } else {
            if (currentPlayingAudio.current) currentPlayingAudio.current.pause();
            const newAudio = new Audio(url);
            currentPlayingAudio.current = newAudio;
            newAudio.play().catch(e => console.error("Audio play error:", e));
            setCurrentlyPlayingUrl(url);
            newAudio.onended = () => setCurrentlyPlayingUrl(null);
        }
    };

    const handleUpload = async (type: 'musics' | 'ambiants', file: File | null) => {
        if (!file || !user) {
            alert(t('errors.userNotAuthenticated', "You must be logged in to upload files."));
            return;
        }

        if (type === 'musics') setUploadingMusic(true);
        else setUploadingAmbient(true);

        const assetId = uuidv4();
        const storagePath = `users/${user.uid}/${type}/${assetId}-${file.name}`;
        const storageRef = ref(firebaseStorage, storagePath);
        const uploadTask = uploadBytesResumable(storageRef, file);

        uploadTask.on('state_changed',
            (snapshot) => {
                const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                setUploadProgress(progress);
            },
            (error) => {
                console.error("Upload failed:", error);
                alert(t('audioAssets.uploadError', `Error during upload: ${error.message}`));
                if (type === 'musics') setUploadingMusic(false); else setUploadingAmbient(false);
            },
            async () => {
                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                const newAsset: AudioAsset = { id: assetId, name: file.name, url: downloadURL, storagePath: storagePath, isUserUploaded: true };
                await addAudioAsset(user.uid, newAsset, type);
                if (type === 'musics') { setMusicFileToUpload(null); setUploadingMusic(false); }
                else { setAmbientFileToUpload(null); setUploadingAmbient(false); }
            }
        );
    };

    const requestDeleteUserAsset = (asset: AudioAsset, type: 'musics' | 'ambiants') => {
        if (!asset.isUserUploaded || !asset.storagePath) {
            alert(t('audioAssets.cannotDeleteDefault', "Default files cannot be deleted."));
            return;
        }
        setAssetToDelete({ asset, type });
        setShowDeleteConfirmModal(true);
    };
    
    const executeDelete = async () => {
        if (!assetToDelete || !user) return;
        const { asset, type } = assetToDelete;

        setIsDeleting(true);
        try {
            const storageRef = ref(firebaseStorage, asset.storagePath);
            await deleteObject(storageRef);
            await deleteAudioAsset(user.uid, asset.id, type);
            alert(t('audioAssets.deleteSuccess', 'File deleted successfully!'));
        } catch (err) {
            console.error("Error deleting asset:", err);
            alert(t('audioAssets.deleteError', `Error during deletion: ${err instanceof Error ? err.message : String(err)}`));
        } finally {
            setIsDeleting(false);
            setShowDeleteConfirmModal(false);
            setAssetToDelete(null);
        }
    };
    
    if (isLoading) {
        return <LoadingContainer><FiLoader />{t('loading.audioAssets', 'Loading audio files...')}</LoadingContainer>;
    }

    const isActionInProgress = uploadingMusic || uploadingAmbient || isDeleting;

    return (
        <PageContainer>
            <BackButton onClick={() => navigate('/settings')} title={t('actions.backToSettings', "Back to settings") || "Back"}>
                <FiChevronLeft />
            </BackButton>

            <PageTitle><FiMusic style={{ marginRight: '0.5rem' }} /> {t('audioAssets.title', 'Manage My Audio Files')}</PageTitle>

            <AssetSection>
                <SectionHeader><FiMusic /> {t('audioAssets.musicTitle', 'Background Music')}</SectionHeader>
                <AssetList>
                    {musics.map(asset => (
                        <AssetItem key={asset.id}>
                            <AssetName><FiMusic /> {asset.name}</AssetName>
                            <AssetActions>
                                <ActionButton onClick={() => handlePreview(asset.url)} disabled={isActionInProgress} title={currentlyPlayingUrl === asset.url ? t('actions.stopPreview', 'Stop') : t('actions.preview', 'Preview')}>
                                    {currentlyPlayingUrl === asset.url ? <FiSquare /> : <FiPlay />}
                                </ActionButton>
                                {asset.isUserUploaded && (
                                    <ActionButton $variant="danger" onClick={() => requestDeleteUserAsset(asset, 'musics')} disabled={isActionInProgress || (currentlyPlayingUrl === asset.url)} title={t('actions.delete', 'Delete') || "Delete"}>
                                        <FiTrash2 />
                                    </ActionButton>
                                )}
                            </AssetActions>
                        </AssetItem>
                    ))}
                </AssetList>
                <PremiumGate feature="customAudio" subscription={subscription} context="audio-asset" title={t('premium.features.custom.titleInList', 'Upload Custom Audio')}>
                    <UploadForm>
                        <p>{t('audioAssets.uploadMusicPrompt', 'Add your own background music (MP3 format, max 10MB):')}</p>
                        <label htmlFor="music-file-upload" className="custom-file-upload"><FiUpload /> {musicFileToUpload ? t('audioAssets.changeFile', 'Change file') : t('audioAssets.selectFile', 'Select a file')}</label>
                        <input id="music-file-upload" type="file" accept="audio/mpeg,audio/mp3" onChange={(e) => setMusicFileToUpload(e.target.files ? e.target.files[0] : null)} disabled={isActionInProgress}/>
                        {musicFileToUpload && <div className="file-name">{musicFileToUpload.name}</div>}
                        {uploadingMusic && <div className="upload-status"><FiLoader style={{animation: 'spin 1s linear infinite'}} /> {t('audioAssets.uploading', 'Uploading...')} <progress value={uploadProgress} max="100"></progress></div>}
                        {!uploadingMusic && musicFileToUpload && <ActionButton onClick={() => handleUpload('musics', musicFileToUpload)} disabled={uploadingAmbient || isDeleting} style={{marginTop: '1rem'}}><FiUpload /> {t('actions.upload', 'Upload')}</ActionButton>}
                    </UploadForm>
                </PremiumGate>
            </AssetSection>

            <AssetSection>
                <SectionHeader><FiRadio /> {t('audioAssets.ambientTitle', 'Ambient Sounds')}</SectionHeader>
                <AssetList>
                    {ambiants.map(asset => (
                         <AssetItem key={asset.id}>
                            <AssetName><FiRadio /> {asset.name}</AssetName>
                            <AssetActions>
                                <ActionButton onClick={() => handlePreview(asset.url)} disabled={isActionInProgress} title={currentlyPlayingUrl === asset.url ? t('actions.stopPreview', 'Stop') : t('actions.preview', 'Preview')}>
                                    {currentlyPlayingUrl === asset.url ? <FiSquare /> : <FiPlay />}
                                </ActionButton>
                                {asset.isUserUploaded && (
                                    <ActionButton $variant="danger" onClick={() => requestDeleteUserAsset(asset, 'ambiants')} disabled={isActionInProgress || (currentlyPlayingUrl === asset.url)} title={t('actions.delete', 'Delete') || "Delete"}>
                                        <FiTrash2 />
                                    </ActionButton>
                                )}
                            </AssetActions>
                        </AssetItem>
                    ))}
                </AssetList>
                <PremiumGate feature="customAudio" subscription={subscription} context="audio-asset" title={t('premium.features.custom.titleInList', 'Upload Custom Audio')}>
                    <UploadForm>
                        <p>{t('audioAssets.uploadAmbientPrompt', 'Add your own ambient sound (MP3 format, max 5MB):')}</p>
                        <label htmlFor="ambient-file-upload" className="custom-file-upload"><FiUpload /> {ambientFileToUpload ? t('audioAssets.changeFile', 'Change file') : t('audioAssets.selectFile', 'Select a file')}</label>
                        <input id="ambient-file-upload" type="file" accept="audio/mpeg,audio/mp3" onChange={(e) => setAmbientFileToUpload(e.target.files ? e.target.files[0] : null)} disabled={isActionInProgress} />
                        {ambientFileToUpload && <div className="file-name">{ambientFileToUpload.name}</div>}
                        {uploadingAmbient && <div className="upload-status"><FiLoader style={{animation: 'spin 1s linear infinite'}} /> {t('audioAssets.uploading', 'Uploading...')} <progress value={uploadProgress} max="100"></progress></div>}
                        {!uploadingAmbient && ambientFileToUpload && <ActionButton onClick={() => handleUpload('ambiants', ambientFileToUpload)} disabled={uploadingMusic || isDeleting} style={{marginTop: '1rem'}}><FiUpload /> {t('actions.upload', 'Upload')}</ActionButton>}
                    </UploadForm>
                </PremiumGate>
            </AssetSection>

            <ReusableModal
                isOpen={showDeleteConfirmModal && !!assetToDelete}
                onClose={() => { if (!isDeleting) { setShowDeleteConfirmModal(false); setAssetToDelete(null); }}}
                title={t('audioAssets.confirmDeleteTitle', 'Confirm Deletion')}
                titleIcon={<FiAlertCircle style={{ color: theme?.errorColor || '#d9534f' }} />}
                isLoading={isDeleting}
                footerContent={
                    <>
                        <ActionButton $variant="secondary" onClick={() => { setShowDeleteConfirmModal(false); setAssetToDelete(null); }} disabled={isDeleting}>
                            {t('actions.cancel', 'Cancel')}
                        </ActionButton>
                        <ActionButton $variant="danger" onClick={executeDelete} disabled={isDeleting}>
                            {isDeleting ? <FiLoader style={{ animation: 'spin 1s linear infinite', marginRight: '0.5em' }} /> : <FiTrash2 />}
                            {isDeleting ? t('actions.deleting', 'Deleting...') : t('actions.deleteConfirm', 'Yes, delete')}
                        </ActionButton>
                    </>
                }
              >
                {assetToDelete && (
                     <p>
                        {t('audioAssets.confirmDeleteMessage', "Are you sure you want to delete the file '{{fileName}}'? This action is irreversible.", { fileName: assetToDelete.asset.name })}
                    </p>
                )}
            </ReusableModal>
        </PageContainer>
    );
};

export default AudioAssetsConfigPage;