import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { FiGrid } from 'react-icons/fi';

const Container = styled.div`
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 2.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }

  p {
    font-size: 1.1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const ComingSoon = styled.div`
  text-align: center;
  padding: 3rem;
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  border: 2px dashed ${({ theme }) => theme.border};
  color: ${({ theme }) => theme.textSecondary};
  font-style: italic;
`;

const CategoriesPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Container>
      <Header>
        <h1>
          <FiGrid />
          {t('categories.title')}
        </h1>
        <p>{t('categories.description')}</p>
      </Header>

      <ComingSoon>
        {t('categories.comingSoon')}
      </ComingSoon>
    </Container>
  );
};

export default CategoriesPage;
