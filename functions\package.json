{"name": "functions", "version": "1.0.0", "description": "Cloud Functions for Firebase", "main": "lib/index.js", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "dependencies": {"@google-cloud/text-to-speech": "^6.1.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "stripe": "^14.25.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/crypto-js": "^4.2.2", "@types/node": "^20.19.2", "@types/stripe": "^8.0.417", "@typescript-eslint/eslint-plugin": "^6.x.x", "@typescript-eslint/parser": "^6.x.x", "eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.29.x", "firebase-functions-test": "^3.1.0", "typescript": "^5.8.3"}, "private": true}