import React, { useEffect, useState, useMemo } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
import {
  FiMessageSquare, FiSend, FiUser, FiEdit, FiThumbsUp, FiMessageCircle as FiCommentIcon,
  FiClock, FiLoader, FiAlertCircle, FiUserX
} from 'react-icons/fi';
import { useAppStore } from '../store/useAppStore';
import type { BlogPost, AppStore } from '../store/useAppStore';
import { getAuth, onAuthStateChanged } from 'firebase/auth';

// --- Styled Components (Unchanged) ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  p {
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const ControlsBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
`;

const SearchInput = styled.input`
  flex-grow: 1;
  min-width: 200px;
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  &:focus { outline: 2px solid ${({ theme }) => theme.primary}80; }
`;

const CategorySelect = styled.select`
  padding: 极mer 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  min-width: 180px;
  &:focus { outline: 2px solid ${({ theme }) => theme.primary}80; }
`;

const PostFormCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem;
  padding: 1.5rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.4rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  padding: 0.8rem 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  &:focus { outline: 2px solid ${({ theme }) => theme.primary}80; }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
`;

const CategoryInputGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
    label {
        font-size: 0.9rem;
        color: ${({ theme }) => theme.textSecondary};
    }
`;

const Button = styled.button` /* ... (button styles) ... */ `;

const PostCard = styled.div<{ $isUnsynced?: boolean }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border-left: 4px solid ${({ theme, $isUnsynced }) => $isUnsynced ? theme.errorColor : theme.primary};
  opacity: ${({ $isUnsynced }) => $isUnsynced ? 0.7 : 1};
`;

const PostHeader = styled.div` /* ... */ `;
const AuthorPseudo = styled.span` /* ... */ `;
const PostDate = styled.span` /* ... */ `;
const PostContent = styled.p` /* ... */ `;
const PostFooter = styled.div` /* ... */ `;
const ActionButton = styled.button<{ $liked?: boolean }>` /* ... */ `;
const LoadingContainer = styled.div` /* ... */ `;
const ErrorMessage = styled.div` /* ... */ `;
const InfoMessage = styled.div` /* ... */ `;


const ALL_CATEGORIES = ["general", "gratitude", "challenges", "inspirations", "questions"];

// This function can be moved to a utils file if used elsewhere

const generateAnonymousPseudo = (uid: string) => {
    const animals = ["Wolf", "Eagle", "Fox", "Bear", "Deer", "Owl", "Hawk", "Lion", "Tiger", "Panther"];
    const adjectives = ["Serene", "Wise", "Curious", "Strong", "Peaceful", "Bright", "Agile", "Fearless", "Calm", "Creative"];
    const hash = uid.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return `${adjectives[hash % adjectives.length]} ${animals[hash % animals.length]}`;
};


const BlogPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang();
  
  // Get data from Zustand store with proper typing
  // Blog functionality removed
  const blog = { posts: [], comments: {}, status: 'idle', error: null };
  const initializeBlog = () => {};
  const addNewPost = async (uid: string, newPost: any) => ({ id: '', title: '', content: '', authorId: '', createdAt: '', updatedAt: '', likes: [] });
  const toggleLike = async (uid: string, postId: string) => {};
  const { posts, status, error } = blog;
  
  // Get user from Firebase auth
  const [currentUser, setCurrentUser] = useState<any>(null);
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
    });
    return unsubscribe;
  }, []);
  const isLoading = status === 'loading';
  const user = currentUser;

  // Local state for form inputs and filters
  const [newPostContent, setNewPostContent] = useState('');
  const [newPostCategory, setNewPostCategory] = useState<string>(ALL_CATEGORIES[0]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // Initialize blog data using Zustand action
  useEffect(() => {
    initializeBlog();
  }, [initializeBlog]);

  // Memoized filtering of posts
  const filteredPosts = useMemo(() => {
    let filtered = posts;
    if (selectedCategory) {
      filtered = filtered.filter((p: any) => p.category === selectedCategory);
    }
    if (searchTerm) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter((p: any) =>
        p.content.toLowerCase().includes(lowercasedTerm) ||
        p.authorPseudo.toLowerCase().includes(lowercasedTerm) ||
        (p.tags || []).some((tag: string) => tag.toLowerCase().includes(lowercasedTerm))
      );
    }
    return filtered;
  }, [posts, selectedCategory, searchTerm]);

  const handlePostSubmit = async () => {
    if (!newPostContent.trim() || !currentUser?.uid) return;
    setIsSubmitting(true);

    const postData = {
      title: newPostContent.substring(0, 20) + (newPostContent.length > 20 ? '...' : ''),
      content: newPostContent
    };

    try {
      await addNewPost(currentUser.uid, postData);
      setNewPostContent('');
      setNewPostCategory(ALL_CATEGORIES[0]);
    } catch (e) {
      console.error("Submission failed:", e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikePost = (postId: string) => {
    if (!user?.uid) return;
    toggleLike(user.uid, postId);
  };

  const formatDate = (isoDateString: string) => {
    if (!isoDateString) return t('blog.unknownDate', 'Unknown date');
    return new Date(isoDateString).toLocaleDateString(lang, {
      year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  if (isLoading && posts.length === 0) {
    return <LoadingContainer><FiLoader /> {t('loading.blog', 'Loading blog...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><FiAlertCircle size={30} /><p>{error}</p><Link to="/">{t('actions.backToHome', "Back to Home")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiMessageSquare /> {t('blog.title', 'Community Journal')}</h1>
        <p>{t('blog.description', 'Share your experiences, discoveries, and inspirations with the PiKnowKyo community. All posts are anonymous.')}</p>
      </PageHeader>

      <ControlsBar>
        <SearchInput
            type="text"
            placeholder={t('blog.searchPlaceholder', "Search posts...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
        />
        <CategorySelect value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)}>
            <option value="">{t('blog.allCategories', "All categories")}</option>
            {ALL_CATEGORIES.map(cat => (
                <option key={cat} value={cat}>{t(`blog.categories.${cat}`, cat.charAt(0).toUpperCase() + cat.slice(1))}</option>
            ))}
        </CategorySelect>
      </ControlsBar>

      {user ? (
        <PostFormCard>
          <SectionTitle style={{borderBottom: 'none', marginBottom: '1rem'}}><FiEdit /> {t('blog.writeNewPost', 'Write a new post')}</SectionTitle>
          <TextArea
            placeholder={t('blog.postPlaceholder', "Your message (will be published anonymously)...")}
            value={newPostContent}
            onChange={e => setNewPostContent(e.target.value)}
            rows={4}
          />
          <FormActions>
            <Button onClick={handlePostSubmit} disabled={isSubmitting || !newPostContent.trim()}>
              {isSubmitting ? <FiLoader style={{animation: 'spin 1s linear infinite'}} /> : <FiSend />}
              {isSubmitting ? t('blog.publishing', 'Publishing...') : t('blog.publish', 'Publish')}
            </Button>
          </FormActions>
        </PostFormCard>
      ) : (
        <InfoMessage>
          <FiUserX />
          {t('blog.loginToPost', 'You must be logged in to post a message.')}
        </InfoMessage>
      )}

      {filteredPosts.length === 0 && !isLoading && (
        <InfoMessage>
          <FiMessageSquare />
          {t('blog.noPostsFound', 'No posts found for this category or search term.')}
        </InfoMessage>
      )}

      {filteredPosts.map((post: BlogPost & { isSynced?: boolean; authorPseudo?: string; commentCount?: number }) => (
        <PostCard key={post.id} $isUnsynced={!post.isSynced} title={!post.isSynced ? t('blog.unsyncedPostTooltip', 'This post is saved locally and will be synced when you are online.') : ''}>
          <PostHeader>
            <AuthorPseudo><FiUser size={16}/> {post.authorPseudo}</AuthorPseudo>
            <PostDate><FiClock size={14}/> {formatDate(post.createdAt as string)}</PostDate>
          </PostHeader>
          <PostContent>{post.content}</PostContent>
          <PostFooter>
            <ActionButton
                onClick={() => handleLikePost(post.id)}
                $liked={user && post.likes.includes(user.uid)}
                title={t('blog.like', 'Like')}
                disabled={!post.isSynced} // Disable liking on unsynced posts
            >
              <FiThumbsUp /> {post.likes.length}
            </ActionButton>
            <ActionButton as={Link} to={`/blog/${post.id}/comments`} title={t('blog.comments', 'Comments')}>
              <FiCommentIcon /> {post.commentCount || 0}
            </ActionButton>
          </PostFooter>
        </PostCard>
      ))}
    </PageContainer>
  );
};

export default BlogPage;