// src/components/AuthPage.tsx
import React, { useState } from 'react';
import { useTheme } from '../ThemeProvider';
import { useTranslation } from 'react-i18next';
import { FiSun, FiMoon } from 'react-icons/fi';
import { useLang, Language } from '../LangProvider';
import ReactCountryFlag from 'react-country-flag';

import Login from './Login';
import SignupForm from './SignupForm';

import {
  AuthPageContainer,
  AuthCard,
  HeaderActions,
  ThemeToggleButton,
  AppLogo,
  Title,
  Subtitle,
  LangSelectorContainer,
  FlagContainer,
  FlagButton,
  WebsiteLink,
} from './common/AuthStyles';

const LanguageSelector: React.FC = () => {
  const { lang, setLang } = useLang();
  const { t } = useTranslation();

  const languages: { code: Language; countryCode: string; name: string }[] = [
    { code: 'fr', countryCode: 'FR', name: 'Français' },
    { code: 'en', countryCode: 'GB', name: 'English' },
    { code: 'es', countryCode: 'ES', name: 'Español' },
  ];

  const currentLangPath = languages.find(l => l.code === lang)?.code === 'fr' ? '' : `/${lang}`;

  return (
    <LangSelectorContainer>
      <FlagContainer>
        {languages.map(({ code, countryCode, name }) => (
          <FlagButton
            key={code}
            $isActive={lang === code}
            onClick={() => setLang(code)}
            aria-label={`Switch language to ${name}`}
          >
            <ReactCountryFlag
              countryCode={countryCode}
              svg
              // --- MODIFIED: Reduced the size of the flags ---
              // The parent button has a font-size of 2rem, so we use a smaller multiplier here.
              // 0.8em of 2rem results in a flag size of about 25px, a standard icon size.
              style={{
                width: '0.8em',
                height: '0.8em',
              }}
              title={name}
            />
          </FlagButton>
        ))}
      </FlagContainer>
      <WebsiteLink href={`https://piknowkyo.com${currentLangPath}`} target="_blank" rel="noopener noreferrer">
        {t('auth.common.visit_website')}
      </WebsiteLink>
    </LangSelectorContainer>
  );
};

const AuthPage: React.FC = () => {
  const [currentForm, setCurrentForm] = useState<'login' | 'signup'>('login');
  const { darkMode, toggleTheme } = useTheme();
  const { t } = useTranslation();

  const handleToggleForm = (formType: 'login' | 'signup') => {
    setCurrentForm(formType);
  };

  return (
    <AuthPageContainer>
      <AuthCard>
        <HeaderActions>
          <ThemeToggleButton
            onClick={toggleTheme}
            aria-label={darkMode ? t('app.theme_light') : t('app.theme_dark')}
          >
            {darkMode ? <FiSun size={20} /> : <FiMoon size={20} />}
          </ThemeToggleButton>
        </HeaderActions>

        <AppLogo src="/logo192.png" alt={t('app.logo_alt')} />
        <Title>{t('app.name')}</Title>
        <Subtitle>
          {currentForm === 'login' ? t('auth.login.subtitle') : t('auth.signup.subtitle')}
        </Subtitle>

        {currentForm === 'login' ? (
          <Login onToggleForm={handleToggleForm} />
        ) : (
          <SignupForm onToggleForm={handleToggleForm} />
        )}

        <LanguageSelector />
      </AuthCard>
    </AuthPageContainer>
  );
};

export default AuthPage;