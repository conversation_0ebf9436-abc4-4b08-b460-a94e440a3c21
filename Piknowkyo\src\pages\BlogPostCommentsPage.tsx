import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { getAuth } from 'firebase/auth';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
import { FiSend, FiChevronLeft, FiLoader, FiUser, FiClock, FiAlertCircle } from 'react-icons/fi';
import { useAppStore } from '../store/useAppStore';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const BackButton = styled.button`
  background: none;
  border: 1px solid ${({ theme }) => theme.border};
  color: ${({ theme }) => theme.text};
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.2s;
  &:hover {
    background-color: ${({ theme }) => theme.surfaceAlt};
    color: ${({ theme }) => theme.primary};
  }
`;

const PostDetailCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const PostContent = styled.p`
  font-size: 1rem;
  line-height: 1.7;
  color: ${({ theme }) => theme.text};
  white-space: pre-wrap;
  margin-bottom: 1rem;
`;

const AuthorInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.9rem;
  span {
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }
`;

const CommentsSection = styled.section`
  margin-top: 2rem;
  h2 {
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  }
`;

const CommentList = styled.ul`
  list-style: none;
  padding: 0;
`;

const CommentItem = styled.li<{ $isUnsynced?: boolean }>`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 3px solid ${({ theme, $isUnsynced }) => $isUnsynced ? theme.errorColor : theme.accent};
  opacity: ${({ $isUnsynced }) => $isUnsynced ? 0.75 : 1};

  p { margin: 0.5rem 0 0 0; font-size: 0.95rem; line-height: 1.6; }
  small { font-size: 0.8rem; color: ${({ theme }) => theme.textMuted}; display: block; }
`;

const CommentFormCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  h3 { margin-top: 0; }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  padding: 0.8rem 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  &:focus { outline: 2px solid ${({ theme }) => theme.primary}80; }
`;

const Button = styled.button`
  background: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  &:hover {
    background: ${({ theme }) => theme.secondary};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
`;

const ErrorMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  color: ${({ theme }) => theme.errorColor};
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
  }
`;

const generateAnonymousPseudo = (uid: string) => {
    const animals = ["Wolf", "Eagle", "Fox", "Bear", "Deer", "Owl", "Hawk", "Lion", "Tiger", "Panther"];
    const adjectives = ["Serene", "Wise", "Curious", "Strong", "Peaceful", "Bright", "Agile", "Fearless", "Calm", "Creative"];
    const hash = uid.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return `${adjectives[hash % adjectives.length]} ${animals[hash % animals.length]}`;
};

const BlogPostCommentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { postId } = useParams<{ postId: string }>();
  const navigate = useNavigate();
  const { lang } = useLang();

  // Blog functionality removed
  const profile = useAppStore((state) => state.profile);
  const blogStatus = 'idle';
  const error = null;
  const posts = [];
  const allComments = {};
  const addNewComment = async () => ({ id: '', postId: '', authorId: '', content: '', createdAt: '' });
  const getCommentsForPost = async () => [];
  
  const post = null;
  const comments = [];

  // Local state for form only
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // All blog functionality removed


  return (
    <PageContainer>
      <BackButton onClick={() => navigate('/')} title={t('actions.backToHome', "Back to Home")}>
        <FiChevronLeft />
        {t('actions.backToHome', "Back to Home")}
      </BackButton>

      <ErrorMessage>
        <FiAlertCircle size={30} />
        <p>{t('blog.featureRemoved', 'Blog feature has been removed')}</p>
      </ErrorMessage>
    </PageContainer>
  );
};

export default BlogPostCommentsPage;