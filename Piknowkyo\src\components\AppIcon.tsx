// src/components/AppIcon.tsx
import React from 'react';
import { IconType } from 'react-icons';

// Navigation & UI Icons
import {
  FiHome, FiSettings, FiUser, FiBook, FiBarChart2, FiAward,
  FiMessageSquare, FiDollarSign, FiLogOut, FiSun, FiMoon, FiGrid,
  FiList, FiHeart, FiXCircle, FiRefreshCw, FiPlay, FiLock, FiArrowRight, FiMenu, FiX,
  FiUsers, FiBookOpen, FiThumbsUp, FiLoader, FiSearch, FiChevronDown, FiChevronRight, FiHelpCircle,
  FiSliders, FiClock, FiArrowLeft, FiMic, FiTarget, FiMusic,
  FiEdit2, FiTrash2, FiVolume2, FiStopCircle, FiCloud, FiCloudOff, FiAlertCircle, FiCornerDownLeft,
  FiRefreshCcw, FiBell, FiPause, FiGlobe, FiInfo, FiStar,
  FiCreditCard,
  FiCalendar,
  FiPieChart,
  FiChevronLeft,
  FiRadio,
  FiMessageCircle,
  FiMaximize,
  FiMinimize,
  FiTag,
  FiCheck,
  FiCheckCircle,
  FiHeadphones,
  FiPlayCircle, // Added for play button
  FiEdit3,      // Added for journal icon
} from 'react-icons/fi';
import { FaGamepad, FaHeart, FaLightbulb } from 'react-icons/fa';
import { GiSparkles, GiWeightLiftingUp, GiSoundOn } from 'react-icons/gi';
import { TbMoodCog } from 'react-icons/tb';

// Session Type Icons
import {
  BsHypnotize, BsPencilSquare, BsImage, BsWind
} from 'react-icons/bs';
import { GiMeditation, GiHeartPlus } from 'react-icons/gi';
import { MdOutlineModelTraining } from 'react-icons/md';
import { FiFeather } from 'react-icons/fi';
import {
  PiEye, PiPersonSimpleRun, PiBed, PiPuzzlePiece
} from 'react-icons/pi';
import { TbMoodSilence, TbWriting } from 'react-icons/tb';
import { FaRegComments } from 'react-icons/fa';

interface AppIconProps {
  name: string;
  size?: number;
  className?: string;
  style?: React.CSSProperties;
}

const iconMap: { [key: string]: IconType } = {
  // Navigation & UI
  home: FiHome,
  sessions: FiBook,
  journal: FiBookOpen,
  stats: FiBarChart2,
  leaderboard: FiAward,
  games: FaGamepad,
  about: FiBook,
  profile: FiUser,
  monetization: FiDollarSign,
  settings: FiSettings,
  'audio-assets': GiSoundOn,
  lexicon: FiBookOpen,
  logout: FiLogOut,
  sun: FiSun,
  moon: FiMoon,
  'theme-light': FiSun,
  'theme-dark': FiMoon,
  grid: FiGrid,
  list: FiList,
  heart: FiHeart,
  favorite: FiHeart,
  favorites: FaHeart,
  'clear-filters': FiRefreshCw,
  play: FiPlay,
  'play-circle': FiPlayCircle, // Added
  pause: FiPause,
  lock: FiLock,
  'arrow-right': FiArrowRight,
  'arrow-left': FiArrowLeft,
  recommendation: GiSparkles,
  menu: FiMenu,
  close: FiX,
  loader: FiLoader,
  search: FiSearch,
  'chevron-down': FiChevronDown,
  'chevron-right': FiChevronRight,
  'help-circle': FiHelpCircle,
  sliders: FiSliders,
  clock: FiClock,
  lightbulb: FaLightbulb,
  return: FiCornerDownLeft,
  bell: FiBell,
  music: FiMusic,
  globe: FiGlobe,
  'credit-card': FiCreditCard,
  info: FiInfo,
  award: FiAward,
  star: FiStar,
  calendar: FiCalendar,
  'pie-chart': FiPieChart,
  'chevron-left': FiChevronLeft,
  radio: FiRadio,
  'message-circle': FiMessageCircle,
  maximize: FiMaximize,
  minimize: FiMinimize,
  tag: FiTag,
  check: FiCheck,
  'check-circle': FiCheckCircle,
  headphones: FiHeadphones,

  // Sync Status Icons
  'sync-ok': FiCloud,
  'sync-offline': FiCloudOff,
  'sync-error': FiAlertCircle,
  syncing: FiRefreshCw,
  'refresh-cw': FiRefreshCcw,

  // Journal Icons
  'edit-2': FiEdit2,
  'edit-3': FiEdit3, // Added
  'trash-2': FiTrash2,

  // Definition Modal Icons
  'volume-2': FiVolume2,
  'stop-circle': FiStopCircle,

  // Aliases for recommendation modal options
  wind: BsWind,
  target: FiTarget,
  mic: FiMic,
  'book-open': FiBookOpen,
  eye: PiEye,

  // Session Types & Tools
  hypnosis: BsHypnotize,
  meditation: GiMeditation,
  training: MdOutlineModelTraining,
  nlp: GiWeightLiftingUp,
  story: FiBook,
  journaling: TbWriting,
  visualization: PiEye,
  relaxation: TbMoodSilence,
  coaching: FiUsers,
  'sleep induction': PiBed,
  roleplay: FaRegComments,
  affirmation: FiThumbsUp,
  'gratitude practice': FiHeart,
  breathwork: BsWind,
  'motivational speech': FiMic,
  'guided imagery': BsImage,
  'problem solving': PiPuzzlePiece,
  'creative writing': FiFeather,
  'mindful movement': PiPersonSimpleRun,
  'self-compassion': GiHeartPlus,
  'focus enhancement': FiTarget,
  
  // Aliases for keys with hyphens
  'guided-imagery': BsImage,
  'problem-solving': PiPuzzlePiece,
  'creative-writing': FiFeather,
  'sleep-induction': PiBed,
  'motivational-speech': FiMic,
  'mindful-movement': PiPersonSimpleRun,
  'focus-enhancement': FiTarget,
  'gratitude-practice': FiHeart,
  
  // Default fallback icon
  default: TbMoodCog,
};

const AppIcon: React.FC<AppIconProps> = ({ name, size = 20, className, style }) => {
  const lookupName = name.toLowerCase().replace(/\s+/g, '-');
  const IconComponent = iconMap[lookupName] || iconMap.default;
  return <IconComponent size={size} className={className} style={style} />;
};

export default AppIcon;