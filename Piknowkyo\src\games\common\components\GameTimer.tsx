// src/games/common/components/GameTimer.tsx
import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiClock } from 'react-icons/fi';

const TimerContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: ${({ theme }) => theme.textLight || 'white'};
`;

interface GameTimerProps {
  timeInSeconds: number;
}

const GameTimer: React.FC<GameTimerProps> = ({ timeInSeconds }) => {
  const { t } = useTranslation();
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = timeInSeconds % 60;
  return (
    <TimerContainer>
      <FiClock /> {t('game.time', 'Temps')}: {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
    </TimerContainer>
  );
};

export default GameTimer;