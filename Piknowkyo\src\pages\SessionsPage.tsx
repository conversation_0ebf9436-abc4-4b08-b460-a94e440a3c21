// src/pages/SessionsPage.tsx

import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store/useAppStore';
import SessionsList from '../components/SessionsList';
import AppIcon from '../components/AppIcon';
import { useAuth } from '../hooks/useAuth';
import RecommendationModal from '../components/RecommendationModal';

// --- STYLED COMPONENTS ---

const PageContainer = styled.div`
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  overflow-x: hidden; // Prevents any accidental horizontal scroll on the page level
  @media (min-width: 768px) { padding: 1.5rem; }
`;

const PageTitle = styled.h1`
  font-size: 2rem;
  color: ${({ theme }) => theme.primary};
  margin-bottom: 2rem;
  text-align: center;
  @media (min-width: 768px) { font-size: 2.2rem; }
`;

const FilterBar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2.5rem;
`;

const MainFilterRow = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;

  @media (min-width: 768px) {
    grid-template-columns: 1fr auto auto;
  }
`;

const SearchInputContainer = styled.div`
  flex-grow: 1;
  position: relative;
  display: flex;
  align-items: center;

  .search-icon {
    position: absolute;
    left: 1rem;
    color: ${({ theme }) => theme.textMuted};
    pointer-events: none;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.85rem 0.85rem 0.85rem 2.75rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  &:focus { outline: none; border-color: ${({ theme }) => theme.primary}; }
`;

const DropdownGroup = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
`;

const FilterSelect = styled.select`
  width: 100%;
  padding: 0.85rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background-color: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  cursor: pointer;
  appearance: none;
`;

const ActionRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
`;

const ActionGroup = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;
`;

const IconButton = styled.button<{ $active?: boolean }>`
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background-color: ${({ theme, $active }) => $active ? theme.primary + '20' : 'transparent'};
  color: ${({ theme, $active }) => $active ? theme.primary : theme.textSecondary};
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    border-color: ${({ theme }) => theme.primary};
    background-color: ${({ theme }) => theme.primary + '15'};
  }
  
  &:disabled { opacity: 0.5; cursor: not-allowed; }
`;

const RecommendationButton = styled.button`
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  border: none;
  background-color: ${({ theme }) => theme.accent};
  color: ${({ theme }) => theme.textLight};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  transition: background-color 0.2s;
  
  &:hover { background-color: ${({ theme }) => theme.accentHover}; }
`;

const FilterInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary};
  margin-top: -1.5rem;
  margin-bottom: 1.5rem;

  strong {
    color: ${({ theme }) => theme.primary};
  }
`;

const AccordionContainer = styled.div`
  display: flex; flex-direction: column; gap: 1.5rem;
`;

const AccordionSection = styled.div`
  background: ${({ theme }) => theme.surface};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 12px;
  overflow: hidden;
`;

const AccordionHeader = styled.div`
  padding: 1.25rem; cursor: pointer; display: flex; align-items: center; gap: 1rem;
  transition: background-color 0.2s;
  &:hover { background: ${({ theme }) => theme.surfaceAlt}; }

  @media (max-width: 768px) {
    padding: 1rem;
    gap: 0.75rem;
  }
`;

const AccordionTitle = styled.div`
  flex-grow: 1;
  min-width: 0; // Prevent flex item from overflowing
  
  h2 { 
    font-size: 1.5rem; 
    margin: 0; 
    color: ${({ theme }) => theme.text}; 
  }
  
  p { 
    font-size: 0.9rem; 
    margin: 0.25rem 0 0; 
    color: ${({ theme }) => theme.textSecondary}; 
    max-width: 80ch; // Good for readability on large screens
  }

  @media (max-width: 768px) {
    h2 { font-size: 1.2rem; }
    p { 
      font-size: 0.85rem;
      max-width: none; // THIS IS THE KEY FIX: allows text to wrap naturally on mobile
    }
  }
`;

const StyledAppIcon = styled(AppIcon)<{ $isOpen?: boolean }>`
  transition: transform 0.3s;
  ${({ $isOpen }) => $isOpen && 'transform: rotate(180deg);'}
`;

const AccordionContent = styled.div`
  padding: 0 1.25rem 1.25rem;

  @media (max-width: 768px) {
    padding: 0 1rem 1rem;
  }
`;

const SearchResultsContainer = styled.div`
  padding-top: 1rem;
`;

const LoadingContainer = styled.div`
  display: flex; justify-content: center; align-items: center; min-height: 200px;
  svg { font-size: 2rem; color: ${({ theme }) => theme.primary}; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

// --- COMPONENT IMPLEMENTATION ---

const SessionsPage: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { user } = useAuth();
  
  const categories = useAppStore(state => state.sessions.categories);
  const allScripts = useAppStore(state => state.sessions.scripts);
  const isLoading = useAppStore(state => state.sessions.isLoading);
  const favoriteSessions = useAppStore(state => state.activity?.favoriteSessions);

  const [isRecommendationModalOpen, setRecommendationModalOpen] = useState(false);

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isFavoriteFilterActive, setFavoriteFilterActive] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [durationFilter, setDurationFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [tagFilter, setTagFilter] = useState(''); // New state for tag filter
  const [openCategories, setOpenCategories] = useState<string[]>([]);

  const isSearchingOrFiltering = searchTerm.trim() !== '' || tagFilter !== '';

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const category = params.get('category');
    const filter = params.get('filter');
    const action = params.get('action');
    const tag = params.get('tag'); // Get tag from URL

    if (category) setCategoryFilter(category);
    if (tag) setTagFilter(tag);
    if (filter === 'favorites') setFavoriteFilterActive(true);
    if (action === 'recommend') setRecommendationModalOpen(true);
    
  }, [location.search]);

  const handleToggleCategory = (category: string) => {
    setOpenCategories(prev => prev.includes(category) ? prev.filter(c => c !== category) : [...prev, category]);
  };
  
  const handleClearFilters = () => {
    setSearchTerm('');
    setCategoryFilter('all');
    setDurationFilter('all');
    setFavoriteFilterActive(false);
    setTagFilter(''); // Clear tag filter
  };

  const allSessionsFlat = useMemo(() => Object.values(allScripts).flat(), [allScripts]);

  const filteredData = useMemo(() => {
    let filtered = [...allSessionsFlat];
    if (categoryFilter !== 'all') { filtered = filtered.filter(s => s.type === categoryFilter); }
    if (isFavoriteFilterActive) { filtered = filtered.filter(s => favoriteSessions?.includes(s.id)); }
    if (durationFilter !== 'all') {
      const min = Number(durationFilter.split('-')[0]);
      const max = Number(durationFilter.split('-')[1]);
      filtered = filtered.filter(s => {
        const d = s.duration || 0;
        if (!max) return d > min;
        return d > min && d <= max;
      });
    }
    // Apply tag filter
    if (tagFilter) {
      filtered = filtered.filter(s => s.tags?.some(t => t.toLowerCase() === tagFilter.toLowerCase()));
    }
    // Apply search term filter
    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(s => s.title.toLowerCase().includes(searchTerm.trim().toLowerCase()) || s.description.toLowerCase().includes(searchTerm.trim().toLowerCase()));
    }
    return filtered;
  }, [allSessionsFlat, categoryFilter, isFavoriteFilterActive, durationFilter, searchTerm, tagFilter, favoriteSessions]);

  const sessionsGroupedByCategory = useMemo(() => {
    if (isSearchingOrFiltering) return {}; // Don't group if searching or filtering by tag
    return filteredData.reduce((acc, session) => {
      const category = allScripts[session.type] ? session.type : 'uncategorized';
      if (!acc[category]) acc[category] = [];
      acc[category].push(session);
      return acc;
    }, {} as { [key: string]: typeof filteredData });
  }, [filteredData, isSearchingOrFiltering, allScripts]);

  const availableCategoriesForFilter = useMemo(() => {
    return categories.filter(cat => allScripts[cat]?.length > 0);
  }, [categories, allScripts]);


  if (isLoading) {
    return <LoadingContainer><AppIcon name="loader" size={40} /></LoadingContainer>;
  }

  return (
    <>
      <RecommendationModal 
        isOpen={isRecommendationModalOpen} 
        onClose={() => setRecommendationModalOpen(false)} 
        allSessions={allSessionsFlat}
      />
      <PageContainer>
        <PageTitle>{t('sessions.title', 'Explore Sessions')}</PageTitle>
        
        <FilterBar>
          <MainFilterRow>
            <SearchInputContainer>
              <AppIcon name="search" className="search-icon" />
              <SearchInput type="text" placeholder={t('sessions.filter.searchPlaceholder', 'Search all sessions...')} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </SearchInputContainer>
            <DropdownGroup>
              <FilterSelect value={categoryFilter} onChange={(e) => setCategoryFilter(e.target.value)}>
                <option value="all">{t('sessions.filter.allCategories', 'All Categories')}</option>
                {availableCategoriesForFilter.map(cat => <option key={cat} value={cat}>{t(`sessionTypes.${cat}`, cat)}</option>)}
              </FilterSelect>
              <FilterSelect value={durationFilter} onChange={(e) => setDurationFilter(e.target.value)}>
                <option value="all">{t('sessions.filter.duration.all', 'Any duration')}</option>
                <option value="0-15">0-15 {t('units.min', 'min')}</option>
                <option value="15-30">15-30 {t('units.min', 'min')}</option>
                <option value="30-999">30+ {t('units.min', 'min')}</option>
              </FilterSelect>
            </DropdownGroup>
          </MainFilterRow>
          <ActionRow>
            <ActionGroup>
              <IconButton onClick={() => setFavoriteFilterActive(!isFavoriteFilterActive)} $active={isFavoriteFilterActive} disabled={!user} title={t('sessions.filter.showFavorites', 'Favorites')}>
                <AppIcon name="favorite" size={18} />
              </IconButton>
              <IconButton onClick={() => setViewMode(prev => prev === 'grid' ? 'list' : 'grid')} title={t('sessions.filter.toggleView', 'Toggle view')}>
                <AppIcon name={viewMode === 'grid' ? 'list' : 'grid'} size={18} />
              </IconButton>
              <IconButton onClick={handleClearFilters} title={t('sessions.filter.clear', 'Clear filters')}>
                <AppIcon name="clear-filters" size={18} />
              </IconButton>
            </ActionGroup>
            <RecommendationButton onClick={() => setRecommendationModalOpen(true)}>
                <AppIcon name="recommendation" />{t('sessions.recommendation.button', 'Get a Recommendation')}
              </RecommendationButton>
          </ActionRow>
        </FilterBar>

        {tagFilter && (
            <FilterInfo>
                <AppIcon name="tag" size={16} />
                <span>{t('sessions.filter.filteringByTag', 'Filtering by tag:')}</span>
                <strong>{tagFilter}</strong>
            </FilterInfo>
        )}
        
        {isSearchingOrFiltering ? (
          <SearchResultsContainer>
            <SessionsList sessions={filteredData} viewMode={viewMode} />
          </SearchResultsContainer>
        ) : (
          <AccordionContainer>
            {Object.entries(sessionsGroupedByCategory).map(([category, sessions]) => (
              <AccordionSection key={category}>
                <AccordionHeader onClick={() => handleToggleCategory(category)}>
                  <AppIcon name={category} size={40} />
                  <AccordionTitle>
                    <h2>{t(`sessionTypes.${category}`, category)}</h2>
                    <p>{t(`sessionTypesDescriptions.${category}`, '')}</p>
                  </AccordionTitle>
                  <StyledAppIcon name="chevron-down" size={24} $isOpen={openCategories.includes(category)} />
                </AccordionHeader>
                {openCategories.includes(category) && (<AccordionContent><SessionsList sessions={sessions} viewMode={viewMode} /></AccordionContent>)}
              </AccordionSection>
            ))}
          </AccordionContainer>
        )}
      </PageContainer>
    </>
  );
};

export default SessionsPage;