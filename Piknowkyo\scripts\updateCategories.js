// scripts/resync-categories.js

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import admin from 'firebase-admin';
import CryptoJS from 'crypto-js';
import dotenv from 'dotenv';

// --- Environment Configuration ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '../.env') });

const { VITE_SHARED_ENCRYPTION_KEY } = process.env;

if (!VITE_SHARED_ENCRYPTION_KEY) {
  console.error("ERROR: VITE_SHARED_ENCRYPTION_KEY is not defined in your .env file.");
  process.exit(1);
}

// --- Firebase Admin Initialization ---
// This uses the same service account key as your other script.
const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');
if (!fs.existsSync(serviceAccountPath)) {
  console.error(`ERROR: Service account key not found at: ${serviceAccountPath}`);
  console.error("Please ensure 'serviceAccountKey.json' is in the project root.");
  process.exit(1);
}
const serviceAccount = fs.readJsonSync(serviceAccountPath);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
const db = admin.firestore();

const LANGUAGES = ['fr', 'en', 'es'];

/**
 * Reads existing categories (subcollections) from Firestore for a given language,
 * then re-encrypts and updates the category index document.
 * @param {string} lang - The language code ('fr', 'en', 'es').
 */
async function resyncCategoryIndex(lang) {
  console.log(`\n--- Starting category resync for language: '${lang}' ---`);

  try {
    const langDocRef = db.collection('SessionScripts').doc(lang);
    
    // 1. Fetch all subcollections for the given language document from Firestore.
    const collections = await langDocRef.listCollections();
    
    if (collections.length === 0) {
      console.warn(`  - No subcollections (categories) found in Firestore for language '${lang}'. Index will be empty.`);
    }

    // 2. The ID of each subcollection IS the category name.
    const categoryIds = collections.map(col => col.id).sort(); // Sorting for consistency
    console.log(`  - Found categories in Firestore: ${categoryIds.join(', ')}`);

    // 3. Prepare the payload to be encrypted.
    const categoryPayload = {
      categories: categoryIds
    };
    
    // 4. Encrypt the payload using the shared key.
    const encryptedCategories = CryptoJS.AES.encrypt(
      JSON.stringify(categoryPayload),
      VITE_SHARED_ENCRYPTION_KEY
    ).toString();

    // 5. Update the main language document with the new encrypted index.
    await langDocRef.set({ encrypted: encryptedCategories }, { merge: true });

    console.log(`  ✅ Successfully RESYNCED and encrypted the category index for '${lang}'.`);
    console.log(`     Document 'SessionScripts/${lang}' now correctly lists ${categoryIds.length} categories.`);

  } catch (error) {
    console.error(`  ❌ An error occurred during the resync process for '${lang}':`, error);
  }
}

/**
 * Main function to run the resync process for all languages.
 */
async function main() {
  console.log('--- Connecting to Firestore to resynchronize category indexes ---');
  for (const lang of LANGUAGES) {
    await resyncCategoryIndex(lang);
  }
  console.log('\n--- Category resynchronization process finished. ---');
}

main().catch(console.error);