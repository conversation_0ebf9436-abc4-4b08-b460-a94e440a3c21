// src/components/BatchTranslateModal.tsx

import React, { useState, useRef, useEffect, useCallback } from 'react';
import styled, { keyframes } from 'styled-components';
import { FiX, FiPlay, FiSquare, FiClock } from 'react-icons/fi';
import { db } from '../firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import CryptoJS from 'crypto-js';
import type { BatchTask, ScriptData, ScriptStep } from '../pages/SessionManagementPage';

const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
if (!SHARED_KEY) throw new Error("VITE_SHARED_ENCRYPTION_KEY not defined");

// --- Data & Constants ---
const PROVIDERS: Record<string, {name: string, apiBase: string, canFetchModels: boolean}> = {
  groq: { name: 'Groq', apiBase: 'https://api.groq.com/openai/v1', canFetchModels: true },
  mistral: { name: 'Mistral', apiBase: 'https://api.mistral.ai/v1', canFetchModels: true },
  chutesai: { name: 'Chutes.ai', apiBase: 'https://llm.chutes.ai/v1', canFetchModels: false },
};
const SUPPORTED_LANGUAGES: Record<string, string> = { 'fr': 'French', 'en': 'English', 'es': 'Spanish' };

// --- Interfaces ---
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  tasks: BatchTask[];
}
interface ActiveProviderInfo { id: string; name: string; model: string; apiKey: string; apiBase: string; }
type FetchedModels = Record<string, { id: string }[]>;

// --- Styled Components ---
const ModalBackdrop = styled.div`
  position: fixed; top: 0; left: 0; width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex; justify-content: center; align-items: center; z-index: 1000;
`;
const ModalContent = styled.div`
  width: 90%; max-width: 800px; height: 90vh; max-height: 800px;
  background-color: #2d2d2d; color: #f8f8f2;
  border-radius: 8px; display: flex; flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
`;
const ModalHeader = styled.div`
  padding: 1rem; border-bottom: 1px solid #444;
  display: flex; justify-content: space-between; align-items: center;
  flex-shrink: 0; h3 { margin: 0; }
`;
const CloseButton = styled.button`
  background: none; border: none; color: #f8f8f2;
  font-size: 1.5rem; cursor: pointer;
`;
const ModalBody = styled.div`
  padding: 1rem; display: flex; flex-direction: column;
  gap: 1rem; overflow-y: auto; flex-grow: 1;
`;
const ConfigPanel = styled.div`
  display: flex; flex-direction: column; gap: 1rem;
  padding: 1rem; background-color: #3a3a3a; border-radius: 6px;
`;
const DelayControl = styled.div`
  display: flex; align-items: center; justify-content: space-between; gap: 1rem;
  label { font-weight: 500; }
  input {
    width: 60px; padding: 8px; border: 1px solid #666;
    border-radius: 4px; background-color: #2d2d2d; color: #f1f1f1; text-align: center;
  }
`;
const ActionButton = styled.button`
  padding: 10px 20px; border: none; border-radius: 6px;
  font-weight: 600; cursor: pointer; display: inline-flex;
  align-items: center; gap: 8px; font-size: 1rem;
  &.start { background-color: ${({ theme }) => theme.primary}; color: white; }
  &.cancel { background-color: ${({ theme }) => theme.danger}; color: white; }
  &:disabled { background-color: ${({ theme }) => theme.disabledBackground}; cursor: not-allowed; }
`;
const TaskList = styled.div`
  h4 { margin-top: 0; }
  ul {
    list-style: none; padding: 0; margin: 0; max-height: 150px; overflow-y: auto;
    background-color: #222; padding: 0.5rem; border-radius: 4px;
    font-family: monospace; font-size: 0.85em;
    li { padding: 4px 8px; }
  }
`;
const LogContainer = styled.div`
  h4 { margin-bottom: 0.5rem; }
  pre {
    flex-grow: 1; background-color: #1e1e1e; padding: 1rem;
    border-radius: 4px; margin: 0; white-space: pre-wrap; word-wrap: break-word;
    font-family: monospace; font-size: 0.9em; min-height: 200px; overflow-y: auto;
    display: flex; flex-direction: column-reverse; /* Keeps latest logs at the bottom */
  }
`;
const slideIn = keyframes` from { opacity: 0; transform: scale(0.95); } to { opacity: 1; transform: scale(1); } `;
const AnimatedModalContent = styled(ModalContent)` animation: ${slideIn} 0.2s ease-out forwards; `;
const CheckboxLabel = styled.label` display: flex; align-items: center; gap: 0.5rem; font-weight: 500; `;
const FormGrid = styled.div` display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; `;
const FormControl = styled.div`
  label, select, input { color: #f1f1f1; }
  label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
  input, select {
    width: 100%; padding: 8px; border: 1px solid #666;
    border-radius: 4px; box-sizing: border-box; background-color: #2d2d2d;
  }
`;

// --- Helper Functions ---
const sleep = (ms: number) => new Promise(res => setTimeout(res, ms));

const updateCategoryIndexIfNeeded = async (lang: string, newCategory: string) => {
    const langDocRef = doc(db, 'SessionScripts', lang);
    const docSnap = await getDoc(langDocRef);
    let currentCategories: string[] = [];
    if (docSnap.exists() && docSnap.data().encrypted) {
        try {
            currentCategories = JSON.parse(CryptoJS.AES.decrypt(docSnap.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8)).categories || [];
        } catch (e) { console.error(`Failed to decrypt category index for lang '${lang}'.`, e); }
    }
    const categorySet = new Set(currentCategories);
    if (!categorySet.has(newCategory)) {
        categorySet.add(newCategory);
        await setDoc(langDocRef, { encrypted: CryptoJS.AES.encrypt(JSON.stringify({ categories: Array.from(categorySet).sort() }), SHARED_KEY).toString() }, { merge: true });
    }
};

const BatchTranslateModal: React.FC<ModalProps> = ({ isOpen, onClose, onComplete, tasks }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [delay, setDelay] = useState(45);
  const [countdown, setCountdown] = useState(0);
  const isCancelledRef = useRef(false);
  const logContainerRef = useRef<HTMLPreElement>(null);

  const [allProviders, setAllProviders] = useState<ActiveProviderInfo[]>([]);
  const [activeProvider, setActiveProvider] = useState<ActiveProviderInfo | null>(null);
  const [useSameModel, setUseSameModel] = useState(true);
  const [translationProvider, setTranslationProvider] = useState<ActiveProviderInfo | null>(null);
  const [translationModels, setTranslationModels] = useState<FetchedModels>({});
  const [isFetchingModels, setIsFetchingModels] = useState(false);

  useEffect(() => {
    const loadAISettings = async () => {
        if (!isOpen) return;
        setIsRunning(false); 
        setLogs([]);      
        setCountdown(0);  
        isCancelledRef.current = false; 

        const docRef = doc(db, 'config', 'ai_settings');
        const docSnap = await getDoc(docRef);
        if (!docSnap.exists()) { console.error("AI Settings not found!"); return; }
        const settings = docSnap.data();
        const providers: ActiveProviderInfo[] = [];
        for (const id in PROVIDERS) {
            if (settings[id] && settings[id].selectedModel) {
                const apiKey = import.meta.env[`VITE_${id.toUpperCase()}_API_KEY`] || settings[id].apiKey;
                if (apiKey) providers.push({ id, name: PROVIDERS[id].name, model: settings[id].selectedModel, apiKey, apiBase: PROVIDERS[id].apiBase });
            }
        }
        setAllProviders(providers);
        const defaultProvider = providers.find(p => p.id === settings.defaultProvider) || providers[0] || null;
        setActiveProvider(defaultProvider);
        setTranslationProvider(defaultProvider);
    };
    loadAISettings();
  }, [isOpen]);

  useEffect(() => {
    if (logContainerRef.current) {
        logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  useEffect(() => {
    return () => { isCancelledRef.current = true; };
  }, []);

  const addLog = (message: string) => setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);

  const performAITranslation = useCallback(async (task: BatchTask, provider: ActiveProviderInfo): Promise<ScriptData> => {
    const template = task.session.en || task.session.fr || task.session.es;
    if (!template) throw new Error("No valid template script found.");

    const translatableContent: Record<string, string> = { title: template.title, description: template.description };
    if (template.benefits) (Array.isArray(template.benefits) ? template.benefits : [String(template.benefits)]).forEach((b, i) => { translatableContent[`benefit_${i}`] = b; });
    if (template.tags) (Array.isArray(template.tags) ? template.tags : [String(template.tags)]).forEach((t, i) => { translatableContent[`tag_${i}`] = t; });
    template.script?.forEach((s, i) => { translatableContent[`step_${i}_text`] = s.text; });

    const systemPrompt = `You are a high-quality JSON translator. Translate the STRING VALUES of the provided JSON object to ${SUPPORTED_LANGUAGES[task.lang]}. Respond with a JSON object with IDENTICAL KEYS. Your response must be ONLY the raw, valid JSON object.`;
    const response = await fetch(`${provider.apiBase}/chat/completions`, {
        method: 'POST', headers: { 'Authorization': `Bearer ${provider.apiKey}`, 'Content-Type': 'application/json' },
        body: JSON.stringify({ model: provider.model, messages: [{ role: 'system', content: systemPrompt }, { role: 'user', content: JSON.stringify(translatableContent, null, 2) }]})
    });
    if (!response.ok) { const err = await response.json(); throw new Error(err.error?.message || `API request failed with status ${response.status}`); }
    const data = await response.json();
    let translatedTexts: any = {};
    try {
        translatedTexts = JSON.parse(data.choices[0]?.message?.content.trim() || '{}');
    } catch (e) {
        throw new Error(`AI returned invalid JSON: ${data.choices[0]?.message?.content}`);
    }
    
    const newScript = JSON.parse(JSON.stringify(template));
    newScript.title = translatedTexts.title || template.title;
    newScript.description = translatedTexts.description || template.description;
    if (template.benefits) { const original = Array.isArray(template.benefits) ? template.benefits : [String(template.benefits)]; newScript.benefits = original.map((_, i) => translatedTexts[`benefit_${i}`] || original[i]); }
    if (template.tags) { const original = Array.isArray(template.tags) ? template.tags : [String(template.tags)]; newScript.tags = original.map((_, i) => translatedTexts[`tag_${i}`] || original[i]); }
    if (newScript.script) newScript.script.forEach((step: ScriptStep, i: number) => { step.text = translatedTexts[`step_${i}_text`] || step.text; });
    
    newScript.id = template.id; newScript.type = template.type; newScript.language = task.lang; newScript.updatedAt = new Date().toISOString(); delete newScript.createdAt;
    return newScript;
  }, []);

  const processSingleTaskWithRetry = async (task: BatchTask, provider: ActiveProviderInfo) => {
    const MAX_RETRIES = 2;
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
            if (isCancelledRef.current) throw new Error("Process cancelled by user.");
            addLog(`Attempt ${attempt}: Translating '${task.session.id}' (${task.session.category}) to ${task.lang.toUpperCase()}...`);
            const translatedScript = await performAITranslation(task, provider);
            
            addLog(`Attempt ${attempt}: Saving script...`);
            const encrypted = CryptoJS.AES.encrypt(JSON.stringify(translatedScript), SHARED_KEY).toString();
            await setDoc(doc(db, `SessionScripts/${task.lang}/${task.session.category}`, task.session.id), { encrypted });

            addLog(`Attempt ${attempt}: Updating category index...`);
            await updateCategoryIndexIfNeeded(task.lang, task.session.category);
            
            return; 
        } catch (error: any) {
            addLog(`❌ Attempt ${attempt} failed: ${error.message}`);
            if (attempt < MAX_RETRIES) {
                if (isCancelledRef.current) throw new Error("Process cancelled by user.");
                addLog("Waiting 60 seconds before retrying...");
                await sleep(60000);
            } else {
                throw new Error(`Task failed for '${task.session.id}' to ${task.lang.toUpperCase()} after ${MAX_RETRIES} attempts.`);
            }
        }
    }
  };

  const startBatchTranslation = async () => {
    const providerToUse = useSameModel ? activeProvider : translationProvider;
    if (!providerToUse) { alert("Please select a valid AI provider."); return; }

    setIsRunning(true);
    isCancelledRef.current = false;
    addLog(`Starting batch process with ${providerToUse.name} (${providerToUse.model})...`);

    for (const [index, task] of tasks.entries()) {
        if (isCancelledRef.current) { addLog("Process stopped by user."); break; }
        
        addLog(`--- [${index + 1}/${tasks.length}] ---`);
        try {
            await processSingleTaskWithRetry(task, providerToUse);
            addLog(`✅ SUCCESS: Task for '${task.session.id}' to ${task.lang.toUpperCase()} completed.`);
        } catch (error: any) {
            addLog(`❌ FATAL ERROR: ${error.message}. Stopping batch process.`);
            break; 
        }

        if (index < tasks.length - 1) {
            if (isCancelledRef.current) { addLog("Process stopped during delay."); break; }
            addLog(`Waiting for ${delay} seconds...`);
            for (let i = delay; i > 0; i--) {
                if (isCancelledRef.current) break;
                setCountdown(i);
                await sleep(1000);
            }
            setCountdown(0);
        }
    }

    addLog("--- Batch process finished. ---");
    setIsRunning(false);
    onComplete(); 
  };

  const handleCancel = () => { isCancelledRef.current = true; addLog("Cancellation requested. Stopping after current task..."); };
  
  const fetchTranslationModels = async (providerId: string) => {
    const provider = allProviders.find(p => p.id === providerId);
    if (!provider || !PROVIDERS[provider.id].canFetchModels) return;
    setIsFetchingModels(true);
    try {
        const response = await fetch(`${provider.apiBase}/models`, { headers: { 'Authorization': `Bearer ${provider.apiKey}` } });
        const data = await response.json();
        if (!response.ok) throw new Error(data.error?.message);
        setTranslationModels(prev => ({ ...prev, [providerId]: data.data }));
    } catch (e) { alert(`Failed to fetch models for ${provider.name}: ${e instanceof Error ? e.message : e}`); }
    finally { setIsFetchingModels(false); }
  };
  
  if (!isOpen) return null;

  return (
    <ModalBackdrop>
      <AnimatedModalContent>
        <ModalHeader>
          <h3>Batch Translation Process</h3>
          <CloseButton onClick={onClose} disabled={isRunning}><FiX/></CloseButton>
        </ModalHeader>
        <ModalBody>
          <TaskList>
            <h4>Tasks ({tasks.length})</h4>
            <ul>{tasks.map((task, i) => (<li key={i}>{task.session.category}/{task.session.id} ➔ {task.lang.toUpperCase()}</li>))}</ul>
          </TaskList>
          
          <ConfigPanel>
            <CheckboxLabel>
              <input type="checkbox" checked={useSameModel} onChange={() => setUseSameModel(prev => !prev)} disabled={isRunning} />
              Use default generation provider ({activeProvider?.name || 'N/A'} - {activeProvider?.model || 'N/A'})
            </CheckboxLabel>
            {!useSameModel && (
              <FormGrid>
                <FormControl>
                  <label>Translation Provider</label>
                  <select 
                    value={translationProvider?.id || ''} 
                    onChange={(e) => {
                      const selected = allProviders.find(p => p.id === e.target.value);
                      setTranslationProvider(selected || null);
                      if (selected && !translationModels[selected.id] && PROVIDERS[selected.id].canFetchModels) {
                        fetchTranslationModels(selected.id);
                      }
                    }} 
                    disabled={isRunning}
                  >
                    <option value="">-- Select Provider --</option>
                    {allProviders.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                  </select>
                </FormControl>
                <FormControl>
                  <label>Translation Model</label>
                  {translationProvider && PROVIDERS[translationProvider.id].canFetchModels ? (
                    <select 
                        value={translationProvider.model} 
                        onChange={e => setTranslationProvider(p => p ? {...p, model: e.target.value} : null)} 
                        onClick={() => translationProvider && !translationModels[translationProvider.id] && fetchTranslationModels(translationProvider.id)} 
                        disabled={isRunning || isFetchingModels}
                    >
                        {translationProvider.model && <option value={translationProvider.model}>{translationProvider.model} (current)</option>}
                        {translationModels[translationProvider.id]?.filter(m => m.id !== translationProvider.model).map(m => 
                            <option key={m.id} value={m.id}>{m.id}</option>
                        )}
                    </select>
                  ) : (
                    <input 
                        value={translationProvider?.model || ''} 
                        onChange={e => setTranslationProvider(p => p ? {...p, model: e.target.value} : null)} 
                        // CORRECTED: Explicitly convert to boolean for the 'disabled' prop
                        disabled={isRunning || !!(translationProvider && PROVIDERS[translationProvider.id].canFetchModels)}
                        placeholder={translationProvider && PROVIDERS[translationProvider.id].canFetchModels ? "Select from list" : "Enter model name"}
                    />
                  )}
                </FormControl>
              </FormGrid>
            )}
            <hr style={{border: 'none', borderTop: '1px solid #444', width: '100%', margin: '0.5rem 0'}}/>
            <DelayControl>
              <label htmlFor="delay-input">Delay between tasks:</label>
              <div>
                <input id="delay-input" type="number" value={delay} onChange={(e) => setDelay(Number(e.target.value))} disabled={isRunning} />
                <span> seconds</span>
              </div>
            </DelayControl>
            <div>
              {isRunning ? (<ActionButton className="cancel" onClick={handleCancel}><FiSquare /> Cancel Process</ActionButton>) 
                         : (<ActionButton className="start" onClick={startBatchTranslation} disabled={tasks.length === 0 || (!useSameModel && !translationProvider)}><FiPlay /> Start Batch Process</ActionButton>)}
            </div>
          </ConfigPanel>

          <LogContainer>
            <h4>Execution Log {countdown > 0 && <FiClock size={12} />} {countdown > 0 && `(Next task in ${countdown}s)`}</h4>
            <pre ref={logContainerRef}>{logs.length > 0 ? logs.join('\n') : 'Ready to start. Select provider and click "Start Batch Process".'}</pre>
          </LogContainer>
        </ModalBody>
      </AnimatedModalContent>
    </ModalBackdrop>
  );
};

export default BatchTranslateModal;