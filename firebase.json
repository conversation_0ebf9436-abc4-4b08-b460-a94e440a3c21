{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}]}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}]}