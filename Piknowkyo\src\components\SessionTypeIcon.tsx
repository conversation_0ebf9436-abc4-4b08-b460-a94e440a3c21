// src/components/SessionTypeIcon.tsx

import React from 'react';
import { IconType } from 'react-icons';

// Import a diverse set of icons for each session type
import {
  FiBookO<PERSON>,
  FiFeather,
  FiHeart,
  FiMic,
  FiTag,
  FiTarget,
  FiThumbsUp,
  FiUsers,
} from 'react-icons/fi';
import { BsHypnotize, BsPencilSquare, BsImage, BsWind } from 'react-icons/bs';
import { GiMeditation, GiHeartPlus } from 'react-icons/gi';
import { MdOutlineModelTraining } from 'react-icons/md';
import {
  <PERSON>Eye,
  PiPersonSimpleRun,
  PiBed,
  PiPuzzlePiece,
} from 'react-icons/pi';
import { TbMoodSilence, TbWriting } from 'react-icons/tb';
import { FaRegComments, FaLightbulb } from 'react-icons/fa';

interface SessionTypeIconProps {
  type: string;
  size?: number;
  className?: string;
}

// Maps session types to their corresponding React Icons component.
// This approach is cleaner and more scalable than a large switch statement.
const iconMap: { [key: string]: IconType } = {
  hypnosis: BsHypnotize,
  meditation: GiMeditation,
  training: MdOutlineModelTraining,
  story: FiBookOpen,
  journaling: TbWriting,
  visualization: PiEye,
  relaxation: TbMoodSilence,
  coaching: FiUsers,
  'sleep induction': PiBed,
  roleplay: FaRegComments,
  affirmation: FiThumbsUp,
  'gratitude practice': FiHeart,
  breathwork: BsWind,
  'motivational speech': FiMic,
  'guided imagery': BsImage,
  'problem solving': PiPuzzlePiece,
  'creative writing': FiFeather,
  'mindful movement': PiPersonSimpleRun,
  'self-compassion': GiHeartPlus,
  'focus enhancement': FiTarget,
  // Add other specific types here
  // A default icon for any type that doesn't have a specific mapping
  default: FiTag,
};

/**
 * A component that displays an icon corresponding to a given session type.
 * @param {string} type - The session type (e.g., 'meditation', 'hypnosis').
 * @param {number} [size=20] - The size of the icon.
 * @param {string} [className] - Optional CSS class for styling.
 */
const SessionTypeIcon: React.FC<SessionTypeIconProps> = ({
  type,
  size = 20,
  className,
}) => {
  // Select the icon component based on the session type (case-insensitive), or use a default.
  const IconComponent = iconMap[type.toLowerCase()] || iconMap.default;

  return <IconComponent size={size} className={className} />;
};

export default SessionTypeIcon;