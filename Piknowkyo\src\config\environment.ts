// /src/config/environment.ts
import dotenv from 'dotenv';
import path from 'path';

// Determine environment (default to development)
const env = process.env.NODE_ENV || 'development';

// Load environment variables only for production
if (env === 'production') {
  dotenv.config({ path: path.resolve(__dirname, '../../.env.production') });
} else {
  dotenv.config({ path: path.resolve(__dirname, '../../.env') });
}

// Export environment variables
export default {
  env,
  firebase: {
    apiKey: process.env.VITE_FIREBASE_API_KEY,
    authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.VITE_FIREBASE_APP_ID,
    measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
  },
  stripe: {
    publicKey: process.env.STRIPE_PUBLIC_KEY,
    secretKey: process.env.STRIPE_SECRET_KEY
  }
};