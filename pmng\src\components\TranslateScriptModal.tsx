// src/components/TranslateScriptModal.tsx

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { db } from '../firebase';
import { doc, getDoc } from 'firebase/firestore';
import { FiX, FiCheckCircle } from 'react-icons/fi';
import type { EditingPayload, ScriptData, ScriptStep } from '../pages/SessionManagementPage';

// Data, Interfaces, and Styled Components remain the same...
// --- Data & Constants ---
const PROVIDERS: Record<string, {name: string, apiBase: string, canFetchModels: boolean}> = {
  groq: { name: 'Groq', apiBase: 'https://api.groq.com/openai/v1', canFetchModels: true },
  mistral: { name: 'Mistral', apiBase: 'https://api.mistral.ai/v1', canFetchModels: true },
  chutesai: { name: 'Chutes.ai', apiBase: 'https://llm.chutes.ai/v1', canFetchModels: false },
};
const SUPPORTED_LANGUAGES: Record<string, string> = { 'fr': 'French', 'en': 'English', 'es': 'Spanish' };

// --- Interfaces ---
interface ActiveProviderInfo { id: string; name: string; model: string; apiKey: string; apiBase: string; }
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (sessionWithNewTranslation: any, lang: 'en'|'fr'|'es') => void;
  payload: EditingPayload;
}

// --- Styled Components (Identiques) ---
const ModalBackdrop = styled.div`
  position: fixed; top: 0; left: 0; width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.6); display: flex;
  justify-content: center; align-items: center; z-index: 1000;
`;
const ModalContent = styled.div`
  width: 90%; max-width: 1100px; height: 90vh; background-color: #2d2d2d;
  color: #f8f8f2; border-radius: 8px; display: flex; flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
`;
const ModalHeader = styled.div`
  padding: 1rem; border-bottom: 1px solid #444; display: flex;
  justify-content: space-between; align-items: center; h3 { margin: 0; }
`;
const CloseButton = styled.button`
  background: none; border: none; color: #f8f8f2; font-size: 1.5rem; cursor: pointer;
`;
const ModalFooter = styled.div`
  padding: 1rem; border-top: 1px solid #444; display: flex; justify-content: flex-end;
`;
const PrimaryButton = styled.button`
  padding: 10px 20px; border: none; background-color: ${({theme}) => theme.primary};
  color: white; border-radius: 6px; font-weight: 600; cursor: pointer;
  &:disabled { background-color: ${({theme}) => theme.disabledBackground}; }
`;
const AcceptButton = styled(PrimaryButton)`
  background-color: ${({theme}) => theme.success};
  display: inline-flex; align-items: center; gap: 8px;
`;
const ThinkingProcessContainer = styled.details`
  margin: 1rem; background-color: #3a3a3a; border: 1px solid #555; border-radius: 4px;
  summary { padding: 8px; cursor: pointer; font-weight: 500; }
  pre { background-color: #2d2d2d; padding: 10px; margin: 0; white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; font-size: 0.85em; }
`;
const TranslationGrid = styled.div`
  display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; padding: 1rem;
  overflow-y: auto; flex-grow: 1;
`;
const ScriptPreview = styled.pre`
  background-color: #222; padding: 1rem; border-radius: 4px; white-space: pre-wrap;
  word-wrap: break-word; font-family: monospace; font-size: 0.9em; height: 100%;
  max-height: 55vh; overflow-y: auto; box-sizing: border-box;
`;
const TranslationOptionsContainer = styled.div`
  background-color: #383838; border: 1px solid #555; border-radius: 4px;
  padding: 1rem; display: flex; flex-direction: column; gap: 1rem; margin: 1rem;
`;
const CheckboxLabel = styled.label` display: flex; align-items: center; gap: 0.5rem; font-weight: 500; `;
const FormGrid = styled.div` display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; `;
const FormControl = styled.div`
  label, select, input { color: #f1f1f1; }
  label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
  input, select { width: 100%; padding: 8px; border: 1px solid #666; border-radius: 4px; box-sizing: border-box; background-color: #2d2d2d; }
`;


const TranslateScriptModal: React.FC<ModalProps> = ({ isOpen, onClose, onAccept, payload }) => {
    const [isTranslating, setIsTranslating] = useState(false);
    const [thinkingProcess, setThinkingProcess] = useState('');
    const [translatedScript, setTranslatedScript] = useState<ScriptData | null>(null);
    const [activeProvider, setActiveProvider] = useState<ActiveProviderInfo | null>(null);
    const [useSameModel, setUseSameModel] = useState(true);
    const [translationProvider, setTranslationProvider] = useState<ActiveProviderInfo | null>(null);
    const [allProviders, setAllProviders] = useState<ActiveProviderInfo[]>([]);
    
    useEffect(() => {
        const loadAISettings = async () => {
            if (!isOpen) return;
            const docRef = doc(db, 'config', 'ai_settings');
            const docSnap = await getDoc(docRef);
            if (!docSnap.exists()) return;
            const settings = docSnap.data();
            const providers: ActiveProviderInfo[] = [];
            for (const id in PROVIDERS) {
                if (settings[id] && settings[id].selectedModel) {
                    const apiKey = import.meta.env[`VITE_${id.toUpperCase()}_API_KEY`] || settings[id].apiKey;
                    if (apiKey) providers.push({ id, name: PROVIDERS[id].name, model: settings[id].selectedModel, apiKey, apiBase: PROVIDERS[id].apiBase });
                }
            }
            setAllProviders(providers);
            const defaultProvider = providers.find(p => p.id === settings.defaultProvider) || providers[0] || null;
            setActiveProvider(defaultProvider);
            setTranslationProvider(defaultProvider);
        };
        loadAISettings();
    }, [isOpen]);

    const streamAIResponse = useCallback(async (provider: ActiveProviderInfo, systemPrompt: string, userPrompt: string): Promise<string> => {
        // This function remains the same
        setThinkingProcess('');
        const body = { model: provider.model, messages: [{ role: 'system', content: systemPrompt }, { role: 'user', content: userPrompt }], stream: true };
        const response = await fetch(`${provider.apiBase}/chat/completions`, { method: 'POST', headers: { 'Authorization': `Bearer ${provider.apiKey}`, 'Content-Type': 'application/json' }, body: JSON.stringify(body), });
        if (!response.ok || !response.body) { const errorData = await response.json(); throw new Error(errorData.error?.message || `Request failed`); }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '', resultBuffer = '', insideThinkTag = false;
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            for (const line of lines) {
                if (!line.startsWith('data: ')) continue;
                const jsonData = line.substring(6);
                if (jsonData.trim() === '[DONE]') continue;
                try {
                    const content = JSON.parse(jsonData).choices[0]?.delta?.content || '';
                    if (!content) continue;
                    let currentContent = content;
                    if (currentContent.includes('<think>')) {
                        insideThinkTag = true;
                        const parts = currentContent.split('<think>');
                        resultBuffer += parts[0];
                        setThinkingProcess(prev => prev + parts[1]);
                    } else if (currentContent.includes('</think>')) {
                        insideThinkTag = false;
                        const parts = currentContent.split('</think>');
                        setThinkingProcess(prev => prev + parts[0]);
                        resultBuffer += parts[1];
                    } else {
                        if (insideThinkTag) setThinkingProcess(prev => prev + currentContent);
                        else resultBuffer += currentContent;
                    }
                } catch (e) { /* Ignore incomplete JSON */ }
            }
        }
        return resultBuffer;
    }, []);

    // --- MODIFIED: Added defensive checks for benefits and tags ---
    const handleTranslate = useCallback(async () => {
      const provider = useSameModel ? activeProvider : translationProvider;
      const template = payload.session.en || payload.session.fr || payload.session.es;
      if (!provider || !template) { alert("Provider or template script is missing."); return; }
  
      setIsTranslating(true);
      setThinkingProcess('');
      setTranslatedScript(null);
  
      const translatableContent: Record<string, string> = {
          title: template.title,
          description: template.description,
      };
      
      // CORRECTED: Defensively handle `benefits` and `tags` to prevent crashes.
      // If the field exists but is not an array, treat it as a single-element array.
      if (template.benefits) {
          (Array.isArray(template.benefits) ? template.benefits : [String(template.benefits)]).forEach((benefit: string, i: number) => { 
              translatableContent[`benefit_${i}`] = benefit; 
          });
      }
      if (template.tags) {
          (Array.isArray(template.tags) ? template.tags : [String(template.tags)]).forEach((tag: string, i: number) => { 
              translatableContent[`tag_${i}`] = tag; 
          });
      }
      
      template.script?.forEach((step: ScriptStep, i: number) => { 
          translatableContent[`step_${i}_text`] = step.text; 
      });

      try {
        const systemPrompt = `You are a high-quality JSON translator. The user will provide a JSON object with flat key-value pairs. Translate the STRING VALUES of this JSON object to ${SUPPORTED_LANGUAGES[payload.lang]}. Respond with a JSON object that has the IDENTICAL KEYS. Your response must be ONLY the raw, valid JSON object, without any markdown, comments, or explanations.`;
        const translatedContentJson = await streamAIResponse(provider, systemPrompt, JSON.stringify(translatableContent, null, 2));
        const translatedTexts = JSON.parse(translatedContentJson);

        const newScript = JSON.parse(JSON.stringify(template));
        newScript.title = translatedTexts.title || template.title;
        newScript.description = translatedTexts.description || template.description;

        // CORRECTED: Also use defensive checks during reconstruction.
        if (template.benefits) {
            const originalBenefits = Array.isArray(template.benefits) ? template.benefits : [String(template.benefits)];
            newScript.benefits = originalBenefits.map((_: string, i: number) => translatedTexts[`benefit_${i}`] || originalBenefits[i]);
        }
        if (template.tags) {
            const originalTags = Array.isArray(template.tags) ? template.tags : [String(template.tags)];
            newScript.tags = originalTags.map((_: string, i: number) => translatedTexts[`tag_${i}`] || originalTags[i]);
        }

        if (newScript.script) {
            newScript.script.forEach((step: ScriptStep, i: number) => {
                step.text = translatedTexts[`step_${i}_text`] || step.text;
            });
        }

        newScript.id = template.id;
        newScript.type = template.type;
        newScript.language = payload.lang;
        newScript.updatedAt = new Date().toISOString();
        delete newScript.createdAt; 

        setTranslatedScript(newScript);
      } catch (e) {
        alert(`Translation process failed: ${e instanceof Error ? e.message : String(e)}`);
      } finally {
        setIsTranslating(false);
      }
    }, [payload, activeProvider, translationProvider, useSameModel, streamAIResponse]);
      
    const handleAccept = () => {
        if (!translatedScript) return;
        const sessionWithNewTranslation = { ...payload.session, [payload.lang]: translatedScript };
        onAccept(sessionWithNewTranslation, payload.lang);
    };

    if (!isOpen) return null;
    const templateScript = payload.session.en || payload.session.fr || payload.session.es;

    return (
        <ModalBackdrop>
            <ModalContent>
                {/* JSX below this line is unchanged */}
                <ModalHeader>
                    <h3>Translate Script: {payload.session.id} to {SUPPORTED_LANGUAGES[payload.lang].toUpperCase()}</h3>
                    <CloseButton onClick={onClose}><FiX/></CloseButton>
                </ModalHeader>
                <TranslationOptionsContainer>
                    <CheckboxLabel>
                        <input type="checkbox" checked={useSameModel} onChange={() => setUseSameModel(prev => !prev)} />
                        Use default generation provider ({activeProvider?.name} - {activeProvider?.model})
                    </CheckboxLabel>
                    {!useSameModel && (
                        <FormGrid>
                            <FormControl>
                                <label>Translation Provider</label>
                                <select value={translationProvider?.id || ''} onChange={(e) => setTranslationProvider(allProviders.find(p => p.id === e.target.value) || null)}>
                                    {allProviders.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                                </select>
                            </FormControl>
                            <FormControl>
                                <label>Translation Model</label>
                                <input value={translationProvider?.model || ''} onChange={e => setTranslationProvider(p => p ? {...p, model: e.target.value} : null)} />
                            </FormControl>
                        </FormGrid>
                    )}
                </TranslationOptionsContainer>
                <div style={{textAlign: 'center', margin: '0 1rem'}}>
                    <PrimaryButton onClick={handleTranslate} disabled={isTranslating}>
                        {isTranslating ? 'Translating...' : 'Start AI Translation'}
                    </PrimaryButton>
                </div>
                {thinkingProcess && (
                    <ThinkingProcessContainer open>
                        <summary>View AI thought process...</summary><pre>{thinkingProcess}</pre>
                    </ThinkingProcessContainer>
                )}
                <TranslationGrid>
                    <div><h4>Source Script (Template)</h4><ScriptPreview>{JSON.stringify(templateScript, null, 2)}</ScriptPreview></div>
                    <div><h4>AI Translated Script (Result)</h4><ScriptPreview>{translatedScript ? JSON.stringify(translatedScript, null, 2) : (isTranslating ? 'Generating...' : 'Awaiting translation...')}</ScriptPreview></div>
                </TranslationGrid>
                <ModalFooter>
                    <AcceptButton onClick={handleAccept} disabled={!translatedScript}>
                        <FiCheckCircle/> Accept & Edit
                    </AcceptButton>
                </ModalFooter>
            </ModalContent>
        </ModalBackdrop>
    );
};

export default TranslateScriptModal;