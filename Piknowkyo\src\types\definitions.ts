// src/types/definitions.ts

export interface LocalizedString {
  en: string;
  fr: string;
  es: string;
}

// Represents a single entry in any part of the dictionary
export interface DictionaryElement {
  key: string;
  name: LocalizedString;
  description: LocalizedString;
  composition?: string[]; // Used in sentiments to define their components
  related_energetic_system?: string[]; // Used in sentiments
  // NEW: Regex keywords for searching in title, description, tags
  regex_keywords?: { 
    en?: string; // Should be a string representing a regex pattern, e.g., "/(word1|word2)/i"
    fr?: string;
    es?: string;
  };
  [key: string]: any; // Allows for additional properties like 'elements' in energetic_systems
}

// The structure of the 'dictionary' object within your definition.json
export interface Dictionary {
  basic_emotions: DictionaryElement[];
  sentiments: DictionaryElement[];
  cognitive_patterns: DictionaryElement[];
  somatic_sensations: DictionaryElement[];
  desired_outcomes: DictionaryElement[];
  sensory_channels: DictionaryElement[];
  modalities: DictionaryElement[];
  durations: DictionaryElement[];
  intensities: DictionaryElement[];
  techniques: DictionaryElement[];
  energetic_systems: DictionaryElement[];
  spiritual_concepts: DictionaryElement[];
}

// Sub-interfaces for detailed recommendation profiles
export interface EmotionalProfileTags {
  primary_emotion_drivers?: string[];
  target_sentiments?: Array<{ key: string; composition?: string[]; related_energetic_system?: string[] }>;
}

export interface ManifestationProfileTags {
  cognitive_patterns?: string[];
  somatic_sensations?: string[];
}

export interface TherapeuticProfileTags {
  desired_outcomes?: string[];
  sensory_channels_engaged?: string[];
  primary_modality?: string | string[]; // Can be string or array of strings
}

export interface SessionProfileTags {
  complexity?: string | string[];
  energy_dynamic?: string | string[];
  ideal_context?: string[];
  duration?: string | string[]; // Can be string or array of strings
  intensity?: string | string[]; // Can be string or array of strings
  techniques_used?: string[];
}

// The overall structure for recommendation metadata tags for a session
export interface SessionRecommendationTags {
  emotional_profile?: EmotionalProfileTags; // Changed from boolean to interface
  manifestation_profile?: ManifestationProfileTags; // Changed from any to interface
  therapeutic_profile?: TherapeuticProfileTags; // Changed from any to interface
  session_profile?: SessionProfileTags; // Changed from any to interface
  // These top-level arrays might still exist, but the nested profiles are now correctly typed
  addresses_emotions_or_sentiments?: string[]; 
  addresses_cognitive_patterns?: string[];
  addresses_somatic_sensations?: string[];
  addresses_behavioral_patterns?: string[];
  cultivates_outcomes?: string[];
  primary_modalities?: string[];
  techniques_used?: string[];
  ideal_for_contexts?: string[];
  addresses_energetic_states?: string[];
  metaphorical_themes?: string[];
  duration_category?: string;
  intensity_category?: string;
}

// The root structure of your definition.json file
export interface AppDefinitionsFile {
  recommendation_metadata: {
    dictionary: Dictionary;
  };
}

// NOTE: No changes to JournalEntry, SessionAudioConfig etc. as they are in models.ts