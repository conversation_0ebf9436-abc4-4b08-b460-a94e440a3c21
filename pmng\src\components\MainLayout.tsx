// pmng/src/components/MainLayout.tsx
import React from 'react';
import styled from 'styled-components';
import { NavLink, Outlet } from 'react-router-dom';
import { FiUsers, FiFileText, FiLogOut, FiCpu, FiMessageSquare, FiFilePlus } from 'react-icons/fi'; // Add FiMessageSquare
import { signOut } from 'firebase/auth';
import { auth } from '../firebase';
import type { User as FirebaseUser } from 'firebase/auth';

// ... All styled components remain the same ...
const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
`;

const Sidebar = styled.nav`
  width: 250px;
  background-color: ${({ theme }) => theme.surface};
  border-right: 1px solid ${({ theme }) => theme.border};
  padding: 20px 0;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 0 20px 20px 20px;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
  }
  p {
    margin: 5px 0 0 0;
    color: ${({ theme }) => theme.textSecondary};
    font-size: 0.8rem;
    word-break: break-all;
  }
`;

const NavList = styled.div`
  flex-grow: 1;
  padding: 20px 0;
`;

const StyledNavLink = styled(NavLink)`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  text-decoration: none;
  color: ${({ theme }) => theme.textSecondary};
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;

  &.active {
    background-color: ${({ theme }) => theme.primary}1A;
    color: ${({ theme }) => theme.primary};
    border-right: 3px solid ${({ theme }) => theme.primary};
  }

  &:hover:not(.active) {
    background-color: ${({ theme }) => theme.background};
  }
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 10px;
  border: none;
  background-color: ${({ theme }) => theme.danger}1A;
  color: ${({ theme }) => theme.danger};
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
`;

const PageContent = styled.main`
  flex-grow: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: ${({ theme }) => theme.background};
`;

const MainLayout: React.FC<{ user: FirebaseUser }> = ({ user }) => {
  return (
    <LayoutContainer>
      <Sidebar>
        <Header>
          <h2>Admin</h2>
          <p>{user.email}</p>
        </Header>
        <NavList>
          <StyledNavLink to="/users">
            <FiUsers /> User Management
          </StyledNavLink>
          <StyledNavLink to="/sessions">
            <FiFileText /> Session Management
          </StyledNavLink>
           {/* --- NEW LINK --- */}
          <StyledNavLink to="/translations">
            <FiMessageSquare /> App Translations
          </StyledNavLink>
          <StyledNavLink to="/script-generation">
            <FiFilePlus /> Script Generation
          </StyledNavLink>
          <StyledNavLink to="/ai-settings">
            <FiCpu /> AI Settings
          </StyledNavLink>
        </NavList>
        <LogoutButton onClick={() => signOut(auth)}>
            <FiLogOut /> Logout
        </LogoutButton>
      </Sidebar>
      <PageContent>
        <Outlet />
      </PageContent>
    </LayoutContainer>
  );
};

export default MainLayout;