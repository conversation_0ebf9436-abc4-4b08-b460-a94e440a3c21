// src/hooks/useAuth.ts
import { useState, useEffect } from "react";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from '../firebase';

/**
 * A clean, simple hook to get the current authentication state.
 * Its only responsibility is to report the user object and loading status from Firebase Auth.
 * It no longer creates or manages user documents in Firestore.
 */
export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // onAuthStateChanged is the real-time listener for auth state.
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      setUser(firebaseUser);
      setLoading(false);
    });

    // The cleanup function provided by onAuthStateChanged detaches the listener
    // when the component unmounts, preventing memory leaks.
    return unsubscribe;
  }, []); // The empty dependency array ensures this effect runs only once on mount.

  return { user, loading };
};