// /src/components/SubscriptionManager.tsx
import React, { useState, useEffect } from 'react';
import { useSubscription } from '../hooks/useSubscription';
import { <PERSON><PERSON>, Card } from './ui';
import { loadStripe } from '@stripe/stripe-js';
import { updateSubscription } from '../services/stripeService';
import clientEnv from '../config/clientEnvironment';
import Toast from './Toast';

const SubscriptionManager: React.FC = () => {
  const {
    subscription,
    isLoading,
    updateSubscription
  } = useSubscription();
  
  const tier = subscription?.tier || 'free';
  const isActive = subscription?.isActive || false;
  // Removed currentPeriodEnd since it's not in SubscriptionState
  const [selectedPlan, setSelectedPlan] = useState<'free' | 'premium'>('premium');
  const [toastMessage, setToastMessage] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (toastMessage) {
      const timer = setTimeout(() => {
        setToastMessage('');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [toastMessage]);
  
  const handleSubscriptionChange = async () => {
    try {
      setIsUpdating(true);
      const stripe = await loadStripe(clientEnv.stripe.publicKey);
      await updateSubscription({ tier: selectedPlan });
      setToastMessage('Subscription updated successfully');
    } catch (err) {
      console.error('Subscription update failed:', err);
      const message = err instanceof Error ? err.message : 'Unknown error';
      setToastMessage(`Failed to update subscription: ${message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) return <div>Loading subscription info...</div>;

  return (
    <div style={{ position: 'relative' }}>
      <Card title="Subscription Management">
      <div className="subscription-info">
        <p>Current Plan: {tier}</p>
        <p>Status: {isActive ? 'Active' : 'Inactive'}</p>
        {/* Removed renewal date since currentPeriodEnd isn't available */}
      </div>
      
      <div className="plan-selection">
        <h4>Change Plan</h4>
        <select
          value={selectedPlan}
          onChange={(e) => setSelectedPlan(e.target.value as 'free' | 'premium')}
        >
          <option value="free">Free</option>
          <option value="premium">Premium ($9.99/month)</option>
        </select>
        <Button
          onClick={handleSubscriptionChange}
          disabled={selectedPlan === tier || isLoading || isUpdating}
        >
          {isUpdating ? (
            <span style={{ display: 'flex', alignItems: 'center' }}>
              Updating...
              <span style={{ marginLeft: 8, display: 'inline-flex' }}>🌀</span>
            </span>
          ) : 'Update Subscription'}
        </Button>
      </div>
      
      <div className="payment-methods">
        <h4>Payment Methods</h4>
        {/* Payment method management UI would go here */}
      </div>
      </Card>
      {toastMessage && <Toast message={toastMessage} />}
    </div>
  );
};

export default SubscriptionManager;