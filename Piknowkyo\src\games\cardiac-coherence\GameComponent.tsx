// src/games/cardiac-coherence/GameComponent.tsx

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../../store/useAppStore';
import { ttsPlay, ttsStop } from '../../services/tts';
import AppIcon from '../../components/AppIcon';
import GameModal from '../common/components/GameModal';

import {
  initializeState,
  updateState,
  GamePhase,
  CardiacCoherenceState,
  GameSettings,
  ADULT_TIMINGS,
  CHILD_TIMINGS,
} from './logic';

import {
  GameContainer,
  UIContainer,
  InstructionText,
  BreathingVisualizer,
  VisualizerContainer,
  TimerDisplay,
  PauseButton,
} from './styles';

import { CardiacCoherenceSettings } from './CardiacCoherenceSetupModal';

import backgroundAdult from './assets/background-adult.webp';
import backgroundChild from './assets/background-child.webp';
import inhaleSound from './assets/inhale.mp3';
import exhaleSound from './assets/exhale.mp3';

export interface CardiacCoherenceGameProps {
  settings: CardiacCoherenceSettings;
  onGameQuit: () => void;
}

const CardiacCoherenceGame: React.FC<CardiacCoherenceGameProps> = ({ settings, onGameQuit }) => {
  const { t } = useTranslation();
  
  const ttsConfig = useAppStore(state => state.preferences?.ttsConfig);
  const binauralConfig = useAppStore(state => state.preferences?.binauralConfig);

  const [gameState, setGameState] = useState<CardiacCoherenceState>(initializeState);
  const [isPaused, setIsPaused] = useState(false);
  const [remainingTime, setRemainingTime] = useState(settings.duration * 60);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const gameLoopRef = useRef<number | null>(null);
  const lastTimeRef = useRef<number | null>(null);
  const ttsAbortController = useRef<AbortController | null>(null);
  
  const inhaleAudioRef = useRef<HTMLAudioElement>(null);
  const exhaleAudioRef = useRef<HTMLAudioElement>(null);
  const musicAudioRef = useRef<HTMLAudioElement>(null);
  const ambientAudioRef = useRef<HTMLAudioElement>(null);
  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});

  const gameSettingsRef = useRef<GameSettings>({
    duration: settings.duration * 60,
    mode: settings.mode,
    timings: settings.mode === 'adult' ? ADULT_TIMINGS : CHILD_TIMINGS,
  });

  const backgroundUrl = settings.mode === 'adult' ? backgroundAdult : backgroundChild;

  const gameLoop = useCallback((timestamp: number) => {
    if (isPaused || isModalOpen) {
      lastTimeRef.current = null; // Reset time on pause to prevent jump on resume
      return;
    }
    if (lastTimeRef.current === null) {
      lastTimeRef.current = timestamp;
    }
    const deltaTime = (timestamp - lastTimeRef.current) / 1000;
    lastTimeRef.current = timestamp;

    setGameState(prev => updateState(prev, gameSettingsRef.current, deltaTime));
    setRemainingTime(prev => Math.max(0, prev - deltaTime));

    gameLoopRef.current = requestAnimationFrame(gameLoop);
  }, [isPaused, isModalOpen]);
  
  // --- Audio Management ---
  const stopBinauralSound = useCallback(() => {
    if (audioCtxRef.current) {
      try {
        binauralOscs.current.left?.stop();
        binauralOscs.current.right?.stop();
        if (audioCtxRef.current.state !== 'closed') audioCtxRef.current.close().catch(() => {});
      } catch (e) {}
      audioCtxRef.current = null;
      binauralOscs.current = {};
    }
  }, []);

  const playBinauralSound = useCallback(() => {
    if (!binauralConfig || audioCtxRef.current?.state === 'running') return;
    stopBinauralSound();
    const Ctx = window.AudioContext || (window as any).webkitAudioContext;
    if (!Ctx) return;
    const newCtx = new Ctx();
    audioCtxRef.current = newCtx;
    const gainNode = newCtx.createGain();
    gainNode.gain.setValueAtTime(binauralConfig.volume, newCtx.currentTime);
    gainNode.connect(newCtx.destination);
    const left = newCtx.createOscillator();
    const right = newCtx.createOscillator();
    left.type = right.type = 'sine';
    left.frequency.setValueAtTime(binauralConfig.baseFrequency, newCtx.currentTime);
    right.frequency.setValueAtTime(binauralConfig.baseFrequency + binauralConfig.beatFrequency, newCtx.currentTime);
    const merger = newCtx.createChannelMerger(2);
    left.connect(merger, 0, 0); right.connect(merger, 0, 1);
    merger.connect(gainNode);
    left.start(); right.start();
    binauralOscs.current = { left, right, gain: gainNode };
  }, [binauralConfig, stopBinauralSound]);

  useEffect(() => {
    const playOrPause = (audioEl: HTMLAudioElement | null, enabled: boolean, config: {url?: string, volume?: number}) => {
      if (!audioEl) return;
      if (enabled && !isPaused && !isModalOpen && config.url && typeof config.volume === 'number') {
        if (!audioEl.src.endsWith(config.url)) audioEl.src = config.url;
        audioEl.volume = config.volume;
        audioEl.play().catch(e => { if (e.name !== "AbortError") console.error("Audio playback error:", e) });
      } else {
        audioEl.pause();
      }
    };
    playOrPause(musicAudioRef.current, settings.enableMusic, settings.musicConfig);
    playOrPause(ambientAudioRef.current, settings.enableAmbient, settings.ambientConfig);
    if (settings.enableBinaural && !isPaused && !isModalOpen) playBinauralSound();
    else stopBinauralSound();
  }, [isPaused, isModalOpen, settings, playBinauralSound, stopBinauralSound]);


  useEffect(() => {
    if (isPaused || isModalOpen || gameState.phase === GamePhase.Ready || gameState.phase === GamePhase.Finished) {
        ttsStop(ttsAbortController.current);
        return;
    };

    let textToSpeak = '';
    let soundToPlay: HTMLAudioElement | null = null;
    
    switch (gameState.phase) {
      case GamePhase.Inhale: textToSpeak = t('games.cardiacCoherence.inhale'); soundToPlay = inhaleAudioRef.current; break;
      case GamePhase.Exhale: textToSpeak = t('games.cardiacCoherence.exhale'); soundToPlay = exhaleAudioRef.current; break;
      case GamePhase.HoldIn: case GamePhase.HoldOut: textToSpeak = t('games.cardiacCoherence.hold'); break;
    }

    if (textToSpeak) {
        ttsAbortController.current = new AbortController();
        if (settings.useTTS && ttsConfig) {
          ttsPlay(ttsConfig.provider, textToSpeak, ttsConfig.voice, ttsConfig.lang, { signal: ttsAbortController.current.signal })
            .catch(err => { if (err.name !== 'AbortError') console.error("TTS Error:", err); });
        }
    }
    if (soundToPlay && settings.useSoundEffects) {
      soundToPlay.currentTime = 0;
      soundToPlay.play().catch(e => { if (e.name !== "AbortError") console.error("SFX Error:", e) });
    }
  }, [gameState.phase, isPaused, isModalOpen]);

  useEffect(() => {
    gameLoopRef.current = requestAnimationFrame(gameLoop);
    return () => { if (gameLoopRef.current) cancelAnimationFrame(gameLoopRef.current); };
  }, [gameLoop]);

  useEffect(() => {
    if (remainingTime <= 0 && gameState.phase !== GamePhase.Finished) {
      setGameState(prev => ({...prev, phase: GamePhase.Finished}));
      ttsStop(ttsAbortController.current);
      setIsModalOpen(true); // Open modal on finish
    }
  }, [remainingTime, gameState.phase]);
  
  const handlePause = () => { setIsPaused(true); setIsModalOpen(true); };
  const handleResume = () => { setIsPaused(false); setIsModalOpen(false); };
  const handleRestart = () => {
    setIsModalOpen(false);
    setIsPaused(true);
    setGameState(initializeState());
    setRemainingTime(settings.duration * 60);
    setIsPaused(false);
  };
  
  // Determine instruction text based on the game phase
  let instructionKey: string;
  switch(gameState.phase) {
    case GamePhase.Inhale:
      instructionKey = 'games.cardiacCoherence.inhale';
      break;
    case GamePhase.Exhale:
      instructionKey = 'games.cardiacCoherence.exhale';
      break;
    case GamePhase.HoldIn:
    case GamePhase.HoldOut:
      instructionKey = 'games.cardiacCoherence.hold';
      break;
    case GamePhase.Finished:
      instructionKey = 'games.cardiacCoherence.finished';
      break;
    default: // Ready phase
      instructionKey = 'games.cardiacCoherence.getReady';
      break;
  }
  
  // --- Breathing Visualizer Animation ---
  // The vertical distance the ball will travel, in viewport height units (vh).
  // This provides a responsive travel distance based on screen height.
  const travelDistanceVh = 45; 
  
  // Calculate the Y position based on the game's progress (0.0 to 1.0).
  // progress=0 (fully exhaled/bottom) => translateY(0)
  // progress=1 (fully inhaled/top) => translateY(-45vh)
  const yPositionVh = -gameState.progress * travelDistanceVh;

  const minutes = Math.floor(remainingTime / 60);
  const seconds = Math.floor(remainingTime % 60);

  return (
    <>
      <GameContainer $backgroundUrl={backgroundUrl}>
        <UIContainer>
          <InstructionText key={instructionKey}>{t(instructionKey)}</InstructionText>
          <VisualizerContainer>
            <BreathingVisualizer style={{ transform: `translateY(${yPositionVh}vh)` }} />
          </VisualizerContainer>
        </UIContainer>
        <TimerDisplay>{String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}</TimerDisplay>
        {gameState.phase !== GamePhase.Finished && (
            <PauseButton onClick={handlePause} title={t('actions.pause')}>
                <AppIcon name={"pause"} size={30} />
            </PauseButton>
        )}
      </GameContainer>

      <GameModal
        isOpen={isModalOpen}
        title={gameState.phase === GamePhase.Finished ? t('games.cardiacCoherence.finishedTitle') : t('game.modal.pausedTitle')}
        showResumeButton={isPaused && gameState.phase !== GamePhase.Finished}
        showRestartButton={isPaused && gameState.phase !== GamePhase.Finished}
        showReturnButton={true}
        onResume={handleResume}
        onRestart={handleRestart}
        onReturn={onGameQuit}
      >
        <p>{gameState.phase === GamePhase.Finished ? t('games.cardiacCoherence.finishedMessage') : t('game.modal.pausedMessage')}</p>
      </GameModal>

      <audio ref={musicAudioRef} loop />
      <audio ref={ambientAudioRef} loop />
      <audio ref={inhaleAudioRef} src={inhaleSound} preload="auto" />
      <audio ref={exhaleAudioRef} src={exhaleSound} preload="auto" />
    </>
  );
};

export default CardiacCoherenceGame;