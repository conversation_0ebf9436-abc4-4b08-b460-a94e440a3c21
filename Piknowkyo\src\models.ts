import { Language } from './LangProvider';

// --- Existing Interfaces ---

export interface SessionAudioConfig {
  music?: {
    url: string;
    volume?: number;
  };
  ambient?: {
    url: string;
    volume?: number;
  };
  binaural?: {
    volume?: number; // Common volume
    
    // For app-generated binaural beats
    baseFreq?: number;  // Base frequency (e.g., for the left ear)
    beatFreq?: number;  // Desired beat frequency (difference between ears)
                        // targetFreq (for the right ear) will typically be baseFreq + beatFreq
    
  };
  voice?: {
    volume?: number;
    gender?: 'masculine' | 'feminine' | 'neutral';
  };
  enableMusic?: boolean;
  enableAmbient?: boolean;
  enableBinaural?: boolean; // This toggle is important to know if we should generate/play binaurals
}

export interface AudioAsset {
  id: string;
  name: string;
  url: string;
  type: 'music' | 'ambient';
  isUserUploaded?: boolean;
  userId?: string;
  storagePath?: string;
  size?: number; // File size in bytes (optional)
  createdAt?: Date; // Creation/upload date (optional)
}

export interface ScriptLine {
  text: string;
  duration?: number; 
  pause?: number;    
  speaker?: string;  
  rate?: number;     
  pitch?: number;    
}

export interface Session {
  id: string;
  title: string;
  description: string;
  type: string; 
  category: string; 
  language: Language; 
  imageUrl?: string; 
  durationMinutes?: number; 
  estimatedDuration?: number; 
  rating?: number;
  tags?: string[];
  benefits?: string[];
  comments?: string[]; 
  audio?: SessionAudioConfig; 
  script?: ScriptLine[]; 
  createdAt?: string;
  updatedAt?: string;
}

export interface AudioManifest {
  musics: AudioAsset[];
  ambiants: AudioAsset[];
}

export type JournalEntry = {
  id: string;
  sessionId: string;
  date: string;
  note: string;
  mood: 'happy' | 'neutral' | 'sad' | 'energized' | 'relaxed';
};

export interface SessionManifestEntry {
  [x: string]: any;
  id: string;
  title: string;
  type: string;
  estimatedDuration: number;
  tags: string[];
  imageUrl?: string;
  isPremium?: boolean;
};

export interface CompletedSession {
  id: string; // Unique ID for this specific completion event
  sessionId: string; // ID of the session that was completed
  completedAt: string; // ISO 8601 timestamp of when it was completed
}


// --- NEWLY ADDED FOR BLOG FEATURE ---

/**
 * Represents a single post in the community blog.
 */
export interface BlogPost {
  id: string;
  authorId: string;
  authorPseudo: string; // Anonymized pseudo
  content: string;
  category: string;
  tags: string[];
  createdAt: string; // ISO 8601 timestamp for Redux serialization
  likes: string[]; // Array of user UIDs who liked the post
  commentCount: number;
  // An optional flag for offline-first logic
  isSynced?: boolean; 
}

/**
 * Represents a single comment on a blog post.
 */
export interface BlogComment {
  id: string;
  postId: string; // ID of the parent post
  authorId: string;
  authorPseudo: string; // Anonymized pseudo
  text: string;
  createdAt: string; // ISO 8601 timestamp
  // An optional flag for offline-first logic
  isSynced?: boolean;
}