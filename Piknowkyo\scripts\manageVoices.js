// scripts/manageVoices.js (Version Finale avec Nom de Bucket Explicite)

import 'dotenv/config';
import { TextToSpeechClient } from '@google-cloud/text-to-speech';
import { Storage } from '@google-cloud/storage';
import fs from 'fs/promises';
import path from 'path';
import inquirer from 'inquirer';
import { fileURLToPath } from 'url';

// --- CONFIGURATION ---
const FIREBASE_PROJECT_ID = 'piknowkyo-777'; // <--- VÉRIFIEZ QUE CECI EST VOTRE ID DE PROJET

// CORRECTION MAJEURE: Mettez ici le nom EXACT de votre bucket, trouvé dans la console Firebase.
// Il ressemble à 'votre-projet-id.appspot.com' mais peut être différent.
const FIREBASE_STORAGE_BUCKET = 'gs://piknowkyo-777.firebasestorage.app/'; // <--- REMPLACEZ CECI PAR LE VRAI NOM

const STORAGE_DESTINATION_DIR = 'tts_previews';
const SUPPORTED_LANGUAGES = ['en', 'fr', 'es'];
const SAMPLE_TEXTS = {
  en: 'Hello, you can use my voice for your audio sessions.',
  fr: 'Bonjour, vous pouvez utiliser ma voix pour vos sessions audio.',
  es: 'Hola, puedes usar mi voz para tus sesiones de audio.',
};

// --- FIN DE LA CONFIGURATION ---


// --- INITIALISATION ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const LOCAL_PREVIEWS_DIR = path.join(__dirname, 'voice-previews-standard');
const ttsClient = new TextToSpeechClient();
const storage = new Storage({ projectId: FIREBASE_PROJECT_ID });
// On utilise directement la variable de configuration, plus de suppositions.
const bucket = storage.bucket(FIREBASE_STORAGE_BUCKET);
let cachedVoices = null;

function getTier(voiceName) {
    if (voiceName.includes('-Studio-')) return 'Studio';
    if (voiceName.includes('-Wavenet-')) return 'WaveNet';
    if (/-Chirp/.test(voiceName)) return 'Chirp';
    return 'Standard';
}


// --- SECTION 1: LISTER LES VOIX ---
async function getAndCacheVoices() {
  if (cachedVoices) return cachedVoices;
  
  console.log('Fetching all available voices from Google Cloud API...');
  try {
    const [result] = await ttsClient.listVoices({});
    cachedVoices = result.voices
      .filter(voice => SUPPORTED_LANGUAGES.some(lang => voice.languageCodes[0].startsWith(lang)))
      .filter(voice => getTier(voice.name) === 'Standard');

    console.log(`Found ${cachedVoices.length} Standard voices for supported languages.`);
    return cachedVoices;
  } catch (error) {
    console.error('Error fetching voices:', error.message);
    throw error;
  }
}

async function listVoices() {
  console.log('\n--- Listing Standard Voices Only ---');
  try {
    const voices = await getAndCacheVoices();
    const formattedVoices = voices.map(v => ({
      "Nom de la Voix": v.name,
      "Langue": v.languageCodes[0],
      "Genre": v.ssmlGender,
      "Niveau": "Standard" 
    }));
    console.table(formattedVoices);
  } catch (e) {
    console.error('Could not list voices. Please check your API key and permissions.');
  }
}


// --- SECTION 2: TÉLÉCHARGER LES APERÇUS MP3 ---
async function downloadPreviews() {
  console.log('\n--- Downloading MP3 Previews for Standard Voices ---');
  await fs.mkdir(LOCAL_PREVIEWS_DIR, { recursive: true });
  
  const voices = await getAndCacheVoices();
  if (!voices || voices.length === 0) {
    console.log('No voices to process. Exiting download task.');
    return;
  }

  let successCount = 0;
  let failCount = 0;

  for (const [index, voice] of voices.entries()) {
    const voiceName = voice.name;
    const langCode = voice.languageCodes[0];
    const sampleText = SAMPLE_TEXTS[langCode.substring(0, 2)];
    const localFilePath = path.join(LOCAL_PREVIEWS_DIR, `${voiceName}.mp3`);
    
    console.log(`[${index + 1}/${voices.length}] Processing ${voiceName}...`);

    try {
      const [response] = await ttsClient.synthesizeSpeech({
        input: { text: sampleText },
        voice: { name: voiceName, languageCode: langCode },
        audioConfig: { audioEncoding: 'MP3' },
      });
      await fs.writeFile(localFilePath, response.audioContent, 'binary');
      console.log(`  -> Saved to ${localFilePath}`);
      successCount++;
    } catch (err) {
      console.error(`  -> FAILED for ${voiceName}: ${err.message}`);
      failCount++;
    }
  }

  console.log(`\nDownload complete. Success: ${successCount}, Failed: ${failCount}.`);
}


// --- SECTION 3: UPLOADER SUR FIREBASE STORAGE ---
async function uploadPreviews() {
  console.log('\n--- Uploading Previews to Firebase Storage ---');
  
  try {
    const files = await fs.readdir(LOCAL_PREVIEWS_DIR);
    const mp3Files = files.filter(f => f.endsWith('.mp3'));

    if (mp3Files.length === 0) {
      console.log(`No local MP3 previews found in \`${LOCAL_PREVIEWS_DIR}\`. Run Download first.`);
      return;
    }

    console.log(`Found ${mp3Files.length} MP3 files to upload.`);
    let successCount = 0;
    let failCount = 0;
    
    for (const [index, file] of mp3Files.entries()) {
      const localFilePath = path.join(LOCAL_PREVIEWS_DIR, file);
      const destination = `${STORAGE_DESTINATION_DIR}/${file}`;
      console.log(`[${index + 1}/${files.length}] Uploading ${file}...`);

      try {
        const [uploadedFile] = await bucket.upload(localFilePath, { destination });
        await uploadedFile.makePublic();
        console.log(`  -> Upload complete: ${uploadedFile.publicUrl()}`);
        successCount++;
      } catch (err) {
        console.error(`  -> FAILED to upload ${file}: ${err.message}`);
        failCount++;
      }
    }

    console.log(`\nUpload complete. Success: ${successCount}, Failed: ${failCount}.`);
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.error(`Error: The \`${LOCAL_PREVIEWS_DIR}\` directory does not exist. Run Download first.`);
    } else {
      console.error('An error occurred during upload:', error.message);
    }
  }
}


// --- MENU PRINCIPAL ---
function showWelcome() {
    console.log(`
  ____  _  __ _   _  _  _  _  __   __  __  _  _ 
 (  _ \\( \\(  ( \\ / )( \\/ )( \\/ ) /  \\(  )/ )( \\
  ) _ < )  )/    \\) \\ \\/ / )  ( (  O ))( \\ \\/ /
 (____/(_)\\_)__)(__/  \\__/ (_/\\_) \\__/(__) \\__/ 
    `);
  console.log('====================================================');
  console.log('      Voice Preview Management Script (Standard Only)');
  console.log('====================================================\n');
}

async function startMenu() {
  showWelcome();

  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'What would you like to do?',
      choices: [
        { name: '1. List All Available Standard Voices', value: 'list' },
        { name: '2. Download MP3 Previews for All Standard Voices', value: 'download' },
        { name: '3. Upload All Local MP3s to Firebase Storage', value: 'upload' },
        new inquirer.Separator(),
        { name: 'Exit', value: 'exit' },
      ],
    },
  ]);

  switch (action) {
    case 'list':
      await listVoices();
      break;
    case 'download':
      await downloadPreviews();
      break;
    case 'upload':
      await uploadPreviews();
      break;
    case 'exit':
      console.log('Exiting script. Goodbye!');
      return;
  }

  const { nextAction } = await inquirer.prompt([
    {
        type: 'list',
        name: 'nextAction',
        message: 'Done. What next?',
        choices: ['Return to main menu', 'Exit']
    }
  ]);

  if(nextAction === 'Return to main menu') {
      console.clear();
      startMenu();
  } else {
      console.log('Exiting script. Goodbye!');
  }
}

startMenu();