import React from 'react';
import styled from 'styled-components';
import { FiRotateCcw, FiSmartphone } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';

const HintContainer = styled.div`
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  z-index: 20;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  @media (min-width: 769px) {
    display: none;
  }

  @media (orientation: landscape) {
    display: none;
  }
`;

const OrientationHint: React.FC = () => {
  const { t } = useTranslation();

  return (
    <HintContainer>
      <FiSmartphone />
      <FiRotateCcw />
      <span>{t('game.orientationHint')}</span>
    </HintContainer>
  );
};

export default OrientationHint;
