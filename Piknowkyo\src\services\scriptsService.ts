// src/services/scriptsService.ts

import { collection, getDocs, query } from 'firebase/firestore';
import CryptoJS from 'crypto-js';
import { db } from '../firebase';
import { SessionScript } from '../store/useAppStore'; // Reuse the type from our store

const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
if (!SHARED_KEY) {
  throw new Error("VITE_SHARED_ENCRYPTION_KEY is not defined in .env file for scriptsService.");
}

/**
 * Fetches and decrypts all session scripts for a given language and category.
 * @param lang The language code (e.g., 'en', 'fr').
 * @param category The category of scripts to fetch.
 * @returns A promise that resolves to an array of SessionScript objects.
 */
export const fetchScriptsByCategory = async (
  lang: string, 
  category: string
): Promise<SessionScript[]> => {
  if (!lang || !category) {
    console.warn('fetchScriptsByCategory called with invalid lang or category.');
    return [];
  }

  try {
    const scriptsCollectionRef = collection(db, 'SessionScripts', lang, category);
    const q = query(scriptsCollectionRef);
    const querySnapshot = await getDocs(q);

    const scripts: SessionScript[] = [];

    querySnapshot.forEach((doc) => {
      if (doc.data().encrypted) {
        try {
          const decryptedBytes = CryptoJS.AES.decrypt(doc.data().encrypted, SHARED_KEY);
          const decryptedJson = decryptedBytes.toString(CryptoJS.enc.Utf8);
          
          if (decryptedJson) {
            // We spread the data to ensure it conforms to the SessionScript type
            const sessionData = JSON.parse(decryptedJson);
            scripts.push({
              id: sessionData.id,
              type: sessionData.type,
              title: sessionData.title,
              description: sessionData.description,
              duration: sessionData.duration,
            });
          }
        } catch (error) {
          console.error(`Failed to decrypt script ${doc.id} in category ${category}:`, error);
        }
      }
    });

    return scripts;

  } catch (error) {
    console.error(`Error fetching scripts for category ${category}:`, error);
    // Return an empty array in case of a fetching error to prevent app crashes.
    return [];
  }
};