# PiKnowKyo - Votre Chemin vers l'Éveil et l'Harmonie Intérieure

<div align="center">
  <p><i>Éveillez votre potentiel. Libérez votre esprit. Transformez votre vie.</i></p>
</div>

## 📱 À propos de PiKnowKyo

PiKnowKyo est une application de bien-être holistique multi-plateforme (Android, iOS, Web) conçue pour accompagner les utilisateurs dans leur voyage vers l'équilibre intérieur et l'épanouissement personnel. L'application propose une gamme complète d'outils de développement personnel, d'hypnose, de méditation et d'exercices de PNL, le tout dans une expérience utilisateur fluide et personnalisable.

## 🚀 Vision du Projet

Notre mission est de créer une application accessible qui fusionne science, spiritualité et technologie pour offrir des outils de transformation personnelle puissants. PiKnowKyo se distingue par son approche holistique, sa personnalisation poussée et son fonctionnement hors-ligne, permettant à chacun d'accéder à des ressources de qualité pour son développement personnel, où qu'il soit.

## 🛠️ Architecture Technique

### Stack Technologique

- **Frontend**:
  - React Native avec TypeScript
  - Expo pour le développement multi-plateforme (Android, iOS, Web)
  - React Navigation pour la navigation entre écrans
  - Redux Toolkit pour la gestion d'état
  - Styled Components pour le styling

- **Backend**:
  - Firebase (Firestore, Authentication, Storage, Functions)
  - Capacité de fonctionnement hors-ligne avec synchronisation

- **Synthèse Vocale**:
  - Intégration de Piper TTS pour la synthèse vocale hors-ligne
  - Support pour d'autres moteurs TTS (système natif, cloud)
  - Personnalisation avancée des voix

- **Audio**:
  - React Native Track Player pour la gestion audio
  - Web Audio API pour la génération de sons binauraux en temps réel
  - Support pour les sons binauraux et ambients
  - Mixage audio multi-pistes avec contrôle individuel des volumes
  - Générateur de battements binauraux configurable (fréquence, état cérébral)

- **Stockage Local**:
  - SQLite / Realm pour le stockage des données hors-ligne
  - AsyncStorage pour les préférences utilisateur

### Architecture Multi-Plateforme

```
src/
├── api/                 # Services d'API et intégrations Firebase
├── assets/              # Ressources statiques (images, sons, etc.)
├── components/          # Composants UI réutilisables
├── contexts/            # Contextes React (thème, langue, etc.)
├── hooks/               # Hooks personnalisés
├── i18n/                # Internationalisation
├── navigation/          # Configuration de la navigation
├── screens/             # Écrans de l'application
├── services/            # Services (audio, TTS, offline, etc.)
│   ├── audio/           # Gestion audio (player, mixage, etc.)
│   ├── tts/             # Services de synthèse vocale
│   │   ├── piper/       # Intégration Piper TTS
│   │   └── providers/   # Autres fournisseurs TTS
│   └── offline/         # Gestion du mode hors-ligne
├── store/               # État global (Redux)
├── types/               # Types TypeScript
└── utils/               # Utilitaires
```

## 🔄 Stratégie de Développement

### Phase 1: Fondations (2-3 mois)
- Configuration du projet React Native avec TypeScript et Expo
- Mise en place de l'architecture de base et des composants UI
- Intégration de Firebase (Auth, Firestore)
- Développement du système de navigation et de l'état global
- Implémentation du système de thèmes (clair/sombre) et multi-langue
- Mise en place de la structure des assets (scripts, musique, sons ambiants)

### Phase 2: Fonctionnalités Core (3-4 mois)
- Développement du lecteur audio avec support pour musique, sons ambiants et binauraux
- Intégration de Piper TTS et autres moteurs de synthèse vocale
- Création des écrans principaux (accueil, bibliothèque, profil)
- Implémentation des séances de base (hypnose, méditation, affirmations)
- Système de personnalisation des séances
- Développement du moteur de lecture des scripts JSON
- Implémentation du générateur de sons binauraux avec préréglages (chakras, états cérébraux)

### Phase 3: Mode Hors-ligne & Synchronisation (2-3 mois)
- Implémentation du stockage local avec SQLite/Realm
- Développement de la logique de synchronisation avec Firebase
- Optimisation des ressources audio pour utilisation hors-ligne
- Système de téléchargement et gestion des scripts et assets
- Tests de performance et optimisations

### Phase 4: Fonctionnalités Avancées (3-4 mois)
- Système de journal personnel et statistiques
- Questionnaires de personnalisation et suggestions
- Mini-jeux thérapeutiques
- Blog communautaire et système de partage
- Intégration des paiements et abonnements
- Éditeur de scripts personnalisés pour les utilisateurs premium

### Phase 5: Déploiement & Optimisation (1-2 mois)
- Tests utilisateurs et corrections de bugs
- Optimisation des performances sur toutes les plateformes
- Préparation des stores (Google Play, App Store)
- Déploiement de la version web
- Mise en place d'un système de mise à jour des scripts et assets

## 🔌 Fonctionnalités Techniques Clés

### Mode Hors-ligne Avancé
L'application est conçue pour fonctionner entièrement hors-ligne, avec synchronisation intelligente lors de la reconnexion. Les données essentielles et les ressources audio sont téléchargées et stockées localement.

```typescript
// Exemple de service de synchronisation
class SyncService {
  async syncUserData(userId: string): Promise<void> {
    const lastSync = await AsyncStorage.getItem('lastSyncTimestamp');
    const changes = await this.getLocalChanges(lastSync);

    if (navigator.onLine) {
      await this.pushChangesToFirebase(changes);
      await this.pullChangesFromFirebase(lastSync);
      await AsyncStorage.setItem('lastSyncTimestamp', Date.now().toString());
    }
  }
  // ...
}
```

### Gestion des Scripts et Assets
L'application utilise un système de scripts JSON structurés pour toutes les séances (hypnose, méditation, affirmations, etc.). Ces scripts sont organisés par langue et type, permettant une gestion flexible et évolutive du contenu.

```typescript
// Structure d'un script de séance
interface Session {
  id: string;
  title: string;
  description: string;
  benefits: string;
  durationMinutes: number;
  type: 'hypnosis' | 'meditation' | 'affirmation' | 'story' | 'training' | 'custom';
  language: 'fr' | 'en' | 'es';
  isPremium: boolean;
  price: number;
  createdAt: string;
  isNew: boolean;
  tags: string[];
  script: ScriptSegment[];
  optimizationModel?: string;
}

interface ScriptSegment {
  text: string;
  pause: number; // Durée de pause en millisecondes
  pitch: number; // Hauteur de la voix (0.5 à 2.0)
  rate: number;  // Vitesse de la voix (0.5 à 2.0)
}

// Exemple de chargement d'un script
class ScriptService {
  async loadScript(language: string, scriptId: string): Promise<Session> {
    try {
      // Chargement depuis le stockage local d'abord (pour le mode hors-ligne)
      const localScript = await this.loadFromLocalStorage(`scripts/${language}/${scriptId}.json`);
      if (localScript) return localScript;

      // Si non disponible localement, charger depuis Firebase
      const remoteScript = await this.loadFromFirebase(`scripts/${language}/${scriptId}`);
      if (remoteScript) {
        // Sauvegarder localement pour utilisation hors-ligne future
        await this.saveToLocalStorage(`scripts/${language}/${scriptId}.json`, remoteScript);
        return remoteScript;
      }

      throw new Error(`Script ${scriptId} not found`);
    } catch (error) {
      console.error('Error loading script:', error);
      throw error;
    }
  }
  // ...
}
```

### Organisation des Assets
Les assets sont organisés en trois catégories principales :

```
assets/
├── scripts/           # Scripts JSON des séances
│   ├── fr/            # Scripts en français
│   ├── en/            # Scripts en anglais
│   └── es/            # Scripts en espagnol
├── music/             # Musiques de fond pour les séances
└── ambient/           # Sons ambiants (nature, white noise, etc.)
```

Chaque script JSON contient toutes les informations nécessaires pour une séance, y compris le texte, les pauses, et les paramètres de voix. Cette architecture permet d'ajouter facilement de nouveaux scripts sans modifier le code de l'application.

### Synthèse Vocale Multi-fournisseur
L'application intègre plusieurs moteurs TTS, avec une préférence pour les solutions hors-ligne comme Piper TTS.

```typescript
// Exemple d'architecture TTS
interface TTSProvider {
  speak(text: string, options: TTSOptions): Promise<void>;
  getVoices(): Promise<Voice[]>;
}

class PiperTTSProvider implements TTSProvider {
  // Implémentation pour Piper TTS
}

class NativeTTSProvider implements TTSProvider {
  // Implémentation pour le TTS natif du système
}

class TTSService {
  private provider: TTSProvider;

  constructor(preferredProvider: string = 'piper') {
    this.provider = this.getProvider(preferredProvider);
  }

  private getProvider(name: string): TTSProvider {
    switch (name) {
      case 'piper': return new PiperTTSProvider();
      case 'native': return new NativeTTSProvider();
      default: return new PiperTTSProvider();
    }
  }

  // Méthodes publiques
}
```

### Système Audio Avancé
Gestion sophistiquée de l'audio avec mixage en temps réel pour combiner voix, musique de fond, sons ambiants et binauraux.

```typescript
// Exemple de service audio
class AudioMixerService {
  private tracks: {
    voice: Track;
    music: Track;
    ambient: Track;
    binaural: Track;
  };

  async setupSession(sessionConfig: SessionAudioConfig): Promise<void> {
    // Configurer les pistes audio
    this.tracks.voice = await this.createTrack(sessionConfig.voiceUrl, sessionConfig.voiceVolume);
    this.tracks.music = await this.createTrack(sessionConfig.musicUrl, sessionConfig.musicVolume);
    this.tracks.ambient = await this.createTrack(sessionConfig.ambientUrl, sessionConfig.ambientVolume);

    // Générer la piste binaurale en temps réel
    this.tracks.binaural = await this.createBinauralTrack({
      baseFrequency: sessionConfig.binauralBaseFrequency,
      targetState: sessionConfig.binauralTargetState,
      volume: sessionConfig.binauralVolume
    });
  }

  async play(): Promise<void> {
    // Démarrer la lecture synchronisée
    await Promise.all([
      this.tracks.voice.play(),
      this.tracks.music.play(),
      this.tracks.ambient.play(),
      this.tracks.binaural.play()
    ]);
  }
  // ...
}
```

### Générateur de Sons Binauraux
L'application intègre un générateur sophistiqué de sons binauraux qui permet de créer des expériences auditives personnalisées pour chaque séance. Les sons binauraux sont générés en temps réel par l'application, offrant une flexibilité totale dans la configuration.

#### Configuration des Sons Binauraux

```typescript
// Interface de configuration des sons binauraux
interface BinauralConfig {
  baseFrequency: number;        // Fréquence de base en Hz
  targetState: BrainwaveState;  // État cérébral cible
  volume: number;               // Volume (0.0 à 1.0)
  waveform?: 'sine' | 'square' | 'triangle' | 'sawtooth'; // Forme d'onde (optionnel)
}

// États cérébraux disponibles
enum BrainwaveState {
  DELTA = 'delta',     // 0.5-4 Hz - Sommeil profond, guérison
  THETA = 'theta',     // 4-8 Hz - Méditation profonde, créativité
  ALPHA = 'alpha',     // 8-14 Hz - Relaxation, calme
  BETA = 'beta',       // 14-30 Hz - Concentration, vigilance
  GAMMA = 'gamma'      // 30-100 Hz - Cognition supérieure, traitement d'information
}

// Exemple de service de génération binaurale
class BinauralGenerator {
  // Génère un son binaural basé sur la configuration
  generateBinauralTone(config: BinauralConfig): AudioBuffer {
    const { baseFrequency, targetState, volume, waveform = 'sine' } = config;

    // Calculer la fréquence de battement en fonction de l'état cérébral cible
    const beatFrequency = this.getBeatFrequencyForState(targetState);

    // Générer deux tonalités légèrement différentes pour créer l'effet binaural
    // Par exemple, si baseFrequency = 200 Hz et beatFrequency = 10 Hz:
    // - Canal gauche: 200 Hz
    // - Canal droit: 210 Hz
    // Le cerveau perçoit un battement de 10 Hz

    return this.createStereoTones(baseFrequency, baseFrequency + beatFrequency, volume, waveform);
  }

  private getBeatFrequencyForState(state: BrainwaveState): number {
    switch (state) {
      case BrainwaveState.DELTA: return this.getRandomInRange(0.5, 4);
      case BrainwaveState.THETA: return this.getRandomInRange(4, 8);
      case BrainwaveState.ALPHA: return this.getRandomInRange(8, 14);
      case BrainwaveState.BETA: return this.getRandomInRange(14, 30);
      case BrainwaveState.GAMMA: return this.getRandomInRange(30, 50);
    }
  }

  private getRandomInRange(min: number, max: number): number {
    return Math.random() * (max - min) + min;
  }

  // Autres méthodes pour la génération audio...
}
```

#### Fréquences des Chakras et Organes
L'application propose une sélection de fréquences prédéfinies basées sur les chakras et les organes du corps, permettant aux utilisateurs de cibler des zones spécifiques pour la guérison et l'équilibre.

```typescript
// Fréquences des chakras
// --- Fréquences des Chakras (Solfeggio Sacré et associées) ---
enum ChakraFrequency {
  // Fréquences Solfeggio principales traditionnellement associées aux chakras
  ROOT = 396,          // Chakra racine (Muladhara) - Libération de la peur et de la culpabilité, Sécurité, Stabilité
  SACRAL = 417,        // Chakra sacré (Svadhisthana) - Facilitation du changement, Créativité, Sexualité
  SOLAR_PLEXUS = 528,  // Plexus solaire (Manipura) - Transformation et Miracles (Réparation ADN), Confiance, Pouvoir personnel
  HEART = 639,         // Chakra du cœur (Anahata) - Connexion, Relations, Amour, Compassion
  THROAT = 741,        // Chakra de la gorge (Vishuddha) - Expression de soi, Résolution de problèmes, Communication
  THIRD_EYE = 852,     // Troisième œil (Ajna) - Retour à l'ordre spirituel, Intuition, Clairvoyance
  CROWN = 963,         // Chakra couronne (Sahasrara) - Éveil de la conscience divine, Connexion spirituelle, Illumination

  // Fréquences Solfeggio additionnelles parfois incluses
  LOWER_EARTH_STAR = 174, // Chakra Étoile de la Terre (sous les pieds) - Ancrage profond, Soulagement de la douleur physique et karmique
  SOUL_STAR = 285,        // Chakra Étoile de l'Âme (au-dessus de la Couronne) - Restauration des tissus, Cognition quantique, Connexion à l'âme
}

// --- Fréquences des Organes et Systèmes Corporels (Indicatives, issues de diverses sources alternatives) ---
enum OrganAndSystemFrequency {
  // Celles que vous aviez déjà
  LIVER = 95,                // Foie - Détoxification (Note: d'autres sources indiquent 317.83 Hz)
  KIDNEYS = 319,             // Reins - Filtration (Note: d'autres sources indiquent 319.88 Hz ou 324 Hz)
  LUNGS = 220,               // Poumons - Respiration (Note: d'autres sources indiquent 120 Hz ou 220 Hz pour les problèmes pulmonaires)
  HEART_ORGAN = 128,         // Cœur (organe) - Circulation, Équilibre émotionnel (Note: ne pas confondre avec le chakra du cœur)
  PINEAL_GLAND = 480,        // Glande pinéale - Mélatonine, Sommeil, Intuition (Note: proche de 852 Hz du 3ème œil aussi)
  THYROID = 295,             // Thyroïde - Métabolisme (Note: d'autres sources indiquent 160 Hz ou 763 Hz)
  ADRENALS = 492,            // Glandes surrénales - Énergie, Réponse au stress

  // Ajouts
  BRAIN = 315.8,             // Cerveau (général) - Activité mentale, Concentration
  STOMACH = 110,             // Estomac - Digestion
  PANCREAS = 117,            // Pancréas - Régulation de la glycémie, Enzymes digestives
  SPLEEN = 380,              // Rate - Système immunitaire, Filtration du sang
  GALLBLADDER = 300,         // Vésicule biliaire - Stockage de la bile, Digestion des graisses
  SMALL_INTESTINE = 281,     // Intestin grêle - Absorption des nutriments
  LARGE_INTESTINE = 176,     // Gros intestin - Absorption de l'eau, Élimination
  BLADDER = 352,             // Vessie - Stockage et élimination de l'urine
  BONES = 40,                // Os - Croissance, Réparation (certaines sources indiquent 7 Hz pour la stimulation de la croissance osseuse)
  MUSCLES = 132,             // Muscles - Tonus, Relaxation, Réparation
  SKIN = 20,                 // Peau - Régénération, Guérison (fréquence basse souvent utilisée en thérapie par micro-courant)
  BLOOD = 50,                // Sang - Circulation, Oxygénation (valeur générale, des fréquences spécifiques existent pour des conditions sanguines)
  IMMUNE_SYSTEM_GENERAL = 8, // Système immunitaire (général, proche des ondes Alpha/Theta) - Renforcement
  NERVOUS_SYSTEM = 7.83,     // Système nerveux - Équilibre, Calme (Résonance de Schumann fondamentale)
  LYMPHATIC_SYSTEM = 15,     // Système lymphatique - Drainage, Détoxification
  PITUITARY_GLAND = 636,     // Glande pituitaire (hypophyse) - Régulation hormonale
  THYMUS = 384,              // Thymus - Fonction immunitaire, Production de cellules T
}

// --- Fréquences Planétaires (Octave Cosmique par Hans Cousto) ---
// Ces fréquences sont calculées en transposant les périodes orbitales des planètes dans le spectre audible.
enum PlanetaryFrequency {
  SUN = 126.22,              // Soleil - Vitalité, Force vitale (souvent associé au plexus solaire)
  MOON_SYNODIC = 210.42,     // Lune (synodique) - Émotions, Cycles féminins (souvent associé au chakra sacré)
  EARTH_DAY = 194.18,        // Terre (jour sidéral) - Ancrage, Stabilité (souvent associé au chakra racine)
  EARTH_YEAR_OM = 136.10,    // Terre (année, fréquence "OM") - Relaxation, Méditation (souvent associé au chakra du cœur)
  MERCURY = 141.27,          // Mercure - Communication, Intellect (souvent associé au chakra de la gorge)
  VENUS = 221.23,            // Vénus - Amour, Harmonie, Beauté (souvent associé au chakra du troisième œil ou du cœur)
  MARS = 144.72,             // Mars - Énergie, Activité, Courage (souvent associé au chakra sacré ou racine)
  JUPITER = 183.58,          // Jupiter - Croissance, Abondance, Sagesse (souvent associé au chakra couronne ou du troisième œil)
  SATURN = 147.85,           // Saturne - Structure, Discipline, Concentration (souvent associé au chakra racine ou du troisième œil)
  URANUS = 207.36,           // Uranus - Inspiration, Changement, Originalité (souvent associé au chakra couronne)
  NEPTUNE = 211.44,          // Neptune - Intuition, Rêves, Spiritualité (souvent associé au chakra couronne)
  PLUTO = 140.25,            // Pluton (fréquence controversée car statut de planète a changé) - Transformation, Renaissance
}

// --- Fréquences des Ondes Cérébrales (Valeurs représentatives ou bornes de plages) ---
// Ces fréquences représentent des états de conscience et d'activité cérébrale.
enum BrainwaveFrequency {
  DELTA_LOW = 1,             // Delta (0.5-4 Hz) - Sommeil profond, Guérison, Inconscient
  THETA_LOW = 4,             // Theta (4-8 Hz) - Méditation profonde, Intuition, Créativité, Sommeil paradoxal
  ALPHA_MID = 10,            // Alpha (8-12 Hz) - Relaxation éveillée, Calme, Apprentissage accéléré
  BETA_LOW = 12,             // Bêta Bas (12-15 Hz) - Attention détendue, Concentration légère
  BETA_MID = 20,             // Bêta Moyen (15-22 Hz) - Pensée active, Concentration, Résolution de problèmes
  BETA_HIGH = 30,            // Bêta Haut (22-38 Hz) - Forte concentration, Excitation, Anxiété possible
  GAMMA_LOW = 38,            // Gamma (38-100+ Hz) - Traitement d'information de haut niveau, Perception, Conscience
  SCHUMANN_RESONANCE = 7.83, // Résonance de Schumann (fondamentale) - Équilibre naturel, Bien-être, Connexion Terre
}

// --- Autres fréquences notables souvent citées ---
enum OtherNotableFrequency {
  A4_CONCERT_PITCH = 440,    // La standard de concert (diapason)
  A4_VERDI_TUNING = 432,     // "La de Verdi" - Souvent citée pour ses propriétés harmonisantes et relaxantes
  RIFE_UNIVERSAL_1 = 727,    // Une des fréquences "universelles" de Royal Rife (pour pathogènes)
  RIFE_UNIVERSAL_2 = 787,    // Une autre fréquence "universelle" de Rife
  RIFE_UNIVERSAL_3 = 880,    // Une autre fréquence "universelle" de Rife (pour pathogènes)
  FREQUENCY_OF_CREATION = 432, // Souvent appelée ainsi, identique au La de Verdi, associée à l'harmonie naturelle
  LOVE_FREQUENCY = 528,      // Identique au Solfeggio MI, souvent appelée "fréquence de l'amour"
}
```

#### Interface Utilisateur pour la Configuration
L'interface utilisateur permet une configuration intuitive des sons binauraux à travers plusieurs options :

1. **Sélection de la fréquence de base**:
   - Slider précis (20-1000 Hz)
   - Menu déroulant avec fréquences prédéfinies (chakras, organes)
   - Descriptions détaillées des effets de chaque fréquence

2. **Sélection de l'état cérébral**:
   - Delta (sommeil profond, guérison)
   - Theta (méditation profonde, créativité)
   - Alpha (relaxation, calme)
   - Beta (concentration, vigilance)
   - Gamma (cognition supérieure)

3. **Personnalisation avancée**:
   - Contrôle du volume
   - Sélection de la forme d'onde
   - Option de modulation temporelle (évolution progressive entre différents états)

## 📊 Expérience Utilisateur

### Parcours Utilisateur
1. **Onboarding**: Introduction à l'application et questionnaire initial pour personnaliser l'expérience
2. **Découverte**: Exploration des différentes catégories de séances et fonctionnalités
3. **Personnalisation**: Configuration des préférences audio, visuelles et de contenu
4. **Utilisation**: Séances guidées avec suivi de progression
5. **Engagement**: Participation à la communauté et partage d'expériences

### Interface Adaptative
- Design fluide s'adaptant à toutes les tailles d'écran (mobile, tablette, web)
- Thèmes clair et sombre avec transitions douces
- Accessibilité intégrée (VoiceOver, TalkBack, contraste, taille de texte)
- Navigation intuitive et cohérente entre plateformes
- Interface de configuration audio avancée avec visualisations en temps réel
- Contrôles intuitifs pour les sons binauraux avec préréglages et personnalisation

## 💰 Stratégie de Monétisation

- **Abonnement Premium**: Accès illimité à toutes les fonctionnalités (9$/mois)
- **Version Gratuite**: Fonctionnalités de base avec publicités non intrusives optionnelles
- **Achats In-App**: Déblocage de contenus spécifiques (packs de séances, voix premium)
- **Dons**: Support volontaire via PayPal et autres plateformes

## 🌐 Internationalisation

Support multi-langue dès le lancement:
- Français
- Anglais
- Espagnol

Avec une architecture permettant d'ajouter facilement d'autres langues.

## 🔒 Sécurité et Confidentialité

- Chiffrement des données sensibles
- Conformité RGPD/CCPA
- Politique de confidentialité transparente
- Options de confidentialité granulaires pour les utilisateurs

## 🔮 Évolutions Futures

- Intégration d'un chatbot IA pour personnalisation avancée
- Connexion avec montres connectées pour suivi biométrique
- Expansion des mini-jeux thérapeutiques
- Communauté et fonctionnalités sociales enrichies
- Contenus exclusifs en partenariat avec des experts
- Système avancé de création de scripts avec IA générative
- Intégration de modèles vocaux personnalisés avec Piper TTS
- Analyse vocale pour adaptation des séances en temps réel
- Réalité augmentée pour expériences immersives guidées

---

<div align="center">
  <p>Développé avec ❤️ par PiKnowKyo Lab</p>
  <p>
    <a href="https://piknowkyo.com">Site Web</a> •
    <a href="mailto:<EMAIL>">Contact</a>
  </p>
</div>
