[debug] [2025-07-02T20:15:25.918Z] ----------------------------------------------------------------------
[debug] [2025-07-02T20:15:25.920Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions:listAllUsers
[debug] [2025-07-02T20:15:25.920Z] CLI Version:   14.9.0
[debug] [2025-07-02T20:15:25.920Z] Platform:      win32
[debug] [2025-07-02T20:15:25.920Z] Node Version:  v22.11.0
[debug] [2025-07-02T20:15:25.921Z] Time:          Wed Jul 02 2025 16:15:25 GMT-0400 (Eastern Daylight Time)
[debug] [2025-07-02T20:15:25.921Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-02T20:15:26.078Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-02T20:15:26.079Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-02T20:15:26.083Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.083Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.084Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/************ [none]
[debug] [2025-07-02T20:15:26.635Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/************ 200
[debug] [2025-07-02T20:15:26.635Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/************ {"projectNumber":"************","projectId":"piknowkyo-777","lifecycleState":"ACTIVE","name":"piknowkyo","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-22T14:11:22.125151Z"}
[debug] [2025-07-02T20:15:26.636Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-02T20:15:26.636Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-02T20:15:26.636Z] [iam] checking project piknowkyo-777 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-07-02T20:15:26.636Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.636Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.637Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/piknowkyo-777:testIamPermissions [none]
[debug] [2025-07-02T20:15:26.637Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/piknowkyo-777:testIamPermissions x-goog-quota-user=projects/piknowkyo-777
[debug] [2025-07-02T20:15:26.637Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/piknowkyo-777:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-07-02T20:15:26.715Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/piknowkyo-777:testIamPermissions 200
[debug] [2025-07-02T20:15:26.716Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/piknowkyo-777:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-07-02T20:15:26.716Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.716Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-02T20:15:26.716Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-07-02T20:15:26.716Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-07-02T20:15:26.891Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions 404
[debug] [2025-07-02T20:15:26.891Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions {"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}}
[debug] [2025-07-02T20:15:26.892Z] [functions] service account IAM check errored, deploy may fail: Request to https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}},"response":{"statusCode":404}},"exit":1,"message":"Request to https://iam.googleapis.com/v1/projects/************/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account","status":404}
[info] 
[info] === Deploying to 'piknowkyo-777'...
[info] 
[info] i  deploying functions 
[info] Running command: npm --prefix "$RESOURCE_DIR" run build
