// src/games/gameData.ts

import { GameInfo } from './common/models'; // Assuming GameInfo is defined in models.ts

// Define a type for the game information that can be exposed.
// We omit the 'component' as it's specific to the rendering logic and not general metadata.
export type GameInfoForList = Omit<GameInfo, 'component'>;

/**
 * List of all games available in the application with their metadata.
 * This list is static and serves as a central source of truth for game information
 * across different parts of the application (e.g., GamesPage, StatsPage).
 */
export const GAMES_LIST: GameInfoForList[] = [
  { id: "zen-tetris", titleKey: "games.zenTetris.title", descriptionKey: "games.zenTetris.description", maxLevels: 100, estimatedDurationMinutes: 10, tags: ["concentration", "memory", "relaxation", "reflexes"], icon: 'grid' },
  { id: "cardiac-coherence", titleKey: "games.cardiacCoherence.title", descriptionKey: "games.cardiacCoherence.description", maxLevels: 1, estimatedDurationMinutes: 5, tags: ["relaxation", "breathing", "focus"], icon: 'heart' },
];

// If you have a map from ID to game object, you can also export it for easier lookup
export const GAMES_MAP: { [id: string]: GameInfoForList } = GAMES_LIST.reduce((acc, game) => {
  acc[game.id] = game;
  return acc;
}, {} as { [id: string]: GameInfoForList });