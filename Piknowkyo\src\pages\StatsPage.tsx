// src/pages/StatsPage.tsx
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { fetchSessionManifest } from '../data/sessions';
import { SessionManifestEntry } from '../models';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { useAppStore, SessionScript } from '../store/useAppStore';
import AppIcon from '../components/AppIcon';
import { useShallow } from 'zustand/react/shallow';
import { GAMES_LIST } from '../games/gameData';
import LexiconText from '../components/LexiconText';
import { Dictionary, DictionaryElement, SessionRecommendationTags,
         EmotionalProfileTags, ManifestationProfileTags, TherapeuticProfileTags, SessionProfileTags
} from '../types/definitions'; 

// Recharts imports for charting
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON>, Toolt<PERSON>, <PERSON><PERSON>, <PERSON>, X<PERSON>, <PERSON>A<PERSON>s, CartesianGrid } from 'recharts';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.header`
  text-align: center;
  margin-bottom: 3rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    svg {
        opacity: 0.9;
    }
  }
  p {
    font-size: 1.05rem;
    color: ${({ theme }) => theme.textSecondary};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.8rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
  }

  .icon-container {
    font-size: 2.2rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    padding: 0.9rem;
    background-color: ${({ theme }) => theme.primary}2A;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
  }

  h2 {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-top: 0;
    margin-bottom: 0.4rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  .stat-value {
    font-size: 2rem;
    color: ${({ theme }) => theme.primary};
    font-weight: 700;
    margin: 0 0 0.3rem 0;
  }
  .small-text {
    font-size: 0.8rem;
    color: ${({ theme }) => theme.textMuted};
    margin-top: 0.3rem;
    line-height: 1.3;
  }
`;

const DetailedStatsSection = styled.div`
  margin-bottom: 2.5rem;
  background: ${({ theme }) => theme.surface};
  padding: 1.8rem;
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};

  h3 {
    font-size: 1.4rem;
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1.2rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
    display: flex;
    align-items: center;
    gap: 0.6rem;
  }
`;

const ChartContainer = styled.div`
  width: 100%;
  height: 300px;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
`;

const StatsList = styled.ul`
  list-style: none;
  padding: 0;
  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.9rem 0.5rem;
    border-bottom: 1px solid ${({ theme }) => theme.border}99;
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background-color: ${({theme}) => theme.surfaceAlt};
    }

    a {
        color: ${({ theme }) => theme.text};
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
        &:hover {
            color: ${({ theme }) => theme.primary};
        }
    }

    strong {
      flex-grow: 1;
      margin-right: 1rem;
    }
    span {
      font-weight: 500;
      color: ${({ theme }) => theme.primary};
      white-space: nowrap;
    }
  }
`;

// Styled component for the analysis text
const AnalysisText = styled.div`
  text-align: left;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
  p {
    margin-bottom: 1rem;
    &:last-child {
      margin-bottom: 0;
    }
  }
  ul {
    list-style: disc inside;
    margin-bottom: 1rem;
    padding-left: 1.2rem;
  }
  li {
    margin-bottom: 0.5rem;
  }
`;

const InfoText = styled.p`
  font-size: 1rem;
  color: ${({ theme }) => theme.textMuted};
  text-align: center;
  padding: 1.5rem 0;
  line-height: 1.6;
  font-style: italic;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 70vh;
  svg { font-size: 3.5rem; margin-bottom: 1.5rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 50vh;
  justify-content: center;

  p { margin-bottom: 1rem; }
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
    font-weight: 500;
  }
`;

const FutureFeatureCard = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
  box-shadow: inset 0 0 8px rgba(0,0,0,0.1);
  text-align: center;
  color: ${({ theme }) => theme.textSecondary};
  font-style: italic;
  font-size: 0.95rem;
  .icon {
    font-size: 2rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.5rem;
  }
  .status {
    font-size: 0.8rem;
    font-weight: 600;
    color: ${({ theme }) => theme.accent};
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
  }
`;

// Helper function to format duration from minutes to a readable string
const formatDuration = (totalMinutes: number, t: any): string => {
  if (totalMinutes === 0) return `0 ${t('units.min', 'min')}`;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  let result = '';
  if (hours > 0) result += `${hours}${t('stats.duration.hours', 'h')} `;
  if (minutes > 0 || hours === 0) result += `${minutes}${t('stats.duration.min', 'min')}`;
  return result.trim();
};

// Define some appealing colors for the charts
const CHART_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28DFF', '#73E9A2', '#FFCD7D', '#FF965B'];

// Custom YAxis Tick for Bar Chart to handle long labels
const YAxisCustomTick = (props: any) => {
  const { x, y, payload, width } = props;
  const label = payload.value;
  const MAX_LABEL_LENGTH = 15; // Max characters before truncating
  const truncatedLabel = label.length > MAX_LABEL_LENGTH ? `${label.substring(0, MAX_LABEL_LENGTH)}...` : label;

  return (
    <g transform={`translate(${x},${y})`}>
      <text x={0} y={0} dy={5} textAnchor="end" fill="var(--text-secondary)" fontSize="0.8rem">
        {truncatedLabel}
      </text>
    </g>
  );
};


const StatsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { lang } = useLang();
  const currentLang = i18n.language as 'en' | 'fr' | 'es';

  // --- Data Fetching ---
  const [sessionManifest, setSessionManifest] = useState<SessionManifestEntry[]>([]);
  const [isLoadingManifest, setIsLoadingManifest] = useState(true);
  const [manifestError, setManifestError] = useState<string | null>(null);

  const { activity, gameProgress, definitions, sessions, privateData } = useAppStore(useShallow(state => ({
    activity: state.activity,
    gameProgress: state.gameProgress,
    definitions: state.definitions,
    sessions: state.sessions, // Contains full SessionScript objects
    privateData: state.privateData,
  })));

  const completedSessions = activity?.completedSessions || [];
  const streak = activity?.streak ?? 0;
  const totalMinutes = activity?.totalMinutes ?? 0;
  const personalBestScores = gameProgress?.personalBest || {};
  const hasGameScores = Object.keys(personalBestScores).length > 0;
  const journalEntries = useMemo(() => {
    return (privateData || []).filter(doc => doc.data.type === 'journal');
  }, [privateData]);
  const hasJournalEntries = journalEntries.length > 0;

  // Create a map for quick lookup of full SessionScript objects by ID
  const sessionScriptMap = useMemo(() => {
    const map = new Map<string, SessionScript>();
    // Flatten scripts from all categories into a single map
    Object.values(sessions.scripts).forEach(scriptArray => {
      scriptArray.forEach(script => {
        map.set(script.id, script);
      });
    });
    return map;
  }, [sessions.scripts]);


  // Load session manifests for ALL supported languages to ensure comprehensive data coverage
  useEffect(() => {
    const loadAllManifests = async () => {
      setIsLoadingManifest(true);
      setManifestError(null);
      try {
        const languages: Array<'en' | 'fr' | 'es'> = ['en', 'fr', 'es'];
        const manifestsPromises = languages.map(l => fetchSessionManifest(l));
        const allManifestsArrays = await Promise.all(manifestsPromises);
        
        // Combine all manifests into a single array and use a Map to remove duplicates by ID
        const combinedManifestMap = new Map<string, SessionManifestEntry>();
        allManifestsArrays.flat().forEach(entry => {
          if (!combinedManifestMap.has(entry.id)) {
            combinedManifestMap.set(entry.id, entry);
          }
        });
        
        const combinedManifestData = Array.from(combinedManifestMap.values());
        setSessionManifest(combinedManifestData);
      } catch (err) {
        console.error("Error loading combined session manifests for stats:", err);
        const errorMessage = err instanceof Error ? err.message : t('errors.cantLoadSessions', "Could not load session data.");
        setManifestError(errorMessage);
      } finally {
        setIsLoadingManifest(false);
      }
    };
    loadAllManifests();
  }, [t]); // Removed lang dependency as we now fetch all languages

  // Combine loading states
  const isLoading = isLoadingManifest || !activity || !gameProgress || definitions.isLoading || sessions.isLoading || privateData === null;
  const error = manifestError || (definitions.dictionary === null ? t('errors.cantLoadDictionary', 'Could not load dictionary data.') : null);

  const getConceptName = useCallback((key: string): string => {
    const dictElement: DictionaryElement | undefined = definitions.dictionaryMap.get(key);
    // Use an object for defaultValue when calling t()
    return dictElement ? dictElement.name[currentLang] : t(`sessionTypes.${key}`, { defaultValue: key.replace(/_/g, ' ') }); 
  }, [definitions.dictionaryMap, currentLang, t]);

  const getSessionTitle = useCallback((sessionId: string): string => {
    const session = sessionScriptMap.get(sessionId);
    if (session) {
        return session.title;
    }
    return t('journal.unknownSession', { id: sessionId });
  }, [sessionScriptMap, t]);


  // Helper function to extract concept keys from metadata, for both unique counting and raw lists
  // This function will now return both a Set of unique keys and an array of all keys (including duplicates)
  const extractConceptsFromMetadata = useCallback((metadata: SessionRecommendationTags): { uniqueKeys: Set<string>; allKeys: string[] } => {
    const uniqueKeys = new Set<string>();
    const allKeys: string[] = [];

    if (!metadata || typeof metadata !== 'object') {
        return { uniqueKeys, allKeys };
    }

    const addConcept = (key: string) => {
        uniqueKeys.add(key);
        allKeys.push(key);
    };

    // Helper to add concepts from an array, handling string or {key: string} elements
    const addConceptsFromArray = (arr: (string | { key: string; composition?: string[] })[] | undefined) => { 
        if (Array.isArray(arr)) {
            for (const item of arr) {
                if (typeof item === 'string') {
                    addConcept(item);
                } else if (typeof item === 'object' && item !== null && 'key' in item && typeof item.key === 'string') {
                    addConcept(item.key);
                    if ('composition' in item && Array.isArray(item.composition)) {
                        item.composition.forEach((comp: any) => { // comp can be string
                            if (typeof comp === 'string') {
                                addConcept(comp);
                            }
                        });
                    }
                }
            }
        }
    };

    // --- Top-level arrays from SessionRecommendationTags ---
    addConceptsFromArray(metadata.addresses_emotions_or_sentiments);
    addConceptsFromArray(metadata.addresses_cognitive_patterns);
    addConceptsFromArray(metadata.addresses_somatic_sensations);
    addConceptsFromArray(metadata.addresses_behavioral_patterns);
    addConceptsFromArray(metadata.cultivates_outcomes);
    addConceptsFromArray(metadata.primary_modalities);
    addConceptsFromArray(metadata.techniques_used);
    addConceptsFromArray(metadata.ideal_for_contexts);
    addConceptsFromArray(metadata.addresses_energetic_states);
    addConceptsFromArray(metadata.metaphorical_themes);
    
    // Handle duration_category and intensity_category directly if they are strings
    if (typeof metadata.duration_category === 'string') {
        addConcept(metadata.duration_category);
    }
    if (typeof metadata.intensity_category === 'string') {
        addConcept(metadata.intensity_category);
    }

    // --- Nested profile objects from SessionRecommendationTags ---
    const emotionalProfile = metadata.emotional_profile;
    if (emotionalProfile) {
        addConceptsFromArray(emotionalProfile.primary_emotion_drivers);
        addConceptsFromArray(emotionalProfile.target_sentiments); 
    }

    const manifestationProfile = metadata.manifestation_profile;
    if (manifestationProfile) {
        addConceptsFromArray(manifestationProfile.cognitive_patterns);
        addConceptsFromArray(manifestationProfile.somatic_sensations);
    }

    const therapeuticProfile = metadata.therapeutic_profile;
    if (therapeuticProfile) {
        addConceptsFromArray(therapeuticProfile.desired_outcomes);
        addConceptsFromArray(therapeuticProfile.sensory_channels_engaged);
        // primary_modality can be string or array
        if (typeof therapeuticProfile.primary_modality === 'string') {
            addConcept(therapeuticProfile.primary_modality);
        } else if (Array.isArray(therapeuticProfile.primary_modality)) {
            addConceptsFromArray(therapeuticProfile.primary_modality);
        }
    }
    
    const sessionProfile = metadata.session_profile;
    if (sessionProfile) {
        // These properties in SessionProfileTags are typed as string | string[] | undefined
        // We handle each case explicitly.
        if (typeof sessionProfile.complexity === 'string') {
            addConcept(sessionProfile.complexity);
        } else if (Array.isArray(sessionProfile.complexity)) {
            addConceptsFromArray(sessionProfile.complexity);
        }

        if (typeof sessionProfile.energy_dynamic === 'string') {
            addConcept(sessionProfile.energy_dynamic);
        } else if (Array.isArray(sessionProfile.energy_dynamic)) {
            addConceptsFromArray(sessionProfile.energy_dynamic);
        }

        addConceptsFromArray(sessionProfile.ideal_context);
        addConceptsFromArray(sessionProfile.techniques_used);
        
        // duration and intensity can be string or array in session_profile
        if (typeof sessionProfile.duration === 'string') {
            addConcept(sessionProfile.duration);
        } else if (Array.isArray(sessionProfile.duration)) {
            addConceptsFromArray(sessionProfile.duration);
        }
        if (typeof sessionProfile.intensity === 'string') {
            addConcept(sessionProfile.intensity);
        } else if (Array.isArray(sessionProfile.intensity)) {
            addConceptsFromArray(sessionProfile.intensity);
        }
    }
    return { uniqueKeys, allKeys };
  }, []);


  // Recalculate affinityScores and allCollectedConceptsRaw based on completed sessions and loaded FULL session scripts
  const { calculatedAffinityScores, allCollectedConceptsRaw } = useMemo(() => {
    const scores: { [conceptKey: string]: number } = {};
    const allRawConcepts: string[] = [];

    // Ensure all necessary data is loaded before attempting calculations
    if (!sessionScriptMap.size || !completedSessions.length || !definitions.dictionary) {
        return { calculatedAffinityScores: scores, allCollectedConceptsRaw: allRawConcepts };
    }

    for (const completion of completedSessions) {
        const sessionFullData = sessionScriptMap.get(completion.id);
        
        if (sessionFullData && sessionFullData.recommendation_metadata) {
            const metadata = sessionFullData.recommendation_metadata;
            const { uniqueKeys, allKeys } = extractConceptsFromMetadata(metadata);

            // Populate scores (unique counts)
            for (const conceptKey of uniqueKeys) {
                scores[conceptKey] = (scores[conceptKey] || 0) + 1;
            }
            // Populate allRawConcepts (including duplicates)
            allRawConcepts.push(...allKeys);

        } else {
            if (!sessionFullData) {
                console.warn(`Full SessionScript not found in sessionScriptMap for ID: ${completion.id}.`);
            }
        }
    }
    return { calculatedAffinityScores: scores, allCollectedConceptsRaw: allRawConcepts };
  }, [completedSessions, sessionScriptMap, definitions.dictionary, extractConceptsFromMetadata]);

  const hasAffinityScores = Object.keys(calculatedAffinityScores).length > 0;

  // --- Stats Calculation ---
  type SessionTimeSpentData = { count: number; totalDuration: number; id: string };
  type SessionTypeAggregateData = { count: number; totalDuration: number; };

  const stats = useMemo(() => {
    const totalSessionsCompleted = completedSessions.length;
    const sessionsTimeSpent: Record<string, SessionTimeSpentData> = {};
    const sessionTypesFollowed: Record<string, SessionTypeAggregateData> = {};

    for (const completion of completedSessions) {
        const sessionFullData = sessionScriptMap.get(completion.id);
        
        if (sessionFullData) {
            const { id, title, type, duration } = sessionFullData;
            const sessionDuration = duration || 0; // Use duration from the script, default to 0

            // Aggregate for "Details per Session" list (by title)
            if (!sessionsTimeSpent[title]) {
                sessionsTimeSpent[title] = { count: 0, totalDuration: 0, id: id };
            }
            sessionsTimeSpent[title].count += 1;
            sessionsTimeSpent[title].totalDuration += sessionDuration;

            // Aggregate for "Breakdown by Session Type" Pie Chart (by type)
            if (!sessionTypesFollowed[type]) {
                sessionTypesFollowed[type] = { count: 0, totalDuration: 0 };
            }
            sessionTypesFollowed[type].count += 1;
            sessionTypesFollowed[type].totalDuration += sessionDuration;
        } else {
             console.warn(`Could not find full script data for completed session ID: ${completion.id}. This session will be excluded from detailed stats.`);
        }
    }

    return {
      totalSessionsCompleted,
      totalNotesWritten: journalEntries.length,
      totalTimeSpent: totalMinutes,
      sessionsTimeSpent,
      sessionTypesFollowed,
      streak,
    };
  }, [completedSessions, totalMinutes, streak, journalEntries.length, sessionScriptMap]);

  // Data for Pie Chart (Session Types Breakdown)
  const pieChartData = useMemo(() => {
    return Object.entries(stats.sessionTypesFollowed)
      .sort(([, a], [, b]) => b.count - a.count)
      .map(([type, data]) => ({
        name: t(`sessionTypes.${type}`, { defaultValue: type.charAt(0).toUpperCase() + type.slice(1) }),
        value: data.count,
      }));
  }, [stats.sessionTypesFollowed, t]);

  // Data for Bar Chart (Affinity Profile - unique counts)
  const affinityChartData = useMemo(() => {
    return Object.entries(calculatedAffinityScores)
      .sort(([, countA], [, countB]) => (countB as number) - (countA as number))
      .slice(0, 10) // Limit to top 10 for readability on the chart
      .map(([conceptKey, count]) => ({
        name: getConceptName(conceptKey),
        count: count,
      }));
  }, [calculatedAffinityScores, getConceptName]);

    // Helper to format a list of concepts with counts into ReactNodes
    const formatConceptListWithCounts = useCallback((conceptsWithCounts: { key: string; count: number }[]): React.ReactNode => {
      if (conceptsWithCounts.length === 0) return t('stats.affinityAnalysis.noConcepts', 'nothing specific');
      
      const parts: React.ReactNode[] = [];
      conceptsWithCounts.forEach((item, index) => {
        if (index > 0) {
            if (index === conceptsWithCounts.length - 1) {
                parts.push(<React.Fragment key={`separator-and-${item.key}-${index}`}> {t('and', 'and')} </React.Fragment>);
            } else {
                parts.push(<React.Fragment key={`separator-comma-${item.key}-${index}`}>, </React.Fragment>);
            }
        }
        const text = item.count > 1 
            ? `${getConceptName(item.key)} (${item.count})` 
            : getConceptName(item.key);
        parts.push(<LexiconText key={item.key} text={text} />);
      });

      return <>{parts}</>;
    }, [t, getConceptName]);


    // Data for textual Affinity Analysis Summary
    const affinityAnalysisSummary = useMemo(() => {
        const summaryParts: React.ReactNode[] = [];
        const topNPerCategory = 3; 
        const overallTopNConceptsToDisplay = 5;

        if (!definitions.dictionary || allCollectedConceptsRaw.length === 0) {
            return null;
        }

        const categoriesToAnalyze = [
            { key: 'sentiments', translationKey: 'stats.affinityAnalysis.emotions', dictionaryCategory: 'sentiments' },
            { key: 'desired_outcomes', translationKey: 'stats.affinityAnalysis.outcomes', dictionaryCategory: 'desired_outcomes' },
            { key: 'techniques', translationKey: 'stats.affinityAnalysis.techniques', dictionaryCategory: 'techniques' },
            { key: 'somatic_sensations', translationKey: 'stats.affinityAnalysis.somatic', dictionaryCategory: 'somatic_sensations' },
            { key: 'cognitive_patterns', translationKey: 'stats.affinityAnalysis.cognitive', dictionaryCategory: 'cognitive_patterns' },
            { key: 'modalities', translationKey: 'stats.affinityAnalysis.modalities', dictionaryCategory: 'modalities' },
            { key: 'durations', translationKey: 'stats.affinityAnalysis.duration', dictionaryCategory: 'durations' },
            { key: 'intensities', translationKey: 'stats.affinityAnalysis.intensity', dictionaryCategory: 'intensities' },
        ];

        const categorizedRawConcepts: { [category: string]: string[] } = {};
        for (const conceptKey of allCollectedConceptsRaw) {
            let categoryFound: string | null = null;
            for (const categoryName in definitions.dictionary) {
                if (Array.isArray((definitions.dictionary as any)[categoryName])) {
                    const dictElements = (definitions.dictionary as any)[categoryName] as DictionaryElement[];
                    if (dictElements.some(el => el.key === conceptKey)) {
                        categoryFound = categoryName;
                        break;
                    }
                }
            }
            if (categoryFound) {
                if (!categorizedRawConcepts[categoryFound]) categorizedRawConcepts[categoryFound] = [];
                categorizedRawConcepts[categoryFound].push(conceptKey);
            }
        }
        
        summaryParts.push(<p key="intro-text"><LexiconText text={t('stats.affinityAnalysis.intro')} /></p>);

        for (const categoryInfo of categoriesToAnalyze) {
            const conceptsInThisCategory = categorizedRawConcepts[categoryInfo.dictionaryCategory];
            if (conceptsInThisCategory && conceptsInThisCategory.length > 0) {
                const freqMap: { [key: string]: number } = {};
                conceptsInThisCategory.forEach(key => {
                    freqMap[key] = (freqMap[key] || 0) + 1;
                });
                
                const conceptsWithCounts = Object.entries(freqMap)
                    .map(([key, count]) => ({ key, count }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, topNPerCategory);

                if (conceptsWithCounts.length > 0) {
                    const formattedList = formatConceptListWithCounts(conceptsWithCounts);
                    summaryParts.push(
                        <p key={`category-summary-${categoryInfo.key}`}>
                            {t(categoryInfo.translationKey, { list: '' })} {formattedList}
                        </p>
                    );
                }
            }
        }
        
        // Overall top concepts calculation
        const overallFreqMap: { [key: string]: number } = {};
        allCollectedConceptsRaw.forEach(key => {
            overallFreqMap[key] = (overallFreqMap[key] || 0) + 1;
        });

        const overallTopConceptsWithCounts = Object.entries(overallFreqMap)
            .map(([key, count]) => ({ key, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, overallTopNConceptsToDisplay);

        if (overallTopConceptsWithCounts.length > 0) {
            const overallFormattedList = formatConceptListWithCounts(overallTopConceptsWithCounts);
            summaryParts.push(
                <p key="overall-concepts">
                    {t('stats.affinityAnalysis.overallTopConcepts', { list: '' })} {overallFormattedList}
                </p>
            );
        }

        return summaryParts.length > 1 ? summaryParts : null;
    }, [allCollectedConceptsRaw, definitions.dictionary, t, formatConceptListWithCounts, currentLang]);


  // Data for Activity History (sessions only for now)
  const activityHistoryData = useMemo(() => {
    // This calculation now uses sessionScriptMap to be more robust
    const history = completedSessions.map(completion => {
        const sessionFullData = sessionScriptMap.get(completion.id);
        const completionDate = new Date(completion.completedAt);
        
        // Use data from the full script if available, otherwise provide defaults
        const title = sessionFullData ? sessionFullData.title : t('journal.unknownSession', { id: completion.id });
        const duration = sessionFullData ? sessionFullData.duration : 0;
        
        return {
            id: completion.id,
            type: 'session',
            title: title,
            date: completionDate,
            duration: duration,
        };
    });
    return history.sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [completedSessions, getSessionTitle, sessionScriptMap, t]); // Use sessionScriptMap
  
  // Determine if there's any data to show (sessions, games, journal, or affinity)
  const hasAnyData = stats.totalSessionsCompleted > 0 || stats.totalNotesWritten > 0 || stats.totalTimeSpent > 0 || stats.streak > 0 || hasGameScores || hasAffinityScores || activityHistoryData.length > 0;

  if (isLoading) {
    return <LoadingContainer><AppIcon name="loader" /> {t('loading.stats', 'Loading statistics...')}</LoadingContainer>;
  }

  if (error) {
    const errorMessage = error || '';
    return <ErrorMessage><p>{String(errorMessage)}</p><Link to="/">{t('actions.backToHome', "Back to home")}</Link></ErrorMessage>;
  }

  if (!hasAnyData) {
    return (
      <PageContainer>
        <PageHeader>
          <h1><AppIcon name="stats" /> {t('stats.title', 'Your Well-being Statistics')}</h1>
          <p><LexiconText text={t('stats.description', 'Track your journey, celebrate your progress, and discover your trends.')} /></p>
        </PageHeader>
        <InfoText>
          {t('stats.noData', 'You have not completed any sessions yet. Start a session to see your stats here!')}
        </InfoText>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><AppIcon name="stats" /> {t('stats.title', 'Your Well-being Statistics')}</h1>
        <p><LexiconText text={t('stats.description', 'Track your journey, celebrate your progress, and discover your trends.')} /></p>
      </PageHeader>

      <StatsGrid>
        <StatCard>
          <div className="icon-container"><AppIcon name="book-open" /></div>
          <h2>{t('stats.sessionsCompleted', 'Sessions Completed')}</h2>
          <p className="stat-value">{stats.totalSessionsCompleted}</p>
          <div className="small-text">{t('stats.sessionsCompletedDesc', 'Total number of sessions you have completed.')}</div>
        </StatCard>
        <StatCard>
          <div className="icon-container"><AppIcon name="clock" /></div>
          <h2>{t('stats.totalTime', 'Total Time in Session')}</h2>
          <p className="stat-value">{formatDuration(stats.totalTimeSpent, t)}</p>
          <div className="small-text">{t('stats.totalTimeDesc', 'Estimated cumulative time.')}</div>
        </StatCard>
        <StatCard>
          <div className="icon-container"><AppIcon name="award" /></div>
          <h2>{t('stats.daysStreak', 'Consecutive days')}</h2>
          <p className="stat-value">{stats.streak}</p>
          <div className="small-text">{t('stats.daysDaysStreakDesc', 'Days you logged in and completed an activity.')}</div>
        </StatCard>
        <StatCard>
          <div className="icon-container"><AppIcon name="edit-2" /></div>
          <h2>{t('stats.notesWritten', 'Total Notes Written')}</h2>
          <p className="stat-value">{stats.totalNotesWritten}</p>
          <div className="small-text">{t('stats.notesWrittenDesc', 'Total number of reflections saved.')}</div>
        </StatCard>
      </StatsGrid>

      {/* Journal Entries Section */}
      {hasJournalEntries && (
        <DetailedStatsSection>
          <h3><AppIcon name="journal" /> {t('stats.journalEntries', 'Your Journal Entries')}</h3>
          <StatsList>
            {journalEntries
              .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime())
              .slice(0, 5) // Limits to 5 entries
              .map(entry => (
                <li key={entry.id}>
                  <strong>
                    <Link to={`/journal/${entry.id}`} title={t('stats.viewEntry', 'View entry')}>
                      {new Date(entry.data.date).toLocaleDateString(currentLang)} {t('stats.forSession', 'for session')} {getSessionTitle(entry.data.sessionId)}
                    </Link>
                  </strong>
                  <span>{entry.data.note.substring(0, 50)}...</span>
                </li>
              ))}
          </StatsList>
          {journalEntries.length > 5 && (
            <InfoText>
              <Link to="/journal">{t('stats.showAllJournalEntries', { count: journalEntries.length })}</Link>
            </InfoText>
          )}
        </DetailedStatsSection>
      )}

      {/* Affinity Profile Section (with Bar Chart and Analysis) */}
      {hasAffinityScores && affinityChartData.length > 0 && (
        <DetailedStatsSection>
          <h3><AppIcon name="target" /> {t('stats.yourAffinityProfile', 'Your Affinity Profile')}</h3>
          <p style={{textAlign: 'center', color: 'var(--text-muted)', marginBottom: '1.5rem'}}><LexiconText text={t('stats.affinityDescription', 'The concepts and emotions you\'ve most explored through your sessions.')} /></p>
          
          <ChartContainer>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={affinityChartData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                    type="number" 
                    dataKey="count" 
                    allowDecimals={false} 
                    tickFormatter={(value) => `${value} ${t('stats.timesExplored', 'times explored')}`}
                    stroke="var(--text-secondary)"
                    tick={{ fill: 'var(--text-secondary)', fontSize: '0.8rem' }}
                    label={{ value: t('stats.timesExplored', 'times explored'), position: 'insideBottomRight', offset: 0, fill: 'var(--text-primary)', fontSize: '0.9rem' }}
                />
                <YAxis 
                    type="category" 
                    dataKey="name" 
                    width={120} // Increased width for YAxis labels
                    tickLine={false} 
                    axisLine={false}
                    stroke="var(--text-secondary)"
                    tick={<YAxisCustomTick />} // Use custom tick component
                />
                <Tooltip 
                    cursor={{ fill: 'rgba(0,0,0,0.1)' }}
                    formatter={(value, name) => [`${value} ${t('stats.timesExplored', 'times explored')}`, name]} 
                    contentStyle={{ backgroundColor: 'var(--surface-alt)', border: 'none', borderRadius: '8px' }}
                    labelStyle={{ color: 'var(--primary-color)' }}
                    itemStyle={{ color: 'var(--text-secondary)' }}
                />
                <Bar dataKey="count" fill={CHART_COLORS[0]} />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>

          {/* Render the interpretive analysis text */}
          {affinityAnalysisSummary && affinityAnalysisSummary.length > 0 ? (
            <AnalysisText>
              {affinityAnalysisSummary.map((part, index) => (
                <React.Fragment key={`summary-part-${index}`}>{part}</React.Fragment>
              ))}
              <InfoText style={{marginTop: '1.5rem', marginBottom: '0'}}><Link to="/lexicon">{t('stats.affinityAnalysis.moreDetailsLink')}</Link></InfoText>
            </AnalysisText>
          ) : (
            <InfoText>{t('stats.affinityAnalysis.noDetails')}</InfoText>
          )}

        </DetailedStatsSection>
      )}

      {/* Session Type Breakdown Section (with Pie Chart) */}
      {Object.keys(stats.sessionTypesFollowed).length > 0 && pieChartData.length > 0 && (
        <DetailedStatsSection>
          <h3><AppIcon name="pie-chart" /> {t('stats.typesFollowed', 'Breakdown by Session Type')}</h3>
          <ChartContainer>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} (${((percent ?? 0) * 100).toFixed(0)}%)`}
                  labelLine={false}
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                    formatter={(value, name) => [`${value} ${t('stats.completionsPlural', 'completions')}`, name]} 
                    contentStyle={{ backgroundColor: 'var(--surface-alt)', border: 'none', borderRadius: '8px' }}
                    labelStyle={{ color: 'var(--primary-color)' }}
                    itemStyle={{ color: 'var(--text-secondary)' }}
                />
                <Legend 
                    layout="vertical" 
                    verticalAlign="middle" 
                    align="right" 
                    wrapperStyle={{ paddingLeft: '20px' }} 
                    formatter={(value) => <span style={{ color: 'var(--text-secondary)' }}>{value}</span>}
                />
              </PieChart>
            </ResponsiveContainer>
          </ChartContainer>
          {/* Keep the list as it provides raw counts not immediately obvious from pie chart, can be hidden/shown via toggle later */}
          <StatsList>
              {Object.entries(stats.sessionTypesFollowed)
                .sort(([, a], [, b]) => b.count - a.count)
                .map(([type, data]) => (
                <li key={type}>
                  <strong>{t(`sessionTypes.${type}`, type.charAt(0).toUpperCase() + type.slice(1))}</strong>
                  <span>{data.count} {data.count > 1 ? t('stats.completionsPlural', 'completions') : t('stats.completionsSingular', 'completion')} ({formatDuration(data.totalDuration, t)})</span>
                </li>
              ))}
          </StatsList>
        </DetailedStatsSection>
      )}

      {Object.keys(stats.sessionsTimeSpent).length > 0 && (
        <DetailedStatsSection>
          <h3><AppIcon name="list" /> {t('stats.timePerSession', 'Details per Session (Estimated)')}</h3>
          <StatsList>
            {Object.entries(stats.sessionsTimeSpent)
              .sort(([, a], [, b]) => b.totalDuration - a.totalDuration)
              .map(([title, data]) => (
              <li key={data.id}>
                <Link to={`/sessions/${data.id}`}>
                  {title}
                </Link>
                <span>{formatDuration(data.totalDuration, t)} ({data.count} {data.count > 1 ? t('stats.completionsPlural', 'completions') : t('stats.completionsSingular', 'completion')})</span>
              </li>
            ))}
          </StatsList>
        </DetailedStatsSection>
      )}

      {hasGameScores && (
        <DetailedStatsSection>
          <h3><AppIcon name="games" /> {t('stats.gameHighScores', 'Game High Scores')}</h3>
          <StatsList>
            {Object.entries(personalBestScores)
              .sort(([, a], [, b]) => b.score - a.score)
              .map(([gameId, bestScoreData]) => {
                const gameInfo = GAMES_LIST.find(g => g.id === gameId);
                const gameTitle = gameInfo ? t(gameInfo.titleKey) : gameId;
                return (
                  <li key={gameId}>
                    <strong>{gameTitle}</strong>
                    <span>
                      {bestScoreData.score} {t('units.points', 'pts')}
                      {bestScoreData.level > 0 && ` (${t('stats.levelLabel', 'Level')} ${bestScoreData.level})`}
                    </span>
                  </li>
                );
              })}
          </StatsList>
        </DetailedStatsSection>
      )}

      {/* Activity History Section */}
      {activityHistoryData.length > 0 && (
        <DetailedStatsSection>
          <h3><AppIcon name="calendar" /> {t('stats.activityHistory', 'Activity History')}</h3>
          <StatsList>
            {activityHistoryData.slice(0, 5).map((activityItem) => (
                <li key={activityItem.id + activityItem.date.toISOString()}>
                  <strong>
                    {activityItem.title}
                  </strong>
                  <span>
                    {t('stats.completedOn', 'Completed on')} {activityItem.date.toLocaleDateString(currentLang)}
                    {activityItem.duration > 0 && ` (${formatDuration(activityItem.duration, t)})`}
                  </span>
                </li>
            ))}
          </StatsList>
          {activityHistoryData.length > 5 && (
            <InfoText>
              <Link to="/activity-history">{t('stats.showAllActivityHistory')}</Link>
            </InfoText>
          )}
        </DetailedStatsSection>
      )}
      {/* Show general info text if no relevant sections have data */}
      {!hasGameScores && !hasJournalEntries && (!activityHistoryData.length) && !hasAffinityScores && (
          <InfoText>{t('stats.noData', 'You have not completed any sessions yet. Start a session to see your stats here!')}</InfoText>
      )}


      {/* Placeholder Section for Future Development (Health Metrics) */}
      <DetailedStatsSection>
        <h3><AppIcon name="heart" /> {t('stats.healthMetrics', 'Health & Well-being Metrics')}</h3>
        <InfoText>
          <LexiconText text={t('stats.futureDevMessage', "This section will soon integrate data from connected devices to provide insights into your sleep patterns, heart rate variability, exercise, nutrition, hydration, and more. Stay tuned for a complete holistic view of your well-being!")} />
        </InfoText>
        <FutureFeatureCard>
          <div className="icon"><AppIcon name="bed" /></div>
          <div>{t('stats.sleepPatterns', 'Sleep Patterns')}</div>
          <div className="status">{t('stats.comingSoon', 'Coming Soon')}</div>
        </FutureFeatureCard>
        <FutureFeatureCard>
          <div className="icon"><AppIcon name="activity" /></div>
          <div>{t('stats.heartRateVariability', 'Heart Rate Variability')}</div>
          <div className="status">{t('stats.comingSoon', 'Coming Soon')}</div>
        </FutureFeatureCard>
        <FutureFeatureCard>
          <div className="icon"><AppIcon name="run" /></div>
          <div>{t('stats.exerciseTracking', 'Exercise Tracking')}</div>
          <div className="status">{t('stats.comingSoon', 'Coming Soon')}</div>
        </FutureFeatureCard>
        <FutureFeatureCard>
          <div className="icon"><AppIcon name="apple" /></div>
          <div>{t('stats.nutritionHydration', 'Nutrition & Hydration')}</div>
          <div className="status">{t('stats.comingSoon', 'Coming Soon')}</div>
        </FutureFeatureCard>
      </DetailedStatsSection>

    </PageContainer>
  );
};

export default StatsPage;