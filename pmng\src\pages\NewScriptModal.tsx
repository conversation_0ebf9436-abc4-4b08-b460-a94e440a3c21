// pmng/src/pages/NewScriptModal.tsx

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { db } from '../firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import Modal from '../components/Modal';
import CryptoJS from 'crypto-js';

// --- Data & Constants ---
const SESSION_TYPES = [
    { "type": "hypnosis", "description": "A guided session to induce a deep state of relaxation and suggestibility, using a slow, repetitive tone to reduce stress or target subconscious goals like confidence or habit change." },
    { "type": "meditation", "description": "A guided mindfulness or relaxation session focusing on breathing, body awareness, or mental calm, with a soothing tone and frequent pauses to enhance presence." },
    { "type": "training", "description": "An energetic session to motivate and guide physical or mental training, delivering clear instructions and encouragement for activities like workouts or productivity tasks." },
    { "type": "story", "description": "An immersive narrative session, such as a fantasy or adventure story, with vivid descriptions and a captivating tone to engage the listener’s imagination." },
    { "type": "journaling", "description": "An introspective session guiding the listener through reflective questions or prompts, with a calm, encouraging tone and pauses to allow for writing responses." },
    { "type": "visualization", "description": "A guided session to create vivid mental imagery, such as visualizing goals or calming scenes, using a descriptive and immersive tone to enhance focus." },
    { "type": "relaxation", "description": "A session focused on physical and mental unwinding, using a gentle tone and slow pacing to release tension and promote deep rest." },
    { "type": "coaching", "description": "A motivational session offering guidance and strategies for personal or professional growth, with an upbeat, empowering tone to inspire action." },
    { "type": "sleep induction", "description": "A soothing session designed to help the listener fall asleep, using a slow, calming tone with gentle imagery and gradual pacing to encourage rest." },
    { "type": "roleplay", "description": "An interactive narrative session placing the listener in a role (e.g., explorer, detective), with a dynamic, engaging tone and pauses for imagined responses." },
    { "type": "affirmation", "description": "A session delivering positive, empowering statements to boost confidence or mindset, using a clear, uplifting tone with pauses to let affirmations sink in." },
    { "type": "gratitude practice", "description": "A session guiding the listener to reflect on things they are thankful for, with a warm, reflective tone and pauses to encourage deep emotional connection." },
    { "type": "breathwork", "description": "A session guiding specific breathing techniques to reduce stress or increase energy, with a steady, rhythmic tone and clear instructions for breath timing." },
    { "type": "motivational speech", "description": "An inspiring session delivering a powerful speech to boost determination and focus, using a passionate, uplifting tone to energize the listener." },
    { "type": "guided imagery", "description": "A session leading the listener through detailed mental scenes (e.g., a peaceful beach), with a descriptive, calming tone to enhance relaxation or creativity." },
    { "type": "problem solving", "description": "A session guiding the listener through structured steps to tackle a personal or professional challenge, with a clear, supportive tone and pauses for reflection." },
    { "type": "creative writing", "description": "A session providing prompts or scenarios to inspire creative writing, with an imaginative, encouraging tone and pauses for the listener to write." },
    { "type": "mindful movement", "description": "A session guiding gentle physical movements (e.g., yoga or stretching) with a calm, instructional tone, synchronized with breathing cues." },
    { "type": "self-compassion", "description": "A session fostering kindness toward oneself through guided reflections and affirmations, with a warm, nurturing tone to promote emotional healing." },
    { "type": "focus enhancement", "description": "A session designed to improve concentration and mental clarity, using a steady, motivating tone with techniques like anchoring or timed focus intervals." }
];
const PROVIDERS: Record<string, {name: string, envKey: string, apiBase: string, canFetchModels: boolean}> = {
  groq: { name: 'Groq', envKey: 'VITE_GROQ_API_KEY', apiBase: 'https://api.groq.com/openai/v1', canFetchModels: true },
  mistral: { name: 'Mistral', envKey: 'VITE_MISTRAL_API_KEY', apiBase: 'https://api.mistral.ai/v1', canFetchModels: true },
  chutesai: { name: 'Chutes.ai', envKey: 'VITE_CHUTESAI_API_KEY', apiBase: 'https://llm.chutes.ai/v1', canFetchModels: false },
};
const SUPPORTED_LANGUAGES: Record<string, string> = { 'fr': 'French', 'en': 'English', 'es': 'Spanish' };
const METADATA_EXAMPLE_STRUCTURE = `
{
  "id": "quantum-leap-healing", "title": "Quantum Leap Healing", "description": "A guided meditation using quantum visualization and somatic awareness to release energetic blockages and promote cellular rejuvenation.",
  "benefits": "Emotional balance, Cellular rejuvenation, Increased energy, Improved focus, Reduced anxiety, Enhanced well-being", "durationMinutes": 15, "type": "meditation", "language": "en", "createdAt": "2025-06-10", "tags": ["healing", "quantum", "meditation", "energy"],
  "script": [ { "text": "Welcome to this quantum healing session.", "pause": 3 } ],
  "recommendation_metadata": {
    "dictionary": { "basic_emotions": ["joy", "trust"], "sentiments": ["serenity", "hope", "contentment"], "cognitive_patterns": ["positive_reframing", "abundance_mindset"], "somatic_sensations": ["warmth", "flow", "expansion"], "desired_outcomes": ["energy_clearing", "stress_reduction", "inner_peace"], "sensory_channels": ["somatic_kinesthetic", "visual", "interoceptive"], "modalities": ["quantum", "somatic", "energetic_healing"], "durations": ["medium"], "intensities": ["moderate"], "techniques": ["quantum_visualization", "body_scan", "breath_focus"], "energetic_systems": ["chakras", "subtle_bodies"], "spiritual_concepts": ["universal_laws"] },
    "emotional_profile": { "primary_emotion_drivers": ["joy"], "target_sentiments": ["serenity", "contentment"] },
    "manifestation_profile": { "cognitive_patterns": ["abundance_mindset"], "somatic_sensations": ["expansion", "flow"] },
    "therapeutic_profile": { "desired_outcomes": ["stress_reduction", "inner_peace"], "sensory_channels_engaged": ["visual", "somatic_kinesthetic"], "primary_modality": ["quantum"] },
    "session_profile": { "complexity": ["intermediate"], "energy_dynamic": ["calming"], "ideal_context": ["evening_routine"], "duration": ["medium"], "intensity": ["moderate"], "techniques_used": ["quantum_visualization", "body_scan"] }
  }
}`;


// --- Styled Components ---
const ThinkingProcessContainer = styled.details` margin-top: 1rem; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; summary { padding: 8px; cursor: pointer; font-weight: 500; } pre { background-color: #e9ecef; padding: 10px; margin: 0; white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; font-size: 0.85em; }`;
const FormGrid = styled.div` display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1rem; `;
const AIGenerationContainer = styled.div` display: flex; flex-direction: column; gap: 1rem; `;
const AIInfoBox = styled.div` padding: 10px 15px; background-color: ${({ theme }) => theme.background}; border: 1px solid ${({ theme }) => theme.border}; border-radius: 4px; font-size: 0.9rem; text-align: center; color: #6c757d; `;
const TypeDescription = styled.p` font-style: italic; font-size: 0.9rem; color: #555; margin: -0.5rem 0 0.5rem 0; grid-column: 1 / -1; `;
const ChatInput = styled.textarea` width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; font-family: inherit; font-size: 1rem; `;
const FormControl = styled.div` label { display: block; margin-bottom: 0.5rem; font-weight: 500; } input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; } `;
const FullWidthFormControl = styled(FormControl)` grid-column: 1 / -1; textarea { min-height: 150px; font-family: monospace; } `;
const ModeSwitcher = styled.div` margin-bottom: 1rem; text-align: center; button { padding: 8px 16px; margin: 0 5px; cursor: pointer; border-radius: 4px; border: 1px solid transparent; } button.active { font-weight: bold; border-color: ${({ theme }) => theme.primary}; background-color: ${({ theme }) => theme.primary}20; } `;
const PrimaryButton = styled.button` padding: 10px; background-color: ${({ theme }) => theme.primary}; color: white; border: none; border-radius: 4px; cursor: pointer; &:disabled { background-color: ${({ theme }) => theme.disabledBackground}; cursor: not-allowed; } `;
const SecondaryButton = styled(PrimaryButton)` background-color: #6c757d; `;
const ActionsContainer = styled.div` display: flex; flex-direction: column; gap: 1rem; align-items: stretch; margin-top: 1.5rem; border-top: 1px solid #ccc; padding-top: 1rem; `;
const SaveButton = styled.button` padding: 10px 20px; border: none; background-color: ${({ theme }) => theme.success}; color: white; border-radius: 6px; font-weight: 600; &:disabled { background-color: ${({ theme }) => theme.disabledBackground}; cursor: not-allowed; } `;
const ClearButton = styled.button` padding: 10px 20px; border: none; background-color: #6c757d; color: white; border-radius: 6px; font-weight: 600; cursor: pointer; &:disabled { background-color: ${({ theme }) => theme.disabledBackground}; } `;
const ReviewHeader = styled.div` display: flex; justify-content: space-between; align-items: center; margin-top: 2rem; border-top: 1px solid #ccc; padding-top: 1rem; h3 { margin: 0; } `;
const TranslationOptionsContainer = styled.div` background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; display: flex; flex-direction: column; gap: 1rem; `;
const CheckboxLabel = styled.label` display: flex; align-items: center; gap: 0.5rem; font-weight: 500; `;

// --- Interfaces ---
interface IScriptSegment { text: string; pause: number; pitch?: number; rate?: number; }
interface IScript { id: string; title: string; description: string; benefits: string; durationMinutes: number; type: string; language: string; tags: string[]; createdAt: string; script: IScriptSegment[]; optimizationModel?: string; recommendation_metadata?: any; }
const initialScriptState: IScript = { id: '', title: '', description: '', benefits: '', durationMinutes: 10, type: 'hypnosis', language: 'fr', createdAt: new Date().toISOString().split('T')[0], tags: [], script: [], recommendation_metadata: undefined };
type CreationMode = 'json' | 'ai';
type GenerationStep = 'initial' | 'enhancing' | 'approval' | 'generating';
interface ActiveProviderInfo { id: string; name: string; model: string; apiKey: string; apiBase: string; }
interface NewScriptModalProps { isOpen: boolean; onClose: () => void; }
type FetchedModels = Record<string, { id: string }[]>;

const NewScriptModal: React.FC<NewScriptModalProps> = ({ isOpen, onClose }) => {
    // --- States ---
    const [mode, setMode] = useState<CreationMode>('ai');
    const [fullJsonContent, setFullJsonContent] = useState('');
    const [scriptData, setScriptData] = useState<IScript>(initialScriptState);
    const [scriptContentJson, setScriptContentJson] = useState('[]');
    const [initialUserPrompt, setInitialUserPrompt] = useState('');
    const [improvedPrompt, setImprovedPrompt] = useState('');
    const [generationStep, setGenerationStep] = useState<GenerationStep>('initial');
    const [isLoading, setIsLoading] = useState(false);
    const [creationStatus, setCreationStatus] = useState('');
    const [activeProvider, setActiveProvider] = useState<ActiveProviderInfo | null>(null);
    const [selectedTypeDescription, setSelectedTypeDescription] = useState(SESSION_TYPES.find(t => t.type === 'hypnosis')?.description || '');
    const [thinkingDots, setThinkingDots] = useState('');
    const [thinkingProcessContent, setThinkingProcessContent] = useState('');

    // --- NEW states for translation model selection ---
    const [allProviders, setAllProviders] = useState<ActiveProviderInfo[]>([]);
    const [useSameModelForTranslation, setUseSameModelForTranslation] = useState(true);
    const [translationProvider, setTranslationProvider] = useState<ActiveProviderInfo | null>(null);
    const [translationModels, setTranslationModels] = useState<FetchedModels>({});
    const [isFetchingModels, setIsFetchingModels] = useState(false);

    const isAiThinking = generationStep === 'enhancing' || generationStep === 'generating';

    const resetAllState = useCallback(() => {
        setScriptData(initialScriptState);
        setScriptContentJson('[]');
        setFullJsonContent('');
        setInitialUserPrompt('');
        setImprovedPrompt('');
        setGenerationStep('initial');
        setCreationStatus('');
        setSelectedTypeDescription(SESSION_TYPES.find(t => t.type === initialScriptState.type)?.description || '');
        setMode('ai');
        setThinkingProcessContent('');
        setUseSameModelForTranslation(true);
        setTranslationProvider(activeProvider);
    }, [activeProvider]);

    useEffect(() => {
        if (isAiThinking) {
            const interval = setInterval(() => { setThinkingDots(dots => (dots.length >= 3 ? '' : dots + '.')); }, 400);
            return () => clearInterval(interval);
        } else { setThinkingDots(''); }
    }, [isAiThinking]);

    useEffect(() => {
        const loadAISettings = async () => {
            if (!isOpen) return;
            try {
                const docRef = doc(db, 'config', 'ai_settings');
                const docSnap = await getDoc(docRef);
                if (!docSnap.exists()) { console.error("AI Settings document not found!"); return; }

                const savedData = docSnap.data();
                const loadedProviders: ActiveProviderInfo[] = [];
                const getProviderInfo = (providerId: string): ActiveProviderInfo | null => {
                    const providerConfig = PROVIDERS[providerId];
                    const providerSettings = savedData[providerId];
                    if (providerConfig && providerSettings) {
                        const apiKey = import.meta.env[providerConfig.envKey] || providerSettings.apiKey;
                        if (apiKey && providerSettings.selectedModel) {
                            return { id: providerId, name: providerConfig.name, model: providerSettings.selectedModel, apiKey, apiBase: providerConfig.apiBase };
                        }
                    }
                    return null;
                };

                for (const providerId in PROVIDERS) {
                    const info = getProviderInfo(providerId);
                    if (info) loadedProviders.push(info);
                }
                setAllProviders(loadedProviders);

                const defaultProviderId = savedData.defaultProvider;
                let providerToSet = loadedProviders.find(p => p.id === defaultProviderId) || loadedProviders[0] || null;
                
                setActiveProvider(providerToSet);
                setTranslationProvider(providerToSet);

            } catch (error) { console.error("Failed to load AI settings:", error); }
        };
        loadAISettings();
    }, [isOpen]);
    
    const handleInputChange = (field: keyof Omit<IScript, 'tags' | 'recommendation_metadata'>, value: any) => { setScriptData(prev => ({ ...prev, [field]: value })); };
    const handleTagsChange = (value: string) => { setScriptData(prev => ({ ...prev, tags: value.split(',').map(t => t.trim()) })); };
    const handleMetadataChange = (value: string) => {
        try {
            setScriptData(prev => ({ ...prev, recommendation_metadata: JSON.parse(value) }));
        } catch (e) {
            console.error("Invalid JSON for metadata");
        }
    };
    const handleTypeChange = (newType: string) => {
        const typeInfo = SESSION_TYPES.find(t => t.type === newType);
        handleInputChange('type', newType);
        setSelectedTypeDescription(typeInfo?.description || '');
    };
    
    const streamAIResponse = useCallback(async (
      provider: ActiveProviderInfo, systemPrompt: string, userPrompt: string,
      onResultUpdate: (chunk: string) => void, onThinkUpdate: (chunk: string) => void
    ) => {
        const body = { model: provider.model, messages: [{ role: 'system', content: systemPrompt }, { role: 'user', content: userPrompt }], stream: true };
        const response = await fetch(`${provider.apiBase}/chat/completions`, { method: 'POST', headers: { 'Authorization': `Bearer ${provider.apiKey}`, 'Content-Type': 'application/json' }, body: JSON.stringify(body), });
        if (!response.ok || !response.body) { const errorData = await response.json(); throw new Error(errorData.error?.message || `Request failed`); }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '', insideThinkTag = false;

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            for (const line of lines) {
                if (!line.startsWith('data: ')) continue;
                const jsonData = line.substring(6);
                if (jsonData.trim() === '[DONE]') continue;
                try {
                    const content = JSON.parse(jsonData).choices[0]?.delta?.content || '';
                    if (!content) continue;
                    let currentContent = content;
                    if (currentContent.includes('<think>')) { insideThinkTag = true; const parts = currentContent.split('<think>'); onResultUpdate(parts[0]); onThinkUpdate(parts[1]); } 
                    else if (currentContent.includes('</think>')) { insideThinkTag = false; const parts = currentContent.split('</think>'); onThinkUpdate(parts[0]); onResultUpdate(parts[1]); } 
                    else { if (insideThinkTag) onThinkUpdate(content); else onResultUpdate(content); }
                } catch (e) { /* Ignore incomplete JSON */ }
            }
        }
    }, []);

    const handleEnhancePrompt = useCallback(async () => {
        if (!initialUserPrompt || !activeProvider) return;
        setGenerationStep('enhancing'); setImprovedPrompt(''); setThinkingProcessContent('');
        const metaPrompt = `You are a creative assistant specializing in personal growth session scripts. A user wants to create a ${scriptData.durationMinutes}-minute guided session in ${SUPPORTED_LANGUAGES[scriptData.language]} about "${scriptData.type}".
        Their initial idea is: "${initialUserPrompt}".
        Expand this into a detailed prompt for a scriptwriter AI, including title, description, benefits, tags, a URL-friendly ID, and a step-by-step plan.
        Respond ONLY with the enhanced prompt.`;
        try {
            await streamAIResponse(activeProvider, "You are a helpful creative assistant.", metaPrompt, (c) => setImprovedPrompt(p => p + c), (c) => setThinkingProcessContent(p => p + c));
            setGenerationStep('approval');
        } catch (e) { alert(`Error enhancing prompt: ${e instanceof Error ? e.message : e}`); setGenerationStep('initial'); }
    }, [initialUserPrompt, scriptData, activeProvider, streamAIResponse]);

    const handleGenerateFinalScript = useCallback(async () => {
        if (!improvedPrompt || !activeProvider) return;
        setGenerationStep('generating'); setThinkingProcessContent(''); let finalJsonString = '';
        const finalSystemPrompt = `You are a scriptwriter AI. Generate a complete script in JSON format based on the user's prompt. The JSON output MUST follow this exact structure, including generating detailed 'recommendation_metadata' based on the session's content. Do not include any extra text or markdown formatting. Here is the required schema:\n${METADATA_EXAMPLE_STRUCTURE}`;
        try {
            await streamAIResponse(activeProvider, finalSystemPrompt, improvedPrompt, (c) => { finalJsonString += c; }, (c) => setThinkingProcessContent(p => p + c));
            const parsedScript = JSON.parse(finalJsonString);
            setScriptData(prev => ({ ...prev, ...parsedScript, optimizationModel: activeProvider.model, language: prev.language }));
            setScriptContentJson(JSON.stringify(parsedScript.script || [], null, 2));
            setGenerationStep('initial');
        } catch (e) { alert(`Error generating/parsing script: ${e instanceof Error ? e.message : e}. Raw output: ${finalJsonString}`); setGenerationStep('approval'); }
    }, [improvedPrompt, activeProvider, streamAIResponse]);

    const fetchTranslationModels = async (providerId: string) => {
        const provider = allProviders.find(p => p.id === providerId);
        if (!provider || !PROVIDERS[provider.id].canFetchModels) return;
        setIsFetchingModels(true);
        try {
            const response = await fetch(`${provider.apiBase}/models`, { headers: { 'Authorization': `Bearer ${provider.apiKey}` } });
            const data = await response.json();
            if (!response.ok) throw new Error(data.error?.message);
            setTranslationModels(prev => ({ ...prev, [providerId]: data.data }));
        } catch (e) { alert(`Failed to fetch models for ${provider.name}: ${e instanceof Error ? e.message : e}`); }
        finally { setIsFetchingModels(false); }
    };
    
    // --- MODIFIED: Recursive translation function ---
    const translateScript = async (originalScript: IScript, targetLang: string, provider: ActiveProviderInfo): Promise<IScript> => {
        const keysToTranslate: (keyof IScript | 'text')[] = ['id', 'title', 'description', 'benefits', 'tags', 'text'];
        const translatedData: any = {};
    
        const translateValue = async (value: any, key: string): Promise<any> => {
            if (typeof value !== 'string' || value.trim() === '') return value;
            const systemPrompt = `You are an expert translator. Translate the following text to ${SUPPORTED_LANGUAGES[targetLang]}. Respond ONLY with the translated text, without any embellishments or explanations. For 'id' fields, translate the content but keep it in a URL-friendly-slug-format.`;
            try {
                let translatedText = '';
                await streamAIResponse(provider, systemPrompt, value, c => translatedText += c, () => {});
                return translatedText.trim();
            } catch (e) { console.error(`Failed to translate key '${key}' to ${targetLang}`, e); return `TRANSLATION_ERROR: ${value}`; }
        };
    
        const traverseAndTranslate = async (data: any): Promise<any> => {
            if (Array.isArray(data)) {
                return Promise.all(data.map(item => traverseAndTranslate(item)));
            }
            if (typeof data === 'object' && data !== null) {
                const newObj: any = {};
                for (const key in data) {
                    if (keysToTranslate.includes(key as any)) {
                        newObj[key] = await translateValue(data[key], key);
                    } else if (typeof data[key] === 'object' && data[key] !== null) {
                        newObj[key] = await traverseAndTranslate(data[key]); // Recurse
                    } else {
                        newObj[key] = data[key]; // Copy value
                    }
                }
                return newObj;
            }
            return data;
        };
        
        const finalTranslatedScript = await traverseAndTranslate(originalScript);
        finalTranslatedScript.language = targetLang; // Set the correct language code
        return finalTranslatedScript;
    };
    
    const handleCreateClick = async () => {
        setIsLoading(true);
        try {
            const finalScriptData: IScript = mode === 'ai'
                ? { ...scriptData, script: JSON.parse(scriptContentJson) }
                : JSON.parse(fullJsonContent);

            const { language, type, id } = finalScriptData;
            const sharedKey = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
            if (!sharedKey) throw new Error("Encryption key is missing!");

            const languagesToProcess = Object.keys(SUPPORTED_LANGUAGES);
            for (const lang of languagesToProcess) {
                setCreationStatus(`Processing ${SUPPORTED_LANGUAGES[lang]}...`);
                let scriptToUpload = lang === language 
                    ? finalScriptData 
                    : await translateScript(finalScriptData, lang, useSameModelForTranslation ? activeProvider! : translationProvider!);

                scriptToUpload.script.forEach(segment => segment.pause = segment.pause * 1000);
                
                const encrypted = CryptoJS.AES.encrypt(JSON.stringify(scriptToUpload), sharedKey).toString();
                const docRef = doc(db, `SessionScripts/${lang}/${type}/${id}`);
                await setDoc(docRef, { encrypted });
            }
            setCreationStatus('Done! All languages processed.');
            setTimeout(() => { resetAllState(); onClose(); }, 2000);
        } catch (e) {
            alert(`Error during script creation: ${e instanceof Error ? e.message : e}`);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} title="Create New Script">
            {/* ... JSX remains largely the same until the end ... */}
            {mode === 'ai' && (
                <AIGenerationContainer>
                    <AIInfoBox>{activeProvider ? `Using: ${activeProvider.name} (${activeProvider.model})` : "No active AI provider found. Check AI Settings."}</AIInfoBox>
                    {isAiThinking && thinkingProcessContent && <ThinkingProcessContainer><summary>View AI thought process...</summary><pre>{thinkingProcessContent}</pre></ThinkingProcessContainer>}
                    <FormGrid>
                        <FormControl><label>Language</label><select value={scriptData.language} onChange={e => handleInputChange('language', e.target.value)} disabled={isAiThinking}>{Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => <option key={code} value={code}>{name}</option>)}</select></FormControl>
                        <FormControl><label>Session Type</label><select value={scriptData.type} onChange={e => handleTypeChange(e.target.value)} disabled={isAiThinking}>{SESSION_TYPES.map(t => <option key={t.type} value={t.type}>{t.type}</option>)}</select></FormControl>
                        {selectedTypeDescription && <TypeDescription>{selectedTypeDescription}</TypeDescription>}
                        <FullWidthFormControl><label>Duration (minutes)</label><input type="number" value={scriptData.durationMinutes} onChange={e => handleInputChange('durationMinutes', parseInt(e.target.value, 10))} disabled={isAiThinking}/></FullWidthFormControl>
                    </FormGrid>
                    <FormControl><label>Step 1: Describe your session idea</label><ChatInput placeholder="e.g., A session about overcoming procrastination, using a river metaphor..." value={initialUserPrompt} onChange={e => setInitialUserPrompt(e.target.value)} disabled={isAiThinking} /></FormControl>
                    {generationStep === 'initial' && <PrimaryButton onClick={handleEnhancePrompt} disabled={!initialUserPrompt || isAiThinking}>Enhance Prompt</PrimaryButton>}
                    {isAiThinking && <AIInfoBox>{generationStep === 'enhancing' ? 'Assistant is enhancing your idea' : 'Generating final script'}{thinkingDots}</AIInfoBox>}
                    {generationStep === 'approval' && (
                        <>
                            <FormControl><label>Step 2: Review Enhanced Prompt</label><ChatInput value={improvedPrompt} onChange={e => setImprovedPrompt(e.target.value)} style={{minHeight: '200px'}} disabled={isAiThinking}/></FormControl>
                            <ActionsContainer style={{borderTop: 'none', marginTop: 0, paddingTop: 0}}>
                                <PrimaryButton onClick={handleGenerateFinalScript} disabled={isAiThinking}>Approve & Generate Script</PrimaryButton>
                                <SecondaryButton onClick={handleEnhancePrompt} disabled={isAiThinking}>Regenerate Suggestion</SecondaryButton>
                                <ClearButton onClick={() => { setGenerationStep('initial'); setImprovedPrompt(''); }} disabled={isAiThinking} style={{marginLeft: 'auto'}}>Edit Idea</ClearButton>
                            </ActionsContainer>
                        </>
                    )}
                    <ReviewHeader><h3>Review & Edit Final Script</h3>{scriptData.id && generationStep === 'initial' && <SecondaryButton onClick={handleGenerateFinalScript} disabled={isAiThinking}>Regenerate Script</SecondaryButton>}</ReviewHeader>
                    <FormGrid>
                        <FormControl><label>Title</label><input value={scriptData.title} onChange={e => handleInputChange('title', e.target.value)} /></FormControl>
                        <FormControl><label>ID (Slug)</label><input value={scriptData.id} onChange={e => handleInputChange('id', e.target.value)} /></FormControl>
                        <FullWidthFormControl><label>Description</label><textarea value={scriptData.description} onChange={e => handleInputChange('description', e.target.value)} /></FullWidthFormControl>
                        <FullWidthFormControl><label>Benefits (comma-separated)</label><input value={scriptData.benefits} onChange={e => handleInputChange('benefits', e.target.value)} /></FullWidthFormControl>
                        <FormControl><label>Tags (comma-separated)</label><input value={scriptData.tags.join(', ')} onChange={e => handleTagsChange(e.target.value)} /></FormControl>
                        <FullWidthFormControl><label>Recommendation Metadata (JSON)</label><textarea style={{minHeight: '200px'}} value={JSON.stringify(scriptData.recommendation_metadata || {}, null, 2)} onChange={e => handleMetadataChange(e.target.value)} /></FullWidthFormControl>
                        <FullWidthFormControl><label>Script Content (JSON Array)</label><textarea style={{minHeight: '200px'}} value={scriptContentJson} onChange={e => setScriptContentJson(e.target.value)} /></FullWidthFormControl>
                    </FormGrid>
                </AIGenerationContainer>
            )}

            {mode === 'json' && ( <FullWidthFormControl><label>Paste Full Script JSON here</label><textarea value={fullJsonContent} onChange={e => setFullJsonContent(e.target.value)} disabled={isLoading} /></FullWidthFormControl> )}

            <ActionsContainer>
                <TranslationOptionsContainer>
                    <CheckboxLabel>
                        <input type="checkbox" checked={useSameModelForTranslation} onChange={() => setUseSameModelForTranslation(prev => !prev)} />
                        Use default provider for translations
                    </CheckboxLabel>
                    {!useSameModelForTranslation && (
                        <FormGrid>
                            <FormControl>
                                <label>Translation Provider</label>
                                <select value={translationProvider?.id || ''} onChange={(e) => setTranslationProvider(allProviders.find(p => p.id === e.target.value) || null)}>
                                    {allProviders.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                                </select>
                            </FormControl>
                            <FormControl>
                                <label>Translation Model</label>
                                {translationProvider && PROVIDERS[translationProvider.id].canFetchModels ? (
                                    <select 
                                        value={translationProvider.model} 
                                        onChange={e => setTranslationProvider(p => p ? {...p, model: e.target.value} : null)}
                                        onClick={() => !translationModels[translationProvider.id] && fetchTranslationModels(translationProvider.id)}
                                        disabled={isFetchingModels}
                                    >
                                        <option value={translationProvider.model}>{translationProvider.model} (saved)</option>
                                        {translationModels[translationProvider.id]?.map(m => <option key={m.id} value={m.id}>{m.id}</option>)}
                                    </select>
                                ) : (
                                    <input value={translationProvider?.model || ''} onChange={e => setTranslationProvider(p => p ? {...p, model: e.target.value} : null)} />
                                )}
                            </FormControl>
                        </FormGrid>
                    )}
                </TranslationOptionsContainer>
                <div style={{display: 'flex', gap: '1rem', width: '100%'}}>
                    <SaveButton onClick={handleCreateClick} disabled={isLoading || isAiThinking || (mode === 'ai' && !scriptData.id) || (mode === 'json' && !fullJsonContent)}> {isLoading ? creationStatus || 'Processing...' : 'Create Script & Translations'} </SaveButton>
                    <ClearButton onClick={resetAllState} disabled={isLoading || isAiThinking}>Clear All</ClearButton>
                </div>
            </ActionsContainer>
        </Modal>
    );
};

export default NewScriptModal;