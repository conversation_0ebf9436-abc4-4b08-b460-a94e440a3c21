// src/firebase.ts
import { initializeApp, FirebaseApp } from "firebase/app";
import { getAuth, Auth } from "firebase/auth";
import { 
  getFirestore, 
  Firestore, 
  initializeFirestore,
  persistentLocalCache, // The factory function for creating a persistent cache instance
} from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";
import { getFunctions, Functions } from "firebase/functions"; // Added getFunctions and Functions
import clientEnv from "./config/clientEnvironment";

// Your web app's Firebase configuration
const firebaseConfig = clientEnv.firebase;

// Initialize Firebase App
const firebaseApp: FirebaseApp = initializeApp(firebaseConfig);

// Initialize Authentication, Storage, and Functions
const auth: Auth = getAuth(firebaseApp);
const storage: FirebaseStorage = getStorage(firebaseApp);
const functions: Functions = getFunctions(firebaseApp); // Initialize Functions

// --- Firestore Initialization with Persistence ---
// We declare 'db' here so it's available outside the try/catch scope.
let db: Firestore;

try {
  // This is the modern and recommended way to enable persistence.
  // We pass the settings directly during initialization.
  db = initializeFirestore(firebaseApp, {
    localCache: persistentLocalCache({
      // We don't need any specific settings here for multi-tab sync, 
      // the default behavior of PersistentLocalCache handles it.
    })
  });
  console.log("Firebase persistence with multi-tab support enabled successfully.");

} catch (err: any) {
  // This block catches initialization errors, like when the browser doesn't support it.
  console.error("Error initializing Firestore with persistence:", err);
  if (err.code === 'unimplemented') {
    // The current browser does not support the features required to enable persistence.
    console.error("Firebase persistence is not supported in this browser environment.");
  }
  
  // Fallback to a regular in-memory instance of Firestore if persistence fails.
  // This ensures the app continues to work online.
  console.warn("Falling back to a regular in-memory Firestore instance.");
  db = getFirestore(firebaseApp);
}

// Export the initialized services to be used throughout the app
export { firebaseApp, db, auth, storage, functions }; // Added functions to export