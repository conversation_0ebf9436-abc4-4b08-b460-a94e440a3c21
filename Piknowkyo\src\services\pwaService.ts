//src/services/pwaService.ts
export interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

let deferredPrompt: BeforeInstallPromptEvent | null = null;
let isInstalled = false;

export const setDeferredPrompt = (promptEvent: BeforeInstallPromptEvent) => {
  deferredPrompt = promptEvent;
};

export const setInstalled = () => {
  isInstalled = true;
};

export const getInstallStatus = (): boolean => {
  return isInstalled;
};

export const promptInstall = async (): Promise<boolean> => {
  if (!deferredPrompt) {
    return false;
  }
  deferredPrompt.prompt();
  const { outcome } = await deferredPrompt.userChoice;
  deferredPrompt = null;
  return outcome === 'accepted';
};