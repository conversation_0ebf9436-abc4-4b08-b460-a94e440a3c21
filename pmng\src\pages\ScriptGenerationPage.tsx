// /src/pages/ScriptGenerationPage.tsx

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import type { Script } from '../models/Script';
import { FiCpu, FiClipboard, FiDownloadCloud } from 'react-icons/fi';

// --- Styled Components ---
const PageWrapper = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const PhaseHeader = styled.h2`
  border-bottom: 2px solid ${({ theme }) => theme.primary};
  padding-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const Section = styled.section`
  background-color: ${({ theme }) => theme.surface};
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: ${({ theme }) => theme.cardShadow};
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const PromptTextArea = styled.textarea`
  width: 100%;
  min-height: 150px;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid ${({ theme }) => theme.border};
  font-family: 'Inter', sans-serif;
  font-size: 1rem;
  resize: vertical;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background-color: ${({ theme }) => theme.primary};
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  align-self: flex-start;
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ResultSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  @media (max-width: 900px) {
    grid-template-columns: 1fr;
  }
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
`;

const FormControl = styled.div`
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  input, select {
    width: 100%;
    padding: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 4px;
    box-sizing: border-box;
  }
`;

// --- Constants and Types ---
const PROVIDERS: Record<string, {name: string, envKey: string, apiBase: string, canFetchModels: boolean}> = {
  groq: { name: 'Groq', envKey: 'VITE_GROQ_API_KEY', apiBase: 'https://api.groq.com/openai/v1', canFetchModels: true },
  mistral: { name: 'Mistral', envKey: 'VITE_MISTRAL_API_KEY', apiBase: 'https://api.mistral.ai/v1', canFetchModels: true },
  chutesai: { name: 'Chutes.ai', envKey: 'VITE_CHUTESAI_API_KEY', apiBase: 'https://llm.chutes.ai/v1', canFetchModels: false },
};

interface ActiveProviderInfo { id: string; name: string; model: string; apiKey: string; apiBase: string; }
type FetchedModels = Record<string, { id: string }[]>;
type GeneratedPayload = Script & { script_content?: string; };

const JSON_STRUCTURE_TEMPLATE = `{
  "id": "unique_session_identifier", "title": "", "description": "", "benefits": "", "durationMinutes": null, "type": "", "language": "en", "createdAt": "${new Date().toISOString().split('T')[0]}", "tags": [],
  "recommendation_metadata": { "dictionary": { "basic_emotions": [], "sentiments": [], "cognitive_patterns": [], "somatic_sensations": [], "desired_outcomes": [], "sensory_channels": [], "modalities": [], "durations": [], "intensities": [], "techniques": [], "energetic_systems": [], "spiritual_concepts": [] }, "emotional_profile": { "primary_emotion_drivers": [], "target_sentiments": [] }, "manifestation_profile": { "cognitive_patterns": [], "somatic_sensations": [] }, "therapeutic_profile": { "desired_outcomes": [], "sensory_channels_engaged": [], "primary_modality": [] }, "session_profile": { "complexity": [], "energy_dynamic": [], "ideal_context": [], "duration": [], "intensity": [], "techniques_used": [] } }
}`;

const ScriptGenerationPage: React.FC = () => {
  type GenerationPhase = 'idea' | 'review_prompt' | 'review_script';

  const [phase, setPhase] = useState<GenerationPhase>('idea');
  const [initialIdea, setInitialIdea] = useState('');
  const [improvedPrompt, setImprovedPrompt] = useState('');
  const [generatedScript, setGeneratedScript] = useState<GeneratedPayload | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);

  const [allProviders, setAllProviders] = useState<ActiveProviderInfo[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<ActiveProviderInfo | null>(null);
  const [fetchedModels, setFetchedModels] = useState<FetchedModels>({});
  const [isFetchingModels, setIsFetchingModels] = useState(false);

  useEffect(() => {
    const loadAiConfig = async () => {
      const docRef = doc(db, 'config', 'ai_settings');
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) {
          console.error("AI Settings document not found!");
          return;
      }
      const savedData = docSnap.data();
      const loadedProviders: ActiveProviderInfo[] = [];

      for (const providerId in PROVIDERS) {
        const providerConfig = PROVIDERS[providerId];
        const providerSettings = savedData[providerId];
        if (providerConfig && providerSettings) {
          const apiKey = import.meta.env[providerConfig.envKey] || providerSettings.apiKey;
          if (apiKey && providerSettings.selectedModel) {
            loadedProviders.push({ id: providerId, name: providerConfig.name, model: providerSettings.selectedModel, apiKey, apiBase: providerConfig.apiBase });
          }
        }
      }
      setAllProviders(loadedProviders);
      
      const defaultProviderId = savedData.defaultProvider || 'groq';
      const providerToSet = loadedProviders.find(p => p.id === defaultProviderId) || loadedProviders[0] || null;
      setSelectedProvider(providerToSet);
    };
    loadAiConfig();
  }, []);

  const fetchModelsForProvider = useCallback(async (providerId: string) => {
    const provider = allProviders.find(p => p.id === providerId);
    if (!provider || !PROVIDERS[providerId].canFetchModels) return;

    setIsFetchingModels(true);
    try {
      const response = await fetch(`${provider.apiBase}/models`, { headers: { 'Authorization': `Bearer ${provider.apiKey}` } });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error?.message);
      setFetchedModels(prev => ({ ...prev, [providerId]: data.data }));
    } catch (e) {
      alert(`Failed to fetch models for ${provider.name}: ${e instanceof Error ? e.message : e}`);
    } finally {
      setIsFetchingModels(false);
    }
  }, [allProviders]);

  const callAI = async (provider: ActiveProviderInfo, systemPrompt: string, userPrompt: string) => {
    const response = await fetch(`${provider.apiBase}/chat/completions`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${provider.apiKey}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: provider.model,
        messages: [{ role: 'system', content: systemPrompt }, { role: 'user', content: userPrompt }],
        temperature: 0.7,
        response_format: { type: 'json_object' }
      })
    });
    const data = await response.json();
    if (!response.ok) throw new Error(data.error?.message || "AI API call failed");
    return data.choices[0].message.content;
  };

  const handleImprovePrompt = async () => {
    if (!selectedProvider) return;
    setIsLoading(true);
    const systemPrompt = "You are a creative director specializing in wellness and hypnosis scripts. Take the user's idea and transform it into a detailed, structured prompt suitable for generating a full session script. The output must be a single JSON object with one key: 'improved_prompt'. Focus on atmosphere, tone, key phases (induction, deepening, core work, emergence), and target audience.";
    try {
      const response = await callAI(selectedProvider, systemPrompt, initialIdea);
      const parsed = JSON.parse(response);
      setImprovedPrompt(parsed.improved_prompt);
      setPhase('review_prompt');
    } catch (error) {
      alert(`Error improving prompt: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateScript = async () => {
    if (!selectedProvider) return;
    setIsLoading(true);
    const systemPrompt = `You are a scriptwriter and metadata analyst. Based on the provided prompt, you have two tasks. First, write a complete, immersive session script. Second, analyze the script and fill out the provided JSON structure with the appropriate metadata. Adhere strictly to the options provided in the 'recommendation_metadata.dictionary'. The final output must be a single, valid JSON object containing the full script AND the metadata. The script text itself should be inside a key named 'script_content'.`;
    const userPrompt = `PROMPT: """${improvedPrompt}"""\n\nJSON_TEMPLATE: """${JSON_STRUCTURE_TEMPLATE}"""`;
    try {
      const response = await callAI(selectedProvider, systemPrompt, userPrompt);
      const parsedScript = JSON.parse(response);
      setGeneratedScript(parsedScript);
      setPhase('review_script');
    } catch (error) {
      alert(`Error generating script: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageWrapper>
      <h1>AI Script Co-Pilot</h1>

      <Section>
        <PhaseHeader><FiCpu /> AI Configuration</PhaseHeader>
        <FormGrid>
          <FormControl>
            <label>Provider</label>
            <select
              value={selectedProvider?.id || ''}
              onChange={(e) => setSelectedProvider(allProviders.find(p => p.id === e.target.value) || null)}
              disabled={isLoading || allProviders.length === 0}
            >
              {allProviders.length > 0 ? (
                allProviders.map(p => <option key={p.id} value={p.id}>{p.name}</option>)
              ) : (
                <option>Loading providers...</option>
              )}
            </select>
          </FormControl>
          
          <FormControl>
            <label>Model</label>
            {selectedProvider && PROVIDERS[selectedProvider.id].canFetchModels ? (
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <select
                  style={{ flexGrow: 1 }}
                  value={selectedProvider.model}
                  onChange={e => setSelectedProvider(p => p ? { ...p, model: e.target.value } : null)}
                  disabled={isFetchingModels || !selectedProvider}
                >
                  <option value={selectedProvider.model}>{selectedProvider.model} (saved)</option>
                  {fetchedModels[selectedProvider.id]?.map(m => <option key={m.id} value={m.id}>{m.id}</option>)}
                </select>
                <Button style={{ padding: '8px' }} onClick={() => fetchModelsForProvider(selectedProvider!.id)} disabled={isFetchingModels}>Fetch</Button>
              </div>
            ) : (
              <input
                type="text"
                placeholder="Enter model name manually"
                value={selectedProvider?.model || ''}
                onChange={e => setSelectedProvider(p => p ? { ...p, model: e.target.value } : null)}
                disabled={!selectedProvider}
              />
            )}
          </FormControl>
        </FormGrid>
      </Section>

      <Section>
        <PhaseHeader>Phase 1: Votre Idée</PhaseHeader>
        <label htmlFor="initial-idea">Décrivez la séance que vous souhaitez créer.</label>
        <PromptTextArea
          id="initial-idea"
          value={initialIdea}
          onChange={(e) => setInitialIdea(e.target.value)}
          placeholder="Ex: Une courte méditation pour calmer l'anxiété avant une réunion importante..."
        />
        <Button onClick={handleImprovePrompt} disabled={isLoading || !initialIdea || !selectedProvider?.model}>
          {isLoading ? 'Analyse...' : "Améliorer le Prompt avec l'IA"}
        </Button>
      </Section>

      {phase === 'review_prompt' && (
        <Section>
          <PhaseHeader><FiClipboard /> Phase 2: Validation du Brief</PhaseHeader>
          <p>L'IA a transformé votre idée en un brief créatif. Modifiez-le si nécessaire avant de générer le script.</p>
          <label htmlFor="improved-prompt">Prompt Amélioré par l'IA:</label>
          <PromptTextArea id="improved-prompt" rows={10} value={improvedPrompt} onChange={(e) => setImprovedPrompt(e.target.value)} />
          <Button onClick={handleGenerateScript} disabled={isLoading || !improvedPrompt}>
            {isLoading ? 'Génération en cours...' : 'Valider et Générer le Script Complet'}
          </Button>
        </Section>
      )}

      {phase === 'review_script' && generatedScript && (
        <Section>
          <PhaseHeader><FiDownloadCloud /> Phase 3: Résultat Final</PhaseHeader>
          <p>Voici le script et les métadonnées générés. Éditez tous les champs avant de sauvegarder.</p>
          <ResultSection>
            <Section>
              <h3>Métadonnées (JSON)</h3>
              <PromptTextArea rows={25} value={JSON.stringify(generatedScript, null, 2)} onChange={(e) => setGeneratedScript(JSON.parse(e.target.value))} />
            </Section>
            <Section>
              <h3>Contenu du Script</h3>
              <PromptTextArea rows={25} value={generatedScript.script_content || ''} onChange={e => setGeneratedScript(prev => prev ? {...prev, script_content: e.target.value} : null)} />
            </Section>
          </ResultSection>
          <Button onClick={() => alert("Sauvegarde non implémentée.")} disabled={isLoading}>
            Sauvegarder le Script
          </Button>
        </Section>
      )}
    </PageWrapper>
  );
};

export default ScriptGenerationPage;