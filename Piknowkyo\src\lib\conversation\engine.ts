// /src/lib/conversation/engine.ts

import { SessionScript } from '../../store/useAppStore';
import { Dictionary, SessionRecommendationTags } from '../../types/definitions';
import { UserProfile, Language } from './types';

export interface ScoredSession {
  session: SessionScript;
  score: number;
  debug?: string[];
}

const CONFIDENCE_THRESHOLD = 10;

function hasIntersection(userTags: string[] = [], sessionTags: string[] = []): boolean {
  if (!userTags.length || !sessionTags.length) return false;
  return userTags.some(tag => sessionTags.includes(tag));
}

function keywordMatchScore(
  session: SessionScript,
  userProfile: UserProfile,
  dictionary: Dictionary,
  lang: Language
): { score: number, debugDetails: string[] } {
  let score = 0;
  const debugDetails: string[] = [];
  
  const benefitsString = typeof session.benefits === 'string' ? session.benefits : '';
  // FIX: Explicitly type tagsArray as string[] to avoid 'never' type inference on empty arrays.
  const tagsArray: string[] = Array.isArray(session.tags) ? session.tags : [];

  const textToSearch: string[] = [
    session.title.toLowerCase(),
    session.description.toLowerCase(),
    benefitsString,
    ...(tagsArray.map(tag => typeof tag === 'string' ? tag.toLowerCase() : '')), // Added check for safety
  ];
  const fullText = textToSearch.join(' ');

  const createRegex = (term: string): RegExp => {
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    return new RegExp(`\\b${escapedTerm}\\b`, 'gi');
  }

  userProfile.emotions.forEach(emotionKey => {
    const emotionTerm = dictionary.basic_emotions.find(e => e.key === emotionKey)?.name[lang] || emotionKey;
    if (fullText.match(createRegex(emotionTerm.toLowerCase()))) {
      score += 2;
      debugDetails.push(`+2 keyword (emotion: ${emotionTerm})`);
    }
    dictionary.sentiments.forEach(sentiment => {
        if (sentiment.composition?.includes(`basic_emotion:${emotionKey}`)) {
            const sentimentTerm = sentiment.name[lang];
            if (fullText.match(createRegex(sentimentTerm.toLowerCase()))) {
                score += 1;
                debugDetails.push(`+1 keyword (composed sentiment: ${sentimentTerm} from ${emotionKey})`);
            }
        }
    });
  });

  userProfile.cognitive_patterns.forEach(patternKey => {
    const term = dictionary.cognitive_patterns.find(p => p.key === patternKey)?.name[lang] || patternKey;
    if (fullText.match(createRegex(term.toLowerCase()))) {
      score += 2;
      debugDetails.push(`+2 keyword (cognitive: ${term})`);
    }
  });
  
  userProfile.somatic_markers.forEach(markerKey => {
    const term = dictionary.somatic_sensations.find(s => s.key === markerKey)?.name[lang] || markerKey;
    if (fullText.match(createRegex(term.toLowerCase()))) {
      score += 2;
      debugDetails.push(`+2 keyword (somatic: ${term})`);
    }
  });

  userProfile.desired_outcomes.forEach(outcomeKey => {
    const term = dictionary.desired_outcomes.find(o => o.key === outcomeKey)?.name[lang] || outcomeKey;
    if (fullText.match(createRegex(term.toLowerCase()))) {
      score += 3;
      debugDetails.push(`+3 keyword (outcome: ${term})`);
    }
  });

  return { score, debugDetails };
}


export function calculateRecommendations(
  allSessions: SessionScript[],
  userProfile: UserProfile,
  dictionary: Dictionary,
  lang: Language
): { bestMatch: ScoredSession | null; hasConfidentResult: boolean } {
  
  if (!userProfile) {
    return { bestMatch: null, hasConfidentResult: false };
  }

  const scoredSessions = allSessions.map((session): ScoredSession => {
    const metadata: SessionRecommendationTags | undefined = session.recommendation_metadata;
    let score = 0;
    const debug: string[] = [];

    if (metadata) {
      if (hasIntersection(userProfile.desired_outcomes, metadata.cultivates_outcomes)) {
        score += 8;
        debug.push(`+8 meta (outcome)`);
      }
      if (hasIntersection(userProfile.somatic_markers, metadata.addresses_somatic_sensations)) {
        score += 6;
        debug.push(`+6 meta (somatic)`);
      }
      if (hasIntersection(userProfile.cognitive_patterns, metadata.addresses_cognitive_patterns)) {
        score += 6;
        debug.push(`+6 meta (cognitive)`);
      }
      if (hasIntersection(userProfile.behavioral_patterns, metadata.addresses_behavioral_patterns)) {
        score += 5;
        debug.push(`+5 meta (behavioral)`);
      }
      userProfile.emotions.forEach(emotionKey => {
        const addressedBySession = metadata.addresses_emotions_or_sentiments || [];
        addressedBySession.forEach(sessionTagKey => {
          if (emotionKey === sessionTagKey) {
            score += 7;
            debug.push(`+7 meta (direct emotion: ${emotionKey})`);
          } else {
            const sentimentEntry = dictionary.sentiments.find(s => s.key === sessionTagKey);
            if (sentimentEntry?.composition?.includes(`basic_emotion:${emotionKey}`)) {
              score += 5;
              debug.push(`+5 meta (composed emotion: ${emotionKey} -> ${sessionTagKey})`);
            }
          }
        });
      });
      if (hasIntersection(userProfile.contextual_triggers, metadata.ideal_for_contexts)) {
        score += 4;
        debug.push(`+4 meta (context)`);
      }
      if (hasIntersection(userProfile.energetic_states, metadata.addresses_energetic_states)) {
        score += 4;
        debug.push(`+4 meta (energy)`);
      }
      if (hasIntersection(userProfile.metaphorical_images, metadata.metaphorical_themes)) {
        score += 3;
        debug.push(`+3 meta (metaphor)`);
      }
      if (metadata.primary_modalities?.length) score += 1;
      if (metadata.techniques_used?.length) score += 1;
    } else {
      debug.push("No structured metadata for this session.");
    }

    const keywordResult = keywordMatchScore(session, userProfile, dictionary, lang);
    score += keywordResult.score;
    if(keywordResult.debugDetails.length > 0) {
        debug.push(...keywordResult.debugDetails.map(d => `keyword: ${d}`));
    }
    

    return { session, score, debug };
  });

  const sorted = scoredSessions.filter(s => s.score > 0).sort((a, b) => b.score - a.score);
    
  // console.log("Scored Sessions:", sorted.map(s => ({ title: s.session.title, id: s.session.id, score: s.score, debug: s.debug })));

  const bestMatch = sorted.length > 0 ? sorted[0] : null;

  return {
    bestMatch,
    hasConfidentResult: bestMatch ? bestMatch.score >= CONFIDENCE_THRESHOLD : false,
  };
}