{"name": "piknowkyo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run && vite", "start": "npm run && vite", "build": "tsc -b && vite build", "test": "vitest", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && firebase deploy", "deploy:prod": "./deploy.sh", "test:prod": "node test-production.cjs", "upload:content": "node scripts/upload-content-to-firestore.js"}, "dependencies": {"@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/push-notifications": "^7.0.1", "@google-cloud/storage": "^7.16.0", "@google-cloud/text-to-speech": "^6.1.0", "@stripe/stripe-js": "^7.3.1", "dexie": "^4.0.11", "firebase": "^11.9.1", "fs-extra": "^11.3.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "inquirer": "^12.7.0", "localforage": "^1.10.0", "polished": "^4.3.1", "react": "^19.1.0", "react-country-flag": "^3.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-icons": "^5.1.0", "react-router-dom": "^7.6.0", "recharts": "^3.0.2", "stripe": "^18.2.1", "styled-components": "^6.1.18", "uuid": "^11.1.0", "web-vitals": "^5.0.2", "workbox-window": "^7.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/node": "^22.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "crypto-js": "^4.2.0", "dotenv": "^16.6.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "firebase-admin": "^13.4.0", "globals": "^16.0.0", "terser": "^5.41.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}