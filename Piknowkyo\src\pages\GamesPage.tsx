// src/pages/GamesPage.tsx

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useShallow } from 'zustand/react/shallow';
import { useLocation, useNavigate } from 'react-router-dom'; // Import useLocation and useNavigate

// --- IMPORTS ---
import { useAppStore } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import { SavedGameState } from '../games/common/models';
import { GAMES_LIST, GameInfoForList } from '../games/gameData'; // Import GAMES_LIST and GameInfoForList
import ReusableModal from '../components/ReusableModal';
import PremiumGate from '../components/PremiumGate';
import AppIcon from '../components/AppIcon';

// --- Game Components ---
import ZenTetrisGame from '../games/zen-tetris/GameComponent';
import CardiacCoherenceGame from '../games/cardiac-coherence/GameComponent';
import CardiacCoherenceSetupModal, { CardiacCoherenceSettings } from '../games/cardiac-coherence/CardiacCoherenceSetupModal';

// --- STYLED COMPONENTS ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageTitle = styled.h1`
  font-size: 2.2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
`;

const GameGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const GameCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid ${({ theme }) => theme.border};
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: ${({ theme }) => theme.primary};
  }
  @media (max-width: 768px) {
    &:hover { transform: none; }
  }
`;

const GameCardHeader = styled.div`
  width: 100%;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, ${({ theme }) => theme.primary}1A, ${({ theme }) => theme.secondary}1A);
  border-bottom: 1px solid ${({ theme }) => theme.border};
  position: relative;
`;

const GameIcon = styled.div`
  position: relative;
  z-index: 1;
  font-size: 3rem;
  color: ${({ theme }) => theme.primary};
  background: ${({ theme }) => theme.surface};
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

const CardContent = styled.div`
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  @media (max-width: 768px) { padding: 1rem; }
`;

const CardTitle = styled.h3`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  margin: 0 0 0.75rem 0;
  font-weight: 600;
  @media (max-width: 768px) { font-size: 1.3rem; }
`;

const CardDescription = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.4;
  flex-grow: 1;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  padding: 0 1.5rem 1.5rem;
  position: relative;
`;

const PlayButton = styled.button`
  background: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  &:hover {
    background: ${({ theme }) => theme.secondary};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }
  @media (max-width: 768px) { padding: 0.6rem 1.2rem; font-size: 0.9rem; }
`;

const InfoButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  position: absolute;
  right: 1.5rem;
  &:hover {
    background-color: ${({ theme }) => theme.surfaceAlt || theme.surface};
    transform: scale(1.1);
  }
`;

const GameStats = styled.div`
  display: flex;
  flex-direction: column;
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  gap: 0.2rem;
  margin-top: 0.5rem;
  span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }
`;

// --- Game Data (Removed local GAMES_LIST, now imported) ---

const gameComponentMap: { [key: string]: React.FC<any> } = {
  'zen-tetris': ZenTetrisGame,
  'cardiac-coherence': CardiacCoherenceGame,
};

const GamesPage: React.FC = () => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const { user } = useAuth();
  const location = useLocation(); // Hook to access URL location
  const navigate = useNavigate(); // Hook to navigate programmatically

  const { subscription, gameProgress, setPersonalBestScore, addActivityTime } = useAppStore(useShallow(state => ({
    subscription: state.subscription,
    gameProgress: state.gameProgress,
    setPersonalBestScore: state.setPersonalBestScore,
    addActivityTime: state.addActivityTime,
  })));
  
  const [activeGameId, setActiveGameId] = useState<string | null>(null);
  const [initialGameLoadState, setInitialGameLoadState] = useState<SavedGameState | null>(null);
  const [showSetupModalFor, setShowSetupModalFor] = useState<string | null>(null);
  const [showInfoModalFor, setShowInfoModalFor] = useState<string | null>(null);
  const [activeGameSettings, setActiveGameSettings] = useState<CardiacCoherenceSettings | null>(null);
  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [lastGameResults, setLastGameResults] = useState<{ score: number; level: number; time: number } | null>(null);
  const gameStartTimeRef = useRef<number | null>(null);

  const handlePlayGame = useCallback((gameId: string) => {
    if (gameId === 'cardiac-coherence') {
      setShowSetupModalFor(gameId);
    } else {
      const savedState = user ? (gameProgress?.progress[gameId] || null) : null;
      setInitialGameLoadState(savedState);
      setActiveGameId(gameId);
    }
    gameStartTimeRef.current = Date.now();
  }, [gameProgress, user]);
  
  const handleStartConfiguredGame = useCallback((settings: CardiacCoherenceSettings) => {
    if (showSetupModalFor) {
      setActiveGameId(showSetupModalFor);
      setActiveGameSettings(settings);
      setShowSetupModalFor(null);
    }
    gameStartTimeRef.current = Date.now();
  }, [showSetupModalFor]);

  const handleGameQuit = useCallback(() => {
    setActiveGameId(null);
    setInitialGameLoadState(null);
    setActiveGameSettings(null);

    if (user?.uid && gameStartTimeRef.current !== null) {
      const durationSeconds = (Date.now() - gameStartTimeRef.current) / 1000;
      const durationMinutes = Math.max(0, Math.round(durationSeconds / 60));
      if (durationMinutes > 0) {
        addActivityTime(user.uid, durationMinutes);
      }
      gameStartTimeRef.current = null;
    }
    // Clear the gameId from the URL after quitting the game
    navigate(location.pathname, { replace: true });
  }, [user, addActivityTime, navigate, location.pathname]);

  const handleGameEnd = useCallback((score: number, levelReached: number, finalTimeSeconds: number) => {
    if (activeGameId) {
      setLastGameResults({ score, level: levelReached, time: finalTimeSeconds });
      setShowGameOverModal(true);
      
      if (user?.uid) {
        setPersonalBestScore(user.uid, activeGameId, score, levelReached);
        
        const durationMinutes = Math.max(1, Math.round(finalTimeSeconds / 60));
        addActivityTime(user.uid, durationMinutes);
      }
    }
    setActiveGameId(null);
    gameStartTimeRef.current = null;
    // Clear the gameId from the URL after ending the game
    navigate(location.pathname, { replace: true });
  }, [activeGameId, user, setPersonalBestScore, addActivityTime, navigate, location.pathname]);

  useEffect(() => {
    // Check for gameId in URL on component mount
    const queryParams = new URLSearchParams(location.search);
    const gameIdFromUrl = queryParams.get('gameId');

    if (gameIdFromUrl && !activeGameId) { // Only launch if no game is currently active
      const gameToLaunch = GAMES_LIST.find(game => game.id === gameIdFromUrl);
      if (gameToLaunch) {
        handlePlayGame(gameToLaunch.id);
        // Clear the gameId from the URL to prevent re-launch on refresh/re-visit
        navigate(location.pathname, { replace: true });
      }
    }

    // Cleanup function for when the component unmounts or dependencies change
    return () => {
      if (user?.uid && gameStartTimeRef.current !== null && activeGameId) {
        const durationSeconds = (Date.now() - gameStartTimeRef.current) / 1000;
        const durationMinutes = Math.max(0, Math.round(durationSeconds / 60));
        if (durationMinutes > 0) {
          addActivityTime(user.uid, durationMinutes);
        }
        gameStartTimeRef.current = null;
      }
    };
  }, [location.search, activeGameId, handlePlayGame, user, addActivityTime, navigate, location.pathname]);


  if (activeGameId) {
    const GameComponent = gameComponentMap[activeGameId];
    if (activeGameId === 'cardiac-coherence' && activeGameSettings) {
      return <CardiacCoherenceGame settings={activeGameSettings} onGameQuit={handleGameQuit} />;
    }
    if (GameComponent) {
      return <GameComponent userId={user?.uid || "guest"} onGameEnd={handleGameEnd} onGameQuit={handleGameQuit} onPauseChange={() => {}} initialGameState={initialGameLoadState} />;
    }
  }

  const gameForInfo = GAMES_LIST.find(g => g.id === showInfoModalFor);
  const personalBest = (user && gameForInfo && gameProgress?.personalBest[gameForInfo.id]) 
                       ? gameProgress.personalBest[gameForInfo.id].score 
                       : 0;

  return (
    <PageContainer>
      <PageTitle><AppIcon name="games" /> {t('games.title', 'Mindfulness Games')}</PageTitle>
      <p style={{ textAlign: 'center', color: theme.textMuted, marginBottom: '2rem' }}>{t('games.intro', 'Test and improve your skills with our fun and stimulating mini-games.')}</p>
      
      <GameGrid>
        {GAMES_LIST.map((game) => {
          const savedGame = (user && gameProgress) ? gameProgress.progress[game.id] : null;
          const gamePersonalBest = (user && gameProgress?.personalBest[game.id]) 
                                   ? gameProgress.personalBest[game.id].score 
                                   : 0;
          return (
            <PremiumGate key={game.id} feature="games" subscription={subscription} gameId={game.id} context="game">
              <GameCard>
                <GameCardHeader><GameIcon><AppIcon name={game.icon} size={40} /></GameIcon></GameCardHeader>
                <CardContent>
                  <CardTitle>{t(game.titleKey)}</CardTitle>
                  <CardDescription>{t(game.descriptionKey)}</CardDescription>
                  <GameStats>
                    {game.estimatedDurationMinutes && <span><AppIcon name="clock" /> {t('games.estimatedDuration', 'Estimated duration')}: {game.estimatedDurationMinutes} {t('units.minutes', 'min')}</span>}
                    {user && gamePersonalBest > 0 && <span><AppIcon name="award" /> {t('games.personalBest', 'Personal best')}: {gamePersonalBest} {t('units.points', 'pts')}</span>}
                    {savedGame && game.id !== 'cardiac-coherence' && <span><AppIcon name="star" style={{color: theme.accent}} /> {t('games.savedGameProgress', 'Saved game')}: {savedGame.level}</span>}
                  </GameStats>
                </CardContent>
                <CardFooter>
                  <PlayButton onClick={() => handlePlayGame(game.id)}>
                    <AppIcon name="play" /> {savedGame && game.id !== 'cardiac-coherence' ? t('games.continueGame', 'Continue Game') : t('games.newGame', 'New Game')}
                  </PlayButton>
                  <InfoButton onClick={() => setShowInfoModalFor(game.id)}><AppIcon name="info" /></InfoButton>
                </CardFooter>
              </GameCard>
            </PremiumGate>
          );
        })}
      </GameGrid>

      {showSetupModalFor === 'cardiac-coherence' && (
        <CardiacCoherenceSetupModal isOpen={true} onClose={() => setShowSetupModalFor(null)} onStart={handleStartConfiguredGame} />
      )}

      {gameForInfo && (
        <ReusableModal isOpen={true} onClose={() => setShowInfoModalFor(null)} title={t(gameForInfo.titleKey)} titleIcon={<AppIcon name="help-circle"/>}
          footerContent={<PlayButton onClick={() => { handlePlayGame(gameForInfo.id); setShowInfoModalFor(null); }}><AppIcon name="play" />{(user && gameProgress?.progress[gameForInfo.id]) ? t('games.continueGame') : t('games.newGame')}</PlayButton>}
        >
          <p>{t(gameForInfo.descriptionKey)}</p>
          {gameForInfo.maxLevels > 1 && <p>{t('games.maxLevels', { maxLevels: gameForInfo.maxLevels })}</p>}
          {user && <p>{t('games.yourBestScore', { score: personalBest })}</p>}
          {gameForInfo.tags && gameForInfo.tags.length > 0 && <p>{t('games.keywords', 'Keywords')}: {gameForInfo.tags.join(', ')}</p>}
        </ReusableModal>
      )}

      <ReusableModal isOpen={showGameOverModal} onClose={() => setShowGameOverModal(false)} title={t('game.modal.gameOverTitle', 'Game Over!')} titleIcon={<AppIcon name="award" />}
        footerContent={<PlayButton onClick={() => setShowGameOverModal(false)}>{t('actions.ok', 'OK')}</PlayButton>}
      >
        {lastGameResults && <p>{t('games.gameOverSummary', { score: lastGameResults.score, level: lastGameResults.level, time: lastGameResults.time })}</p>}
      </ReusableModal>
    </PageContainer>
  );
};

export default GamesPage;