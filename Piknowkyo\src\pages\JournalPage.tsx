// src/pages/JournalPage.tsx

import React, { useMemo } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { JournalEntry } from '../models';
import { useAppStore } from '../store/useAppStore';
import AppIcon from '../components/AppIcon';
import CollapsibleSessionNotes from '../components/CollapsibleSessionNotes';

// --- Styled Components ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    svg {
      font-size: 2.2rem;
      opacity: 0.8;
    }
  }
  p {
    font-size: 1.05rem;
    color: ${({ theme }) => theme.textSecondary};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
`;

const NoNotesMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1rem;
  text-align: center;
  color: ${({ theme }) => theme.textSecondary};
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 16px;
  border: 2px dashed ${({ theme }) => theme.border};

  svg {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: ${({ theme }) => theme.textMuted};
    opacity: 0.7;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: ${({ theme }) => theme.text};
  }
  
  span {
    font-size: 0.95rem;
    max-width: 400px;
    line-height: 1.5;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 60vh;
  svg { font-size: 3rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

// --- Component Implementation ---

const JournalPage: React.FC = () => {
  const { t } = useTranslation();
  
  const privateData = useAppStore(state => state.privateData);
  const allScripts = useAppStore(state => state.sessions.scripts);
  const isInitialized = useAppStore(state => state.isInitialized);

  const journalEntries = useMemo(() => {
    if (!privateData) return [];
    return privateData
      .filter(doc => doc?.data?.type === 'journal')
      .map(doc => ({ ...doc.data, id: doc.id } as unknown as JournalEntry))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [privateData]);
  
  const sessionTitleMap = useMemo(() => {
    const map = new Map<string, string>();
    if (!allScripts) return map;
    for (const category of Object.values(allScripts)) {
      for (const script of category) {
        map.set(script.id, script.title);
      }
    }
    return map;
  }, [allScripts]);

  const groupedEntries = useMemo(() => {
    return journalEntries.reduce((acc, entry) => {
      const sessionEntries = acc[entry.sessionId] || [];
      return {
        ...acc,
        [entry.sessionId]: [...sessionEntries, entry]
      };
    }, {} as Record<string, JournalEntry[]>);
  }, [journalEntries]);

  const sortedSessionIds = useMemo(() => 
    Object.keys(groupedEntries).sort((a, b) => {
      const lastNoteA = groupedEntries[a][0];
      const lastNoteB = groupedEntries[b][0];
      return new Date(lastNoteB.date).getTime() - new Date(lastNoteA.date).getTime();
    })
  , [groupedEntries]);

  if (!isInitialized) {
    return <LoadingContainer><AppIcon name="loader" /> {t('loading.journal', 'Loading your journal...')}</LoadingContainer>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><AppIcon name="journal" /> {t('journal.title', 'Tracking Journal')}</h1>
        <p>{t('journal.description', 'Find all your personal notes here, organized by session. Reflect on your experiences and track your progress.')}</p>
      </PageHeader>

      {sortedSessionIds.length === 0 ? (
        <NoNotesMessage>
          <AppIcon name="journal" />
          <p>{t('journal.noNotesYet', 'Your journal is empty.')}</p>
          <span>{t('journal.startSessionPrompt', 'Start a session and take notes to see your reflections here.')}</span>
        </NoNotesMessage>
      ) : (
        <div>
          {sortedSessionIds.map((sessionId) => {
            const sessionTitle = sessionTitleMap.get(sessionId) || t('journal.unknownSession', 'Session (ID: {{id}})', { id: sessionId });
            const entriesForSession = groupedEntries[sessionId];

            return (
              <CollapsibleSessionNotes
                key={sessionId}
                sessionId={sessionId} // Pass the ID for the link
                title={sessionTitle}
                entries={entriesForSession}
              />
            );
          })}
        </div>
      )}
    </PageContainer>
  );
};

export default JournalPage;