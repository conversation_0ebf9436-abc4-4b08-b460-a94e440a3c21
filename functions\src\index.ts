// functions/src/index.ts

import * as functions from "firebase-functions";
import { https } from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { getFirestore } from 'firebase-admin/firestore';
import Stripe from 'stripe';
import CryptoJS from 'crypto-js';
import { TextToSpeechClient } from '@google-cloud/text-to-speech';
import cors from 'cors'; // <-- 1. Importez le package cors

admin.initializeApp();
const db = getFirestore();

// --- Configuration Checks ---
const stripeSecretKey = functions.config().stripe?.secret_key;
const stripeWebhookSecret = functions.config().stripe?.webhook_secret;

if (!stripeSecretKey) { console.error("CRITICAL ERROR: Stripe secret key is not configured."); }
if (!stripeWebhookSecret) { console.error("CRITICAL ERROR: Stripe webhook secret is not configured."); }

const stripe = new Stripe(stripeSecretKey!, { apiVersion: '2025-05-28.basil' as any });
const ttsClient = new TextToSpeechClient(); 
const getUserEncryptionKey = (uid: string): string => uid;

// 2. Configurez les origines autorisées
const allowedOrigins = [
  'https://piknowkyo-777.web.app',
  'https://app.piknowkyo.com',
  'http://localhost:3000', // Assurez-vous que localhost est bien là pour le développement
  'http://localhost:5174', // Pour l'application pmng en développement
  'http://localhost:5173', // Port Vite par défaut
];

// 3. Créez le gestionnaire cors avec les bonnes options
const corsHandler = cors({
    origin: (origin, callback) => {
        // Autorise les requêtes sans origine (ex: Postman, apps mobiles) et celles de notre liste
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    }
});

const getCustomerEmailFromEvent = async (event: Stripe.Event): Promise<string | null> => {
    const eventObject = event.data.object as any;
    if (eventObject.customer_details?.email) return eventObject.customer_details.email;
    if (eventObject.customer_email) return eventObject.customer_email;
    if (eventObject.customer && typeof eventObject.customer === 'string') {
        try {
            const customer = await stripe.customers.retrieve(eventObject.customer);
            if (!customer.deleted && (customer as Stripe.Customer).email) {
                return (customer as Stripe.Customer).email;
            }
        } catch (error) {
            console.error(`Error retrieving Stripe customer ${eventObject.customer}:`, error);
            return null;
        }
    }
    return null;
}

export const stripeWebhook = https.onRequest(async (req, res) => {
    // ... (votre logique Stripe reste inchangée)
    if (!stripeWebhookSecret) {
        console.error('Stripe webhook secret not configured.');
        res.status(500).send('Internal Server Error: Webhook secret not configured.');
        return;
    }

    let event: Stripe.Event;
    try {
        event = stripe.webhooks.constructEvent(req.rawBody, req.headers['stripe-signature'] as string, stripeWebhookSecret!);
    } catch (err: any) {
        console.error(`Webhook signature verification failed.`, err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
    }

    let firebaseUid: string | undefined;
    const userEmail = await getCustomerEmailFromEvent(event);

    if (userEmail) {
        try {
            const userRecord = await admin.auth().getUserByEmail(userEmail);
            firebaseUid = userRecord.uid;
            console.log(`[Webhook] Matched event ${event.id} to user ${firebaseUid} via email ${userEmail}`);
        } catch (error: any) {
            if (error.code === 'auth/user-not-found') {
                console.warn(`[Webhook] Event for email ${userEmail} received, but no matching Firebase user was found.`);
            } else {
                console.error(`[Webhook] Error fetching user by email ${userEmail}:`, error);
            }
        }
    } else {
        console.warn(`[Webhook] Could not extract a customer email from event ${event.id} (type: ${event.type}).`);
    }
    
    if (!firebaseUid) {
        res.status(200).send('Webhook received, but no corresponding Firebase user could be identified. Ignoring event.');
        return;
    }

    const userSubscriptionRef = db.collection('users').doc(firebaseUid).collection('subscription').doc('main');
    try {
        const docSnap = await userSubscriptionRef.get();
        let currentSubscriptionData: any = {};
        if (docSnap.exists && docSnap.data()?.encrypted) {
            try {
                const decryptedBytes = CryptoJS.AES.decrypt(docSnap.data()!.encrypted, getUserEncryptionKey(firebaseUid));
                currentSubscriptionData = JSON.parse(decryptedBytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                console.error(`[Webhook] Error decrypting user ${firebaseUid} subscription data:`, e);
                currentSubscriptionData = {};
            }
        } else {
            console.log(`[Webhook] No existing subscription document for user ${firebaseUid}. Initializing new state.`);
        }

        let updatedSubscriptionState: { [key: string]: any } | null = null;
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object as Stripe.Checkout.Session;
                if (session.mode === 'subscription' && session.subscription) {
                    const customerId = session.customer as string;
                    const newSubscriptionId = session.subscription as string;
                    const existingSubscriptions = await stripe.subscriptions.list({ customer: customerId, status: 'active', limit: 5 });
                    const otherActiveSubscriptions = existingSubscriptions.data.filter(sub => sub.id !== newSubscriptionId);

                    if (otherActiveSubscriptions.length > 0) {
                        console.error(`CRITICAL: Duplicate subscription for customer ${customerId}. New sub ${newSubscriptionId} will be canceled.`);
                        await stripe.subscriptions.cancel(newSubscriptionId);
                    }
                }
                break;
            
            case 'customer.subscription.created':
                const createdSub = event.data.object as Stripe.Subscription;
                const createdPeriodEnd = (createdSub.items?.data[0] as any)?.current_period_end;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: true,
                    tier: 'premium',
                    renewsAt: createdPeriodEnd ? new Date(createdPeriodEnd * 1000).toISOString() : null,
                    willBeCanceled: createdSub.cancel_at_period_end,
                    stripeCustomerId: createdSub.customer as string,
                    stripeSubscriptionId: createdSub.id,
                    updatedAt: new Date().toISOString(),
                    isTrialActive: false, trialStarts: null, trialEnds: null,
                };
                break;

            case 'customer.subscription.updated':
                const updatedSub = event.data.object as Stripe.Subscription;
                const updatedPeriodEnd = (updatedSub.items?.data[0] as any)?.current_period_end;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: updatedSub.status === 'active' || updatedSub.status === 'trialing',
                    tier: (updatedSub.status === 'active' || updatedSub.status === 'trialing') ? 'premium' : 'free',
                    renewsAt: updatedPeriodEnd ? new Date(updatedPeriodEnd * 1000).toISOString() : null,
                    willBeCanceled: updatedSub.cancel_at_period_end,
                    stripeCustomerId: updatedSub.customer as string,
                    stripeSubscriptionId: updatedSub.id,
                    updatedAt: new Date().toISOString(),
                };
                break;

            case 'invoice.payment_succeeded':
                const invoice = event.data.object as Stripe.Invoice;
                if (invoice.subscription) {
                    const subFromInvoice = await stripe.subscriptions.retrieve(invoice.subscription as string);
                    const periodEndFromInvoice = (subFromInvoice.items?.data[0] as any)?.current_period_end;
                    updatedSubscriptionState = {
                        ...currentSubscriptionData,
                        isActive: subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing',
                        tier: (subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing') ? 'premium' : 'free',
                        renewsAt: periodEndFromInvoice ? new Date(periodEndFromInvoice * 1000).toISOString() : null,
                        willBeCanceled: subFromInvoice.cancel_at_period_end,
                        stripeCustomerId: subFromInvoice.customer as string,
                        stripeSubscriptionId: subFromInvoice.id,
                        updatedAt: new Date().toISOString(),
                        isTrialActive: false, trialStarts: null, trialEnds: null,
                    };
                }
                break;

            case 'customer.subscription.deleted':
                const deletedSubscription = event.data.object as Stripe.Subscription;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: false, tier: 'free', renewsAt: null,
                    willBeCanceled: false, 
                    stripeSubscriptionId: deletedSubscription.id,
                    updatedAt: new Date().toISOString(),
                };
                break;

            case 'invoice.payment_failed':
                 const failedInvoice = event.data.object as Stripe.Invoice;
                 if (failedInvoice.subscription) {
                    const latestSubscription = await stripe.subscriptions.retrieve(failedInvoice.subscription as string);
                    const stillHasAccess = ['active', 'past_due'].includes(latestSubscription.status);
                    updatedSubscriptionState = { ...currentSubscriptionData, isActive: stillHasAccess, tier: stillHasAccess ? 'premium' : 'free', updatedAt: new Date().toISOString() };
                }
                break;

            default:
                console.log(`[Webhook] Unhandled event type ${event.type} for Firebase UID: ${firebaseUid}`);
                res.status(200).send(`Event type ${event.type} not handled.`);
                return;
        }

        if (updatedSubscriptionState) {
            const encrypted = CryptoJS.AES.encrypt(JSON.stringify(updatedSubscriptionState), getUserEncryptionKey(firebaseUid)).toString();
            await userSubscriptionRef.set({ encrypted });
            console.log(`[Webhook] Subscription for user ${firebaseUid} updated by event: ${event.type}.`);
            res.status(200).send('Webhook received and processed successfully.');
        } else {
            res.status(200).send(`Webhook event ${event.type} received, no state change required.`);
        }
    } catch (error: any) {
        console.error(`[Webhook] Error processing webhook event ${event.id} for Firebase UID ${firebaseUid}:`, error);
        res.status(500).send(`Webhook Error: ${error.message}`);
    }
});


// 4. Enveloppez la logique de votre fonction avec le gestionnaire CORS
export const getGoogleTTSAudio = https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        let idToken;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
            idToken = req.headers.authorization.split('Bearer ')[1];
        }

        if (!idToken) {
            res.status(401).send('Unauthorized: No authentication token provided.');
            return;
        }

        try {
            await admin.auth().verifyIdToken(idToken);
            const { text, lang, voiceName } = req.body;
            if (!text || !lang || !voiceName) {
                res.status(400).send('Bad Request: Text, language, and voice name are required.');
                return;
            }
            const request = {
                input: { text: text },
                voice: { languageCode: lang, name: voiceName },
                audioConfig: { audioEncoding: 'MP3' as const },
            };
            const [response] = await ttsClient.synthesizeSpeech(request);
            if (response.audioContent) {
                res.set('Content-Type', 'audio/mpeg');
                res.status(200).send(response.audioContent);
            } else {
                console.error('TTS synthesis failed to produce audio for request:', req.body);
                res.status(500).send('Internal Server Error: TTS synthesis failed.');
            }
        } catch (error: any) {
            console.error('Error in getGoogleTTSAudio:', error);
            if (error.code && error.code.startsWith('auth/')) {
                res.status(401).send(`Authentication Error: ${error.message}`);
            } else if (error.details) {
                res.status(500).send(`TTS API Error: ${error.details}`);
            } else {
                res.status(500).send(`Internal Server Error: ${error.message}`);
            }
        }
    });
});

// --- Admin Function: List All Users ---
export const listAllUsers = https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            // Verify admin access (you can add more sophisticated auth here)
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).send('Unauthorized: Missing or invalid authorization header');
                return;
            }

            const idToken = authHeader.split('Bearer ')[1];

            try {
                // Verify the ID token
                const decodedToken = await admin.auth().verifyIdToken(idToken);

                // Check if user is admin (you can customize this logic)
                const adminEmail = '<EMAIL>'; // Replace with your admin email
                if (decodedToken.email !== adminEmail) {
                    res.status(403).send('Forbidden: Admin access required');
                    return;
                }
            } catch (authError) {
                console.error('Token verification failed:', authError);
                res.status(401).send('Unauthorized: Invalid token');
                return;
            }

            const allUsers: any[] = [];
            let nextPageToken: string | undefined;

            // Fetch all users from Firebase Auth
            do {
                const listUsersResult = await admin.auth().listUsers(1000, nextPageToken);

                for (const userRecord of listUsersResult.users) {
                    const userId = userRecord.uid;

                    // Get Firestore data for this user
                    let firestoreData: any = null;
                    let hasFirestoreData = false;

                    try {
                        // Check if user has a document in the main users collection
                        const userDocRef = db.collection('users').doc(userId);
                        const userDocSnap = await userDocRef.get();

                        if (userDocSnap.exists) {
                            hasFirestoreData = true;
                            firestoreData = {
                                mainDocument: userDocSnap.data(),
                                hasSubscription: false,
                                hasProfile: false,
                                hasActivity: false,
                                hasPreferences: false
                            };

                            // Check subcollections
                            const subcollections = ['subscription', 'profile', 'activity', 'preferences'];
                            for (const subcol of subcollections) {
                                try {
                                    const subDocRef = userDocRef.collection(subcol).doc('main');
                                    const subDocSnap = await subDocRef.get();
                                    if (subDocSnap.exists) {
                                        firestoreData[`has${subcol.charAt(0).toUpperCase() + subcol.slice(1)}`] = true;
                                    }
                                } catch (subError) {
                                    console.warn(`Error checking ${subcol} for user ${userId}:`, subError);
                                }
                            }
                        }
                    } catch (firestoreError) {
                        console.warn(`Error fetching Firestore data for user ${userId}:`, firestoreError);
                    }

                    allUsers.push({
                        uid: userRecord.uid,
                        email: userRecord.email,
                        displayName: userRecord.displayName,
                        photoURL: userRecord.photoURL,
                        emailVerified: userRecord.emailVerified,
                        disabled: userRecord.disabled,
                        creationTime: userRecord.metadata.creationTime,
                        lastSignInTime: userRecord.metadata.lastSignInTime,
                        providerData: userRecord.providerData.map(provider => ({
                            providerId: provider.providerId,
                            uid: provider.uid,
                            email: provider.email,
                            displayName: provider.displayName
                        })),
                        hasFirestoreData,
                        firestoreData
                    });
                }

                nextPageToken = listUsersResult.pageToken;
            } while (nextPageToken);

            console.log(`Retrieved ${allUsers.length} users from Firebase Auth`);

            res.status(200).json({
                success: true,
                totalUsers: allUsers.length,
                users: allUsers
            });

        } catch (error: any) {
            console.error('Error in listAllUsers:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
});