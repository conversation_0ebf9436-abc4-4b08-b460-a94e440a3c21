// functions/src/index.ts

import * as functions from "firebase-functions";
import { https } from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { getFirestore } from 'firebase-admin/firestore';
import Stripe from 'stripe';
import CryptoJS from 'crypto-js';
import { TextToSpeechClient } from '@google-cloud/text-to-speech';
import cors from 'cors'; // <-- 1. Importez le package cors

admin.initializeApp();
const db = getFirestore();

// --- Configuration Checks ---
const stripeSecretKey = functions.config().stripe?.secret_key;
const stripeWebhookSecret = functions.config().stripe?.webhook_secret;

if (!stripeSecretKey) { console.error("CRITICAL ERROR: Stripe secret key is not configured."); }
if (!stripeWebhookSecret) { console.error("CRITICAL ERROR: Stripe webhook secret is not configured."); }

const stripe = new Stripe(stripeSecretKey!, { apiVersion: '2025-05-28.basil' as any });
const ttsClient = new TextToSpeechClient(); 
const getUserEncryptionKey = (uid: string): string => uid;

// 2. Configurez les origines autorisées
const allowedOrigins = [
  'https://piknowkyo-777.web.app',
  'https://app.piknowkyo.com',
  'http://localhost:3000', // Assurez-vous que localhost est bien là pour le développement
];

// 3. Créez le gestionnaire cors avec les bonnes options
const corsHandler = cors({
    origin: (origin, callback) => {
        // Autorise les requêtes sans origine (ex: Postman, apps mobiles) et celles de notre liste
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    }
});

const getCustomerEmailFromEvent = async (event: Stripe.Event): Promise<string | null> => {
    const eventObject = event.data.object as any;
    if (eventObject.customer_details?.email) return eventObject.customer_details.email;
    if (eventObject.customer_email) return eventObject.customer_email;
    if (eventObject.customer && typeof eventObject.customer === 'string') {
        try {
            const customer = await stripe.customers.retrieve(eventObject.customer);
            if (!customer.deleted && (customer as Stripe.Customer).email) {
                return (customer as Stripe.Customer).email;
            }
        } catch (error) {
            console.error(`Error retrieving Stripe customer ${eventObject.customer}:`, error);
            return null;
        }
    }
    return null;
}

export const stripeWebhook = https.onRequest(async (req, res) => {
    // ... (votre logique Stripe reste inchangée)
    if (!stripeWebhookSecret) {
        console.error('Stripe webhook secret not configured.');
        res.status(500).send('Internal Server Error: Webhook secret not configured.');
        return;
    }

    let event: Stripe.Event;
    try {
        event = stripe.webhooks.constructEvent(req.rawBody, req.headers['stripe-signature'] as string, stripeWebhookSecret!);
    } catch (err: any) {
        console.error(`Webhook signature verification failed.`, err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
    }

    let firebaseUid: string | undefined;
    const userEmail = await getCustomerEmailFromEvent(event);

    if (userEmail) {
        try {
            const userRecord = await admin.auth().getUserByEmail(userEmail);
            firebaseUid = userRecord.uid;
            console.log(`[Webhook] Matched event ${event.id} to user ${firebaseUid} via email ${userEmail}`);
        } catch (error: any) {
            if (error.code === 'auth/user-not-found') {
                console.warn(`[Webhook] Event for email ${userEmail} received, but no matching Firebase user was found.`);
            } else {
                console.error(`[Webhook] Error fetching user by email ${userEmail}:`, error);
            }
        }
    } else {
        console.warn(`[Webhook] Could not extract a customer email from event ${event.id} (type: ${event.type}).`);
    }
    
    if (!firebaseUid) {
        res.status(200).send('Webhook received, but no corresponding Firebase user could be identified. Ignoring event.');
        return;
    }

    const userSubscriptionRef = db.collection('users').doc(firebaseUid).collection('subscription').doc('main');
    try {
        const docSnap = await userSubscriptionRef.get();
        let currentSubscriptionData: any = {};
        if (docSnap.exists && docSnap.data()?.encrypted) {
            try {
                const decryptedBytes = CryptoJS.AES.decrypt(docSnap.data()!.encrypted, getUserEncryptionKey(firebaseUid));
                currentSubscriptionData = JSON.parse(decryptedBytes.toString(CryptoJS.enc.Utf8));
            } catch (e) {
                console.error(`[Webhook] Error decrypting user ${firebaseUid} subscription data:`, e);
                currentSubscriptionData = {};
            }
        } else {
            console.log(`[Webhook] No existing subscription document for user ${firebaseUid}. Initializing new state.`);
        }

        let updatedSubscriptionState: { [key: string]: any } | null = null;
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object as Stripe.Checkout.Session;
                if (session.mode === 'subscription' && session.subscription) {
                    const customerId = session.customer as string;
                    const newSubscriptionId = session.subscription as string;
                    const existingSubscriptions = await stripe.subscriptions.list({ customer: customerId, status: 'active', limit: 5 });
                    const otherActiveSubscriptions = existingSubscriptions.data.filter(sub => sub.id !== newSubscriptionId);

                    if (otherActiveSubscriptions.length > 0) {
                        console.error(`CRITICAL: Duplicate subscription for customer ${customerId}. New sub ${newSubscriptionId} will be canceled.`);
                        await stripe.subscriptions.cancel(newSubscriptionId);
                    }
                }
                break;
            
            case 'customer.subscription.created':
                const createdSub = event.data.object as Stripe.Subscription;
                const createdPeriodEnd = (createdSub.items?.data[0] as any)?.current_period_end;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: true,
                    tier: 'premium',
                    renewsAt: createdPeriodEnd ? new Date(createdPeriodEnd * 1000).toISOString() : null,
                    willBeCanceled: createdSub.cancel_at_period_end,
                    stripeCustomerId: createdSub.customer as string,
                    stripeSubscriptionId: createdSub.id,
                    updatedAt: new Date().toISOString(),
                    isTrialActive: false, trialStarts: null, trialEnds: null,
                };
                break;

            case 'customer.subscription.updated':
                const updatedSub = event.data.object as Stripe.Subscription;
                const updatedPeriodEnd = (updatedSub.items?.data[0] as any)?.current_period_end;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: updatedSub.status === 'active' || updatedSub.status === 'trialing',
                    tier: (updatedSub.status === 'active' || updatedSub.status === 'trialing') ? 'premium' : 'free',
                    renewsAt: updatedPeriodEnd ? new Date(updatedPeriodEnd * 1000).toISOString() : null,
                    willBeCanceled: updatedSub.cancel_at_period_end,
                    stripeCustomerId: updatedSub.customer as string,
                    stripeSubscriptionId: updatedSub.id,
                    updatedAt: new Date().toISOString(),
                };
                break;

            case 'invoice.payment_succeeded':
                const invoice = event.data.object as Stripe.Invoice;
                if (invoice.subscription) {
                    const subFromInvoice = await stripe.subscriptions.retrieve(invoice.subscription as string);
                    const periodEndFromInvoice = (subFromInvoice.items?.data[0] as any)?.current_period_end;
                    updatedSubscriptionState = {
                        ...currentSubscriptionData,
                        isActive: subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing',
                        tier: (subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing') ? 'premium' : 'free',
                        renewsAt: periodEndFromInvoice ? new Date(periodEndFromInvoice * 1000).toISOString() : null,
                        willBeCanceled: subFromInvoice.cancel_at_period_end,
                        stripeCustomerId: subFromInvoice.customer as string,
                        stripeSubscriptionId: subFromInvoice.id,
                        updatedAt: new Date().toISOString(),
                        isTrialActive: false, trialStarts: null, trialEnds: null,
                    };
                }
                break;

            case 'customer.subscription.deleted':
                const deletedSubscription = event.data.object as Stripe.Subscription;
                updatedSubscriptionState = {
                    ...currentSubscriptionData,
                    isActive: false, tier: 'free', renewsAt: null,
                    willBeCanceled: false, 
                    stripeSubscriptionId: deletedSubscription.id,
                    updatedAt: new Date().toISOString(),
                };
                break;

            case 'invoice.payment_failed':
                 const failedInvoice = event.data.object as Stripe.Invoice;
                 if (failedInvoice.subscription) {
                    const latestSubscription = await stripe.subscriptions.retrieve(failedInvoice.subscription as string);
                    const stillHasAccess = ['active', 'past_due'].includes(latestSubscription.status);
                    updatedSubscriptionState = { ...currentSubscriptionData, isActive: stillHasAccess, tier: stillHasAccess ? 'premium' : 'free', updatedAt: new Date().toISOString() };
                }
                break;

            default:
                console.log(`[Webhook] Unhandled event type ${event.type} for Firebase UID: ${firebaseUid}`);
                res.status(200).send(`Event type ${event.type} not handled.`);
                return;
        }

        if (updatedSubscriptionState) {
            const encrypted = CryptoJS.AES.encrypt(JSON.stringify(updatedSubscriptionState), getUserEncryptionKey(firebaseUid)).toString();
            await userSubscriptionRef.set({ encrypted });
            console.log(`[Webhook] Subscription for user ${firebaseUid} updated by event: ${event.type}.`);
            res.status(200).send('Webhook received and processed successfully.');
        } else {
            res.status(200).send(`Webhook event ${event.type} received, no state change required.`);
        }
    } catch (error: any) {
        console.error(`[Webhook] Error processing webhook event ${event.id} for Firebase UID ${firebaseUid}:`, error);
        res.status(500).send(`Webhook Error: ${error.message}`);
    }
});


// 4. Enveloppez la logique de votre fonction avec le gestionnaire CORS
export const getGoogleTTSAudio = https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        let idToken;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
            idToken = req.headers.authorization.split('Bearer ')[1];
        }

        if (!idToken) {
            res.status(401).send('Unauthorized: No authentication token provided.');
            return;
        }

        try {
            await admin.auth().verifyIdToken(idToken);
            const { text, lang, voiceName } = req.body;
            if (!text || !lang || !voiceName) {
                res.status(400).send('Bad Request: Text, language, and voice name are required.');
                return;
            }
            const request = {
                input: { text: text },
                voice: { languageCode: lang, name: voiceName },
                audioConfig: { audioEncoding: 'MP3' as const },
            };
            const [response] = await ttsClient.synthesizeSpeech(request);
            if (response.audioContent) {
                res.set('Content-Type', 'audio/mpeg');
                res.status(200).send(response.audioContent);
            } else {
                console.error('TTS synthesis failed to produce audio for request:', req.body);
                res.status(500).send('Internal Server Error: TTS synthesis failed.');
            }
        } catch (error: any) {
            console.error('Error in getGoogleTTSAudio:', error);
            if (error.code && error.code.startsWith('auth/')) {
                res.status(401).send(`Authentication Error: ${error.message}`);
            } else if (error.details) {
                res.status(500).send(`TTS API Error: ${error.details}`);
            } else {
                res.status(500).send(`Internal Server Error: ${error.message}`);
            }
        }
    });
});