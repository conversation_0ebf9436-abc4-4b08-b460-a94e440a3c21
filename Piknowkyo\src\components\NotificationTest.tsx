import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { sendNotification } from '../services/usePushNotifications';
import { Capacitor } from '@capacitor/core';

const Button = styled.button`
  background-color: #4CAF50;
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
`;

const Card = styled.div`
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const NotificationTest: React.FC = () => {
  const { t } = useTranslation();
  const isWeb = Capacitor.getPlatform() === 'web';

  const handleTestNotification = () => {
    sendNotification({
      title: t('notificationTest.title'),
      body: t('notificationTest.body'),
      icon: '/logo192.png'
    });
  };

  return (
    <Card>
      <h3>{t('notificationTest.heading')}</h3>
      <p>
        {t('notificationTest.platform')} <strong>{Capacitor.getPlatform()}</strong>
      </p>
      {isWeb && (
        <p>
          {t('notificationTest.status')} <strong>{Notification.permission}</strong>
        </p>
      )}
      <Button onClick={handleTestNotification}>
        {t('notificationTest.sendButton')}
      </Button>
    </Card>
  );
};

export default NotificationTest;
