// src/pages/TranslationManagementPage.tsx (or wherever the component is located)

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { useTranslation } from '../hooks/useTranslation';
import type { Translation } from '../hooks/useTranslation';
import { FiCpu } from 'react-icons/fi';

// --- Styled Components for this page ---
const InfoBox = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background-color: ${({ theme }) => theme.surface};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 6px;
  margin-top: -10px;
  margin-bottom: 20px;
  font-size: 0.9rem;
`;

const ProviderBadge = styled.span`
  background-color: ${({ theme }) => theme.primary}20;
  color: ${({ theme }) => theme.primary};
  padding: 3px 8px;
  border-radius: 4px;
  font-weight: 500;
`;

// Helper function to safely render a cell's content
const renderCellContent = (content: any): string => {
  if (typeof content === 'string') return content;
  if (content === null || content === undefined) return '';
  return JSON.stringify(content);
};

// Helper function to flatten nested objects
const flattenObject = (obj: any, prefix = ''): { [key: string]: string } => {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? prefix + '.' : '';
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k], pre + k));
    } else {
      acc[pre + k] = obj[k];
    }
    return acc;
  }, {} as { [key: string]: string });
};


const TranslationManagementPage: React.FC = () => {
  const { translations, loading, error, addTranslation, updateTranslation, deleteTranslation } = useTranslation();
  
  // --- States ---
  const [newKey, setNewKey] = useState('');
  const [newValues, setNewValues] = useState({ en: '', fr: '', es: '' });
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editValues, setEditValues] = useState({ en: '', fr: '', es: '' });
  const [jsonInput, setJsonInput] = useState('');
  const [preparedTranslations, setPreparedTranslations] = useState<Translation[]>([]);
  
  // --- NEW: AI related states ---
  const [aiSettings, setAiSettings] = useState<any>(null);
  const [isTranslating, setIsTranslating] = useState(false);

  // --- NEW: Fetch AI settings on component mount ---
  useEffect(() => {
    const loadAISettings = async () => {
      const docRef = doc(db, 'config', 'ai_settings');
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const settings = docSnap.data();
        // Set API key from environment variable if available
        if (settings.groq && import.meta.env.VITE_GROQ_API_KEY) {
            settings.groq.apiKey = import.meta.env.VITE_GROQ_API_KEY;
        }
        setAiSettings(settings);
      } else {
        console.warn("AI settings not found in Firestore.");
      }
    };
    loadAISettings();
  }, []);

  // --- NEW: AI Translation Function ---
  const translateText = async (text: string, targetLang: string): Promise<string> => {
    if (!aiSettings?.groq?.apiKey || !aiSettings?.groq?.selectedModel) {
      throw new Error("Groq AI provider is not configured. Please set it up in AI Settings.");
    }
    
    const API_KEY = aiSettings.groq.apiKey;
    const MODEL = aiSettings.groq.selectedModel;
    const API_BASE = 'https://api.groq.com/openai/v1';

    const systemPrompt = `You are an expert translator. Translate the user's text to ${targetLang}. Respond ONLY with the translated text, without any additional comments, introductions, or quotation marks.`;
    
    try {
      const response = await fetch(`${API_BASE}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: MODEL,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: text }
          ],
          temperature: 0.1,
        })
      });

      const data = await response.json();
      if (!response.ok || data.error) {
        throw new Error(data.error?.message || 'Unknown API error');
      }
      return data.choices[0]?.message?.content.trim() || '';
    } catch (e) {
      console.error(`Translation to ${targetLang} failed:`, e);
      throw e; // Re-throw to be caught by the caller
    }
  };

  const handleParseAndTranslate = async () => {
    let processedInput = jsonInput.trim();
    if (!processedInput) return;
    if (!processedInput.startsWith('{') && processedInput.includes(':')) {
      processedInput = `{${processedInput}}`;
    }

    setIsTranslating(true);
    setPreparedTranslations([]); // Clear previous results

    try {
      const parsedJson = JSON.parse(processedInput);
      const flattened = flattenObject(parsedJson);

      const toTranslate = Object.entries(flattened).map(([key, value]) => ({
        key,
        value: String(value)
      }));

      const translatedItems = await Promise.all(
        toTranslate.map(async item => {
          const [french, spanish] = await Promise.all([
            translateText(item.value, 'French'),
            translateText(item.value, 'Spanish')
          ]);
          return {
            key: item.key,
            values: { en: item.value, fr: french, es: spanish }
          };
        })
      );

      setPreparedTranslations(translatedItems);

    } catch (e: any) {
      alert(`An error occurred: ${e.message}`);
      console.error("Parsing or Translation Error:", e);
    } finally {
      setIsTranslating(false);
    }
  };
  
  const handleAddAllPrepared = async () => {
     if (preparedTranslations.length === 0) return;
     await Promise.all(preparedTranslations.map(t => addTranslation(t)));
     setPreparedTranslations([]);
     setJsonInput('');
     alert(`${preparedTranslations.length} keys have been added successfully!`);
  };

  // --- Other handlers (startEditing, etc.) remain unchanged ---

  if (loading) return <div>Loading translations...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div style={{ padding: '20px' }}>
      <h1>Translation Management</h1>
      
      {aiSettings?.groq?.selectedModel && (
        <InfoBox>
            <FiCpu size={20} color="#007bff" />
            <div>
              Using AI model <ProviderBadge>{aiSettings.groq.selectedModel}</ProviderBadge> for translations.
            </div>
        </InfoBox>
      )}

      <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #007bff', borderRadius: '8px', background: '#f0f7ff' }}>
        <h2 style={{ marginTop: 0 }}>Bulk Add from JSON</h2>
        <p>Paste a JSON object below. The keys will be flattened, translated using the configured AI model, and prepared for addition.</p>
        <textarea
          value={jsonInput}
          onChange={(e) => setJsonInput(e.target.value)}
          placeholder='{ "myKey": "myValue", "nested": { "child": "..." } }'
          style={{ width: '100%', minHeight: '150px', padding: '10px', margin: '5px 0', fontFamily: 'monospace' }}
        />
        <button onClick={handleParseAndTranslate} disabled={isTranslating} style={{ padding: '10px 15px', background: '#007bff', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
          {isTranslating ? 'Parsing and Translating...' : 'Parse, Translate & Prepare'}
        </button>

        {preparedTranslations.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <h3>Prepared Translations ({preparedTranslations.length} keys)</h3>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <table style={{ width: '100%', fontSize: '0.9em' }}>
                <thead><tr><th>Key</th><th>English</th><th>French (AI)</th><th>Spanish (AI)</th></tr></thead>
                <tbody>
                  {preparedTranslations.map(t => (
                    <tr key={t.key}>
                      <td style={{padding: '5px', border: '1px solid #ddd'}}><strong>{t.key}</strong></td>
                      <td style={{padding: '5px', border: '1px solid #ddd'}}>{t.values.en}</td>
                      <td style={{padding: '5px', border: '1px solid #ddd'}}>{t.values.fr}</td>
                      <td style={{padding: '5px', border: '1px solid #ddd'}}>{t.values.es}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <button onClick={handleAddAllPrepared} style={{ marginTop: '10px', padding: '10px 15px', background: '#28a745', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
              Add All Prepared Translations
            </button>
          </div>
        )}
      </div>
      
      {/* ... The rest of the component for single add and the main table ... */}
    </div>
  );
};

export default TranslationManagementPage;