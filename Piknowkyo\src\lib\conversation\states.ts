import { StateNode, ConversationState, TFunction, UserProfile, ExplorationDomain, Dictionary, Language } from './types';

// --- Constants for special answers ---
const IDK_KEY = 'idk';
const YES_KEY = 'yes';
const NO_KEY = 'no';

// --- Helper function for conversational variety ---
function getRandomPhrase(t: TFunction, key: string, options?: any): string {
  const phrases = t(key, { returnObjects: true, ...options }) as string[] | string;
  if (Array.isArray(phrases) && phrases.length > 0) {
    return phrases[Math.floor(Math.random() * phrases.length)];
  }
  return Array.isArray(phrases) ? phrases.join('') : phrases;
}

const explorationPriority: ExplorationDomain[] = [ 'somatic', 'cognitive', 'behavioral', 'contextual', 'metaphorical', 'energetic' ];

// --- The COMPLETE State Machine Definition ---
export const conversationStates: Record<string, StateNode> = {

  // =================================================================
  // == STARTING POINT & ORCHESTRATOR
  // =================================================================
  start: {
    type: 'exploratory_question',
    domain: 'initial',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_initial'),
    getOptions: (d, p, l, t) => [
      ...d.basic_emotions.map(e => ({ ...e, label: e.name[l] })),
      { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey && answerKey !== IDK_KEY) {
        updatedProfile.emotions.push(answerKey);
      }
      return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: state.completedDomains };
    },
  },

  determine_next_exploration_path: {
    type: 'proposing_exploration',
    domain: 'initial',
    getText: () => '', // Logic-only state
    determineNextState: (answerKey, state) => {
      const nextDomainToExplore = explorationPriority.find(domain => !state.completedDomains.includes(domain));
      if (nextDomainToExplore) {
        return { 
          nextStateId: `propose_${nextDomainToExplore}_exploration`, 
          updatedProfile: state.userProfile, 
          updatedCompletedDomains: state.completedDomains 
        };
      }
      return { 
        nextStateId: 'ask_outcome', 
        updatedProfile: state.userProfile, 
        updatedCompletedDomains: state.completedDomains 
      };
    }
  },

  // =================================================================
  // == DOMAIN: SOMATIC
  // =================================================================
  propose_somatic_exploration: {
    type: 'proposing_exploration',
    domain: 'somatic',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_somatic'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_somatic_location', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_somatic_location: {
    type: 'exploratory_question',
    domain: 'somatic',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_somatic_location'),
    getOptions: (d, p, l, t) => [
      ...d.somatic_sensations.filter(s => ['chest_oppression', 'knotted_stomach', 'shoulder_neck_tension', 'heaviness_limbs', 'racing_heart'].includes(s.key)).map(e => ({ ...e, label: e.name[l] })),
      { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey === IDK_KEY || !answerKey) {
        const updatedDomains: ExplorationDomain[] = [...state.completedDomains, 'somatic'];
        return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: updatedDomains };
      }
      updatedProfile.somatic_markers.push(answerKey);
      return { nextStateId: 'somatic_verification', updatedProfile, updatedCompletedDomains: state.completedDomains };
    }
  },
  somatic_verification: {
    type: 'verification_check',
    domain: 'somatic',
    getText: (t, p, d, l) => {
      const lastSensationKey = p.somatic_markers[p.somatic_markers.length - 1];
      const sensation = d.somatic_sensations.find(s => s.key === lastSensationKey);
      const sensationName = sensation ? sensation.name[l] : lastSensationKey;
      return getRandomPhrase(t, 'recommendationAssistant.q_verification', { context: sensationName });
    },
    getOptions: (d, p, l, t) => [
      { key: YES_KEY, name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_yes') },
      { key: NO_KEY, name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_no') },
    ],
    determineNextState: (answerKey, state) => {
      const updatedDomains: ExplorationDomain[] = answerKey === YES_KEY ? [...state.completedDomains, 'somatic'] : state.completedDomains;
      if(answerKey === NO_KEY) state.userProfile.somatic_markers.pop();
      return { nextStateId: 'determine_next_exploration_path', updatedProfile: state.userProfile, updatedCompletedDomains: updatedDomains };
    }
  },

  // =================================================================
  // == DOMAIN: COGNITIVE
  // =================================================================
  propose_cognitive_exploration: {
    type: 'proposing_exploration',
    domain: 'cognitive',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_cognitive'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_cognitive_patterns', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_cognitive_patterns: {
    type: 'exploratory_question',
    domain: 'cognitive',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_cognitive_pattern'),
    getOptions: (d, p, l, t) => [
        ...d.cognitive_patterns.filter(s => ['mental_rumination', 'future_anxiety', 'critical_inner_dialogue', 'mental_fog', 'comparison'].includes(s.key)).map(e => ({ ...e, label: e.name[l] })),
        { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
        let updatedProfile = state.userProfile;
        if (answerKey === IDK_KEY || !answerKey) {
            const updatedDomains: ExplorationDomain[] = [...state.completedDomains, 'cognitive'];
            return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: updatedDomains };
        }
        updatedProfile.cognitive_patterns.push(answerKey);
        return { nextStateId: 'cognitive_verification', updatedProfile, updatedCompletedDomains: state.completedDomains };
    }
  },
  cognitive_verification: {
    type: 'verification_check',
    domain: 'cognitive',
    getText: (t, p, d, l) => {
      const lastPatternKey = p.cognitive_patterns[p.cognitive_patterns.length - 1];
      const pattern = d.cognitive_patterns.find(pat => pat.key === lastPatternKey);
      const patternName = pattern ? pattern.name[l] : lastPatternKey;
      return getRandomPhrase(t, 'recommendationAssistant.q_verification', { context: patternName });
    },
    getOptions: (d, p, l, t) => [
      { key: YES_KEY, name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_yes') },
      { key: NO_KEY, name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_no') },
    ],
    determineNextState: (answerKey, state) => {
      const updatedDomains: ExplorationDomain[] = answerKey === YES_KEY ? [...state.completedDomains, 'cognitive'] : state.completedDomains;
      if(answerKey === NO_KEY) state.userProfile.cognitive_patterns.pop();
      return { nextStateId: 'determine_next_exploration_path', updatedProfile: state.userProfile, updatedCompletedDomains: updatedDomains };
    }
  },

  // =================================================================
  // == DOMAIN: BEHAVIORAL
  // =================================================================
  propose_behavioral_exploration: {
    type: 'proposing_exploration',
    domain: 'behavioral',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_behavioral'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_behavioral_patterns', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_behavioral_patterns: {
    type: 'exploratory_question',
    domain: 'behavioral',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_behavioral_pattern'),
    getOptions: (d, p, l, t) => [
      { key: 'procrastination', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_behavior_procrastination') },
      { key: 'isolation', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_behavior_isolation') },
      { key: 'irritability', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_behavior_irritability') },
      { key: 'avoidance', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_behavior_avoidance') },
      { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      const updatedDomains: ExplorationDomain[] = [...state.completedDomains, 'behavioral'];
      if (answerKey === IDK_KEY || !answerKey) return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: updatedDomains };
      if(answerKey) updatedProfile.behavioral_patterns.push(answerKey);
      if(answerKey === 'procrastination' || answerKey === 'avoidance') updatedProfile.cognitive_patterns.push('future_anxiety');
      if(answerKey === 'isolation') updatedProfile.emotions.push('sadness');
      if(answerKey === 'irritability') updatedProfile.emotions.push('anger');
      return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: updatedDomains };
    }
  },
  
  // =================================================================
  // == DOMAIN: CONTEXTUAL
  // =================================================================
  propose_contextual_exploration: {
    type: 'proposing_exploration',
    domain: 'contextual',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_contextual'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_contextual_triggers', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_contextual_triggers: {
    type: 'exploratory_question',
    domain: 'contextual',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_contextual_trigger'),
    getOptions: (d, p, l, t) => [
      { key: 'work', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_context_work') },
      { key: 'relationships', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_context_relationships') },
      { key: 'home', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_context_home') },
      { key: 'internal', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_context_internal') },
      { key: IDK_KEY, name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey && answerKey !== IDK_KEY) updatedProfile.contextual_triggers.push(answerKey);
      return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: [...state.completedDomains, 'contextual'] };
    }
  },

  // =================================================================
  // == DOMAIN: METAPHORICAL
  // =================================================================
  propose_metaphorical_exploration: {
    type: 'proposing_exploration',
    domain: 'metaphorical',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_metaphorical'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_metaphorical_image', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_metaphorical_image: {
    type: 'exploratory_question',
    domain: 'metaphorical',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_metaphorical_image'),
    getOptions: (d, p, l, t) => [
      { key: 'storm', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_metaphor_storm') },
      { key: 'fog', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_metaphor_fog') },
      { key: 'weight', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_metaphor_weight') },
      { key: 'knot', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_metaphor_knot') },
      { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey && answerKey !== IDK_KEY) {
        updatedProfile.metaphorical_images.push(answerKey);
        if (answerKey === 'storm' || answerKey === 'knot') updatedProfile.cognitive_patterns.push('mental_rumination');
        if (answerKey === 'fog') updatedProfile.cognitive_patterns.push('mental_fog');
        if (answerKey === 'weight') updatedProfile.somatic_markers.push('heaviness_limbs');
      }
      return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: [...state.completedDomains, 'metaphorical'] };
    }
  },

  // =================================================================
  // == DOMAIN: ENERGETIC
  // =================================================================
  propose_energetic_exploration: {
    type: 'proposing_exploration',
    domain: 'energetic',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.affirm_propose_energetic'),
    determineNextState: (answerKey, state) => ({ nextStateId: 'explore_energetic_sensation', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
  explore_energetic_sensation: {
    type: 'exploratory_question',
    domain: 'energetic',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_energetic_sensation'),
    getOptions: (d, p, l, t) => [
      { key: 'stagnation', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_energy_stuck') },
      { key: 'overactivation', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_energy_overactive') },
      { key: 'depletion', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_energy_drained') },
      { key: 'scattered', name: {en:'',fr:'',es:''}, description: {en:'',fr:'',es:''}, label: t('recommendationAssistant.opt_energy_scattered') },
      { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_idk_or_other') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey && answerKey !== IDK_KEY) updatedProfile.energetic_states.push(answerKey);
      return { nextStateId: 'determine_next_exploration_path', updatedProfile, updatedCompletedDomains: [...state.completedDomains, 'energetic'] };
    }
  },
  
  // =================================================================
  // == FINAL OUTCOME QUESTION
  // =================================================================
  ask_outcome: {
    type: 'exploratory_question',
    domain: 'outcome',
    getText: (t) => getRandomPhrase(t, 'recommendationAssistant.q_outcome'),
    getOptions: (d, p, l, t) => [
        ...d.desired_outcomes.filter(o => ['deep_calm', 'letting_go', 'grounding', 'stress_reduction', 'increased_self_esteem', 'joyful_living'].includes(o.key)).map(e => ({ ...e, label: e.name[l] })),
        { key: IDK_KEY, name: { en: '', fr: '', es: '' }, description: { en: '', fr: '', es: '' }, label: t('recommendationAssistant.opt_surprise_me') }
    ],
    determineNextState: (answerKey, state) => {
      let updatedProfile = state.userProfile;
      if (answerKey !== IDK_KEY && answerKey) {
        updatedProfile.desired_outcomes.push(answerKey);
      }
      return { nextStateId: 'end_conversation', updatedProfile, updatedCompletedDomains: [...state.completedDomains, 'outcome'] };
    }
  },

  // =================================================================
  // == END STATE
  // =================================================================
  end_conversation: {
    type: 'recommendation_end',
    domain: 'initial',
    getText: () => '',
    determineNextState: (answerKey, state) => ({ nextStateId: 'end_conversation', updatedProfile: state.userProfile, updatedCompletedDomains: state.completedDomains })
  },
};