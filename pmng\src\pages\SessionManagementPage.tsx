// pmng/src/pages/SessionManagementPage.tsx

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import { db } from '../firebase';
import { collection, getDocs, doc, setDoc, getDoc } from 'firebase/firestore';
import CryptoJS from 'crypto-js';
import { FiSearch, FiEdit, FiPlus, FiGlobe } from 'react-icons/fi';
import EditScriptModal from '../components/EditScriptModal';
import TranslateScriptModal from '../components/TranslateScriptModal';
import BatchTranslateModal from '../components/BatchTranslateModal'; 

const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
if (!SHARED_KEY) throw new Error("VITE_SHARED_ENCRYPTION_KEY not defined");

// --- Interfaces ---
export interface ScriptStep {
  type: string;
  text: string;
  duration: number;
}
export interface ScriptData {
  id: string;
  title: string;
  description: string;
  script: ScriptStep[];
  [key: string]: any;
}
export interface UnifiedSession {
  id: string;
  category: string;
  en?: ScriptData;
  fr?: ScriptData;
  es?: ScriptData;
}
export interface EditingPayload {
  session: UnifiedSession;
  lang: 'en' | 'fr' | 'es';
}
export interface BatchTask {
  session: UnifiedSession;
  lang: 'en' | 'fr' | 'es';
}

// --- Styled Components ---
const PageWrapper = styled.div`
  display: flex; flex-direction: column; height: 100%; gap: 1rem;
`;
const Header = styled.div`
  display: flex; justify-content: space-between; align-items: center;
  flex-shrink: 0; h1 { font-size: 1.8rem; }
`;
const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;
const SearchContainer = styled.div`
  position: relative;
  width: 350px;
`;
const SearchInput = styled.input`
  width: 100%; padding: 10px 10px 10px 40px;
  border-radius: 6px; border: 1px solid ${({ theme }) => theme.border};
`;
const TableWrapper = styled.div`
  overflow-x: auto; background-color: ${({ theme }) => theme.surface};
  border-radius: 8px; box-shadow: ${({ theme }) => theme.cardShadow};
`;
const StyledTable = styled.table`
  width: 100%; border-collapse: collapse;
  th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid ${({ theme }) => theme.border}; }
  th { background-color: ${({ theme }) => theme.tableHeaderBackground}; font-weight: 600; }
  tr:hover { background-color: ${({ theme }) => theme.tableRowHoverBackground}; }
`;
const ActionButton = styled.button`
  display: inline-flex; align-items: center; gap: 6px; padding: 6px 10px;
  border: none; border-radius: 4px; cursor: pointer; font-size: 0.85rem;
  &.edit { background-color: ${({ theme }) => theme.primary}20; color: ${({ theme }) => theme.primary}; }
  &.create { background-color: ${({ theme }) => theme.success}20; color: ${({ theme }) => theme.success}; }
`;
const BatchButton = styled.button`
  padding: 10px 15px;
  border: none;
  background-color: ${({ theme }) => theme.secondary};
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
`;

const updateCategoryIndexIfNeeded = async (lang: string, newCategory: string) => {
    const langDocRef = doc(db, 'SessionScripts', lang);
    const docSnap = await getDoc(langDocRef);
    let currentCategories: string[] = [];
    if (docSnap.exists() && docSnap.data().encrypted) {
        try {
            const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8);
            currentCategories = JSON.parse(decrypted).categories || [];
        } catch (e) { console.error(`Failed to decrypt category index for lang '${lang}'.`, e); currentCategories = []; }
    }
    const categorySet = new Set(currentCategories);
    if (!categorySet.has(newCategory)) {
        categorySet.add(newCategory);
        const updatedCategories = Array.from(categorySet).sort();
        const payload = { categories: updatedCategories };
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(payload), SHARED_KEY).toString();
        await setDoc(langDocRef, { encrypted }, { merge: true });
    }
};

const SessionManagementPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [unifiedSessions, setUnifiedSessions] = useState<UnifiedSession[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingPayload, setEditingPayload] = useState<EditingPayload | null>(null);
  const [translationPayload, setTranslationPayload] = useState<EditingPayload | null>(null);
  
  const [batchTasks, setBatchTasks] = useState<BatchTask[]>([]);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);

  const fetchAllSessions = useCallback(async () => {
    setIsLoading(true);
    const languages = ['en', 'fr', 'es'];
    const sessionMap = new Map<string, UnifiedSession>();
    for (const lang of languages) {
      const categoriesDocRef = doc(db, 'SessionScripts', lang);
      const categoriesDoc = await getDoc(categoriesDocRef);
      if (!categoriesDoc.exists() || !categoriesDoc.data().encrypted) continue;
      let categories: string[] = [];
      try {
        categories = JSON.parse(CryptoJS.AES.decrypt(categoriesDoc.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8)).categories || [];
      } catch (e) { continue; }
      for (const category of categories) {
        const scriptsCollectionRef = collection(db, `SessionScripts/${lang}/${category}`);
        const scriptsSnapshot = await getDocs(scriptsCollectionRef);
        for (const scriptDoc of scriptsSnapshot.docs) {
          const uniqueKey = `${category}-${scriptDoc.id}`;
          if (!sessionMap.has(uniqueKey)) sessionMap.set(uniqueKey, { id: scriptDoc.id, category });
          const session = sessionMap.get(uniqueKey)!;
          try {
            session[lang as 'en' | 'fr' | 'es'] = JSON.parse(CryptoJS.AES.decrypt(scriptDoc.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8));
          } catch (e) { console.error(`Decrypt failed for ${scriptDoc.id}`); }
        }
      }
    }
    setUnifiedSessions(Array.from(sessionMap.values()).sort((a, b) => `${a.category}-${a.id}`.localeCompare(`${b.category}-${b.id}`)));
    setIsLoading(false);
  }, []);

  useEffect(() => {
    fetchAllSessions();
  }, [fetchAllSessions]);

  const handleCreate = (session: UnifiedSession, lang: 'en' | 'fr' | 'es') => {
    const templateScript = session.en || session.fr || session.es;
    if (!templateScript) { alert("Cannot create new version: a source script is required to serve as a template."); return; }
    setTranslationPayload({ session, lang });
  };

  const handleTranslationAccepted = (sessionWithNewTranslation: UnifiedSession, lang: 'en' | 'fr' | 'es') => {
    setTranslationPayload(null);
    setEditingPayload({ session: sessionWithNewTranslation, lang });
  };
  
  const handleSave = async (payload: EditingPayload, content: string) => {
    setIsLoading(true);
    const { session, lang } = payload;
    try {
      const dataToEncrypt = JSON.parse(content);
      dataToEncrypt.id = session.id;
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(dataToEncrypt), SHARED_KEY).toString();
      await setDoc(doc(db, `SessionScripts/${lang}/${session.category}`, session.id), { encrypted });
      await updateCategoryIndexIfNeeded(lang, session.category);
      alert(`Script "${dataToEncrypt.title}" in ${lang.toUpperCase()} saved successfully!`);
      fetchAllSessions();
      setEditingPayload(null);
    } catch (error) {
      alert('Error: Failed to save script. The JSON might be invalid.'); console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchTranslate = () => {
    const tasks: BatchTask[] = [];
    const languages: ('en' | 'fr' | 'es')[] = ['en', 'fr', 'es'];

    for (const session of unifiedSessions) {
      const hasTemplate = session.en || session.fr || session.es;
      if (!hasTemplate) continue;

      for (const lang of languages) {
        if (!session[lang]) {
          tasks.push({ session, lang });
        }
      }
    }

    if (tasks.length === 0) {
      alert("No missing translations found. All scripts are up-to-date!");
      return;
    }
    
    setBatchTasks(tasks);
    setIsBatchModalOpen(true);
  };

  const filteredSessions = useMemo(() => {
    if (!searchTerm) return unifiedSessions;
    const lowercasedTerm = searchTerm.toLowerCase();
    return unifiedSessions.filter(s =>
      s.id.toLowerCase().includes(lowercasedTerm) ||
      s.category.toLowerCase().includes(lowercasedTerm) ||
      Object.values(s).some(val => typeof val === 'object' && val?.title?.toLowerCase().includes(lowercasedTerm))
    );
  }, [unifiedSessions, searchTerm]);

  return (
    <>
      {translationPayload && ( <TranslateScriptModal isOpen={!!translationPayload} onClose={() => setTranslationPayload(null)} onAccept={handleTranslationAccepted} payload={translationPayload} /> )}
      {editingPayload && ( <EditScriptModal isOpen={!!editingPayload} onClose={() => setEditingPayload(null)} onSave={handleSave} payload={editingPayload} /> )}
      {isBatchModalOpen && ( 
        <BatchTranslateModal 
          isOpen={isBatchModalOpen} 
          onClose={() => setIsBatchModalOpen(false)} 
          onComplete={fetchAllSessions}
          tasks={batchTasks} 
        /> 
      )}

      <PageWrapper>
        <Header>
          <h1>Session Script Management</h1>
          <HeaderActions>
            <SearchContainer>
              <FiSearch style={{ position: 'absolute', top: 12, left: 12, color: '#888' }} />
              <SearchInput type="text" placeholder="Search by ID, category, or title..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </SearchContainer>
            <BatchButton onClick={handleBatchTranslate}>
              <FiGlobe /> Translate All Missing
            </BatchButton>
          </HeaderActions>
        </Header>
        {isLoading ? (
          <p>Loading all sessions, please wait...</p>
        ) : (
          <TableWrapper>
            <StyledTable>
              <thead>
                <tr><th>Category</th><th>Script ID</th><th>English</th><th>French</th><th>Spanish</th></tr>
              </thead>
              <tbody>
                {filteredSessions.map(session => (
                  <tr key={`${session.category}-${session.id}`}>
                    <td>{session.category}</td>
                    <td>{session.id}</td>
                    {(['en', 'fr', 'es'] as const).map(lang => (
                      <td key={lang}>
                        {session[lang] ? (
                          <ActionButton className="edit" onClick={() => setEditingPayload({ session, lang })}>
                            <FiEdit size={12} /> {session[lang]!.title}
                          </ActionButton>
                        ) : (
                          <ActionButton className="create" onClick={() => handleCreate(session, lang)}>
                             <FiPlus size={12} /> Create
                          </ActionButton>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </StyledTable>
          </TableWrapper>
        )}
      </PageWrapper>
    </>
  );
};

export default SessionManagementPage;