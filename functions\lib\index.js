"use strict";
// functions/src/index.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.listAllUsers = exports.getGoogleTTSAudio = exports.stripeWebhook = void 0;
const functions = __importStar(require("firebase-functions"));
const v1_1 = require("firebase-functions/v1");
const admin = __importStar(require("firebase-admin"));
const firestore_1 = require("firebase-admin/firestore");
const stripe_1 = __importDefault(require("stripe"));
const crypto_js_1 = __importDefault(require("crypto-js"));
const text_to_speech_1 = require("@google-cloud/text-to-speech");
const cors_1 = __importDefault(require("cors")); // <-- 1. Importez le package cors
admin.initializeApp();
const db = (0, firestore_1.getFirestore)();
// --- Configuration Checks ---
const stripeSecretKey = (_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.secret_key;
const stripeWebhookSecret = (_b = functions.config().stripe) === null || _b === void 0 ? void 0 : _b.webhook_secret;
if (!stripeSecretKey) {
    console.error("CRITICAL ERROR: Stripe secret key is not configured.");
}
if (!stripeWebhookSecret) {
    console.error("CRITICAL ERROR: Stripe webhook secret is not configured.");
}
const stripe = new stripe_1.default(stripeSecretKey, { apiVersion: '2025-05-28.basil' });
const ttsClient = new text_to_speech_1.TextToSpeechClient();
const getUserEncryptionKey = (uid) => uid;
// 2. Configurez les origines autorisées
const allowedOrigins = [
    'https://piknowkyo-777.web.app',
    'https://app.piknowkyo.com',
    'http://localhost:3000', // Assurez-vous que localhost est bien là pour le développement
];
// 3. Créez le gestionnaire cors avec les bonnes options
const corsHandler = (0, cors_1.default)({
    origin: (origin, callback) => {
        // Autorise les requêtes sans origine (ex: Postman, apps mobiles) et celles de notre liste
        if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    }
});
const getCustomerEmailFromEvent = async (event) => {
    var _a;
    const eventObject = event.data.object;
    if ((_a = eventObject.customer_details) === null || _a === void 0 ? void 0 : _a.email)
        return eventObject.customer_details.email;
    if (eventObject.customer_email)
        return eventObject.customer_email;
    if (eventObject.customer && typeof eventObject.customer === 'string') {
        try {
            const customer = await stripe.customers.retrieve(eventObject.customer);
            if (!customer.deleted && customer.email) {
                return customer.email;
            }
        }
        catch (error) {
            console.error(`Error retrieving Stripe customer ${eventObject.customer}:`, error);
            return null;
        }
    }
    return null;
};
exports.stripeWebhook = v1_1.https.onRequest(async (req, res) => {
    var _a, _b, _c, _d, _e, _f, _g;
    // ... (votre logique Stripe reste inchangée)
    if (!stripeWebhookSecret) {
        console.error('Stripe webhook secret not configured.');
        res.status(500).send('Internal Server Error: Webhook secret not configured.');
        return;
    }
    let event;
    try {
        event = stripe.webhooks.constructEvent(req.rawBody, req.headers['stripe-signature'], stripeWebhookSecret);
    }
    catch (err) {
        console.error(`Webhook signature verification failed.`, err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
    }
    let firebaseUid;
    const userEmail = await getCustomerEmailFromEvent(event);
    if (userEmail) {
        try {
            const userRecord = await admin.auth().getUserByEmail(userEmail);
            firebaseUid = userRecord.uid;
            console.log(`[Webhook] Matched event ${event.id} to user ${firebaseUid} via email ${userEmail}`);
        }
        catch (error) {
            if (error.code === 'auth/user-not-found') {
                console.warn(`[Webhook] Event for email ${userEmail} received, but no matching Firebase user was found.`);
            }
            else {
                console.error(`[Webhook] Error fetching user by email ${userEmail}:`, error);
            }
        }
    }
    else {
        console.warn(`[Webhook] Could not extract a customer email from event ${event.id} (type: ${event.type}).`);
    }
    if (!firebaseUid) {
        res.status(200).send('Webhook received, but no corresponding Firebase user could be identified. Ignoring event.');
        return;
    }
    const userSubscriptionRef = db.collection('users').doc(firebaseUid).collection('subscription').doc('main');
    try {
        const docSnap = await userSubscriptionRef.get();
        let currentSubscriptionData = {};
        if (docSnap.exists && ((_a = docSnap.data()) === null || _a === void 0 ? void 0 : _a.encrypted)) {
            try {
                const decryptedBytes = crypto_js_1.default.AES.decrypt(docSnap.data().encrypted, getUserEncryptionKey(firebaseUid));
                currentSubscriptionData = JSON.parse(decryptedBytes.toString(crypto_js_1.default.enc.Utf8));
            }
            catch (e) {
                console.error(`[Webhook] Error decrypting user ${firebaseUid} subscription data:`, e);
                currentSubscriptionData = {};
            }
        }
        else {
            console.log(`[Webhook] No existing subscription document for user ${firebaseUid}. Initializing new state.`);
        }
        let updatedSubscriptionState = null;
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object;
                if (session.mode === 'subscription' && session.subscription) {
                    const customerId = session.customer;
                    const newSubscriptionId = session.subscription;
                    const existingSubscriptions = await stripe.subscriptions.list({ customer: customerId, status: 'active', limit: 5 });
                    const otherActiveSubscriptions = existingSubscriptions.data.filter(sub => sub.id !== newSubscriptionId);
                    if (otherActiveSubscriptions.length > 0) {
                        console.error(`CRITICAL: Duplicate subscription for customer ${customerId}. New sub ${newSubscriptionId} will be canceled.`);
                        await stripe.subscriptions.cancel(newSubscriptionId);
                    }
                }
                break;
            case 'customer.subscription.created':
                const createdSub = event.data.object;
                const createdPeriodEnd = (_c = (_b = createdSub.items) === null || _b === void 0 ? void 0 : _b.data[0]) === null || _c === void 0 ? void 0 : _c.current_period_end;
                updatedSubscriptionState = Object.assign(Object.assign({}, currentSubscriptionData), { isActive: true, tier: 'premium', renewsAt: createdPeriodEnd ? new Date(createdPeriodEnd * 1000).toISOString() : null, willBeCanceled: createdSub.cancel_at_period_end, stripeCustomerId: createdSub.customer, stripeSubscriptionId: createdSub.id, updatedAt: new Date().toISOString(), isTrialActive: false, trialStarts: null, trialEnds: null });
                break;
            case 'customer.subscription.updated':
                const updatedSub = event.data.object;
                const updatedPeriodEnd = (_e = (_d = updatedSub.items) === null || _d === void 0 ? void 0 : _d.data[0]) === null || _e === void 0 ? void 0 : _e.current_period_end;
                updatedSubscriptionState = Object.assign(Object.assign({}, currentSubscriptionData), { isActive: updatedSub.status === 'active' || updatedSub.status === 'trialing', tier: (updatedSub.status === 'active' || updatedSub.status === 'trialing') ? 'premium' : 'free', renewsAt: updatedPeriodEnd ? new Date(updatedPeriodEnd * 1000).toISOString() : null, willBeCanceled: updatedSub.cancel_at_period_end, stripeCustomerId: updatedSub.customer, stripeSubscriptionId: updatedSub.id, updatedAt: new Date().toISOString() });
                break;
            case 'invoice.payment_succeeded':
                const invoice = event.data.object;
                if (invoice.subscription) {
                    const subFromInvoice = await stripe.subscriptions.retrieve(invoice.subscription);
                    const periodEndFromInvoice = (_g = (_f = subFromInvoice.items) === null || _f === void 0 ? void 0 : _f.data[0]) === null || _g === void 0 ? void 0 : _g.current_period_end;
                    updatedSubscriptionState = Object.assign(Object.assign({}, currentSubscriptionData), { isActive: subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing', tier: (subFromInvoice.status === 'active' || subFromInvoice.status === 'trialing') ? 'premium' : 'free', renewsAt: periodEndFromInvoice ? new Date(periodEndFromInvoice * 1000).toISOString() : null, willBeCanceled: subFromInvoice.cancel_at_period_end, stripeCustomerId: subFromInvoice.customer, stripeSubscriptionId: subFromInvoice.id, updatedAt: new Date().toISOString(), isTrialActive: false, trialStarts: null, trialEnds: null });
                }
                break;
            case 'customer.subscription.deleted':
                const deletedSubscription = event.data.object;
                updatedSubscriptionState = Object.assign(Object.assign({}, currentSubscriptionData), { isActive: false, tier: 'free', renewsAt: null, willBeCanceled: false, stripeSubscriptionId: deletedSubscription.id, updatedAt: new Date().toISOString() });
                break;
            case 'invoice.payment_failed':
                const failedInvoice = event.data.object;
                if (failedInvoice.subscription) {
                    const latestSubscription = await stripe.subscriptions.retrieve(failedInvoice.subscription);
                    const stillHasAccess = ['active', 'past_due'].includes(latestSubscription.status);
                    updatedSubscriptionState = Object.assign(Object.assign({}, currentSubscriptionData), { isActive: stillHasAccess, tier: stillHasAccess ? 'premium' : 'free', updatedAt: new Date().toISOString() });
                }
                break;
            default:
                console.log(`[Webhook] Unhandled event type ${event.type} for Firebase UID: ${firebaseUid}`);
                res.status(200).send(`Event type ${event.type} not handled.`);
                return;
        }
        if (updatedSubscriptionState) {
            const encrypted = crypto_js_1.default.AES.encrypt(JSON.stringify(updatedSubscriptionState), getUserEncryptionKey(firebaseUid)).toString();
            await userSubscriptionRef.set({ encrypted });
            console.log(`[Webhook] Subscription for user ${firebaseUid} updated by event: ${event.type}.`);
            res.status(200).send('Webhook received and processed successfully.');
        }
        else {
            res.status(200).send(`Webhook event ${event.type} received, no state change required.`);
        }
    }
    catch (error) {
        console.error(`[Webhook] Error processing webhook event ${event.id} for Firebase UID ${firebaseUid}:`, error);
        res.status(500).send(`Webhook Error: ${error.message}`);
    }
});
// 4. Enveloppez la logique de votre fonction avec le gestionnaire CORS
exports.getGoogleTTSAudio = v1_1.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        let idToken;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
            idToken = req.headers.authorization.split('Bearer ')[1];
        }
        if (!idToken) {
            res.status(401).send('Unauthorized: No authentication token provided.');
            return;
        }
        try {
            await admin.auth().verifyIdToken(idToken);
            const { text, lang, voiceName } = req.body;
            if (!text || !lang || !voiceName) {
                res.status(400).send('Bad Request: Text, language, and voice name are required.');
                return;
            }
            const request = {
                input: { text: text },
                voice: { languageCode: lang, name: voiceName },
                audioConfig: { audioEncoding: 'MP3' },
            };
            const [response] = await ttsClient.synthesizeSpeech(request);
            if (response.audioContent) {
                res.set('Content-Type', 'audio/mpeg');
                res.status(200).send(response.audioContent);
            }
            else {
                console.error('TTS synthesis failed to produce audio for request:', req.body);
                res.status(500).send('Internal Server Error: TTS synthesis failed.');
            }
        }
        catch (error) {
            console.error('Error in getGoogleTTSAudio:', error);
            if (error.code && error.code.startsWith('auth/')) {
                res.status(401).send(`Authentication Error: ${error.message}`);
            }
            else if (error.details) {
                res.status(500).send(`TTS API Error: ${error.details}`);
            }
            else {
                res.status(500).send(`Internal Server Error: ${error.message}`);
            }
        }
    });
});
// --- Admin Function: List All Users ---
exports.listAllUsers = v1_1.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            // Verify admin access (you can add more sophisticated auth here)
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).send('Unauthorized: Missing or invalid authorization header');
                return;
            }
            const idToken = authHeader.split('Bearer ')[1];
            try {
                // Verify the ID token
                const decodedToken = await admin.auth().verifyIdToken(idToken);
                // Check if user is admin (you can customize this logic)
                const adminEmail = '<EMAIL>'; // Replace with your admin email
                if (decodedToken.email !== adminEmail) {
                    res.status(403).send('Forbidden: Admin access required');
                    return;
                }
            }
            catch (authError) {
                console.error('Token verification failed:', authError);
                res.status(401).send('Unauthorized: Invalid token');
                return;
            }
            const allUsers = [];
            let nextPageToken;
            // Fetch all users from Firebase Auth
            do {
                const listUsersResult = await admin.auth().listUsers(1000, nextPageToken);
                for (const userRecord of listUsersResult.users) {
                    const userId = userRecord.uid;
                    // Get Firestore data for this user
                    let firestoreData = null;
                    let hasFirestoreData = false;
                    try {
                        // Check if user has a document in the main users collection
                        const userDocRef = db.collection('users').doc(userId);
                        const userDocSnap = await userDocRef.get();
                        if (userDocSnap.exists) {
                            hasFirestoreData = true;
                            firestoreData = {
                                mainDocument: userDocSnap.data(),
                                hasSubscription: false,
                                hasProfile: false,
                                hasActivity: false,
                                hasPreferences: false
                            };
                            // Check subcollections
                            const subcollections = ['subscription', 'profile', 'activity', 'preferences'];
                            for (const subcol of subcollections) {
                                try {
                                    const subDocRef = userDocRef.collection(subcol).doc('main');
                                    const subDocSnap = await subDocRef.get();
                                    if (subDocSnap.exists) {
                                        firestoreData[`has${subcol.charAt(0).toUpperCase() + subcol.slice(1)}`] = true;
                                    }
                                }
                                catch (subError) {
                                    console.warn(`Error checking ${subcol} for user ${userId}:`, subError);
                                }
                            }
                        }
                    }
                    catch (firestoreError) {
                        console.warn(`Error fetching Firestore data for user ${userId}:`, firestoreError);
                    }
                    allUsers.push({
                        uid: userRecord.uid,
                        email: userRecord.email,
                        displayName: userRecord.displayName,
                        photoURL: userRecord.photoURL,
                        emailVerified: userRecord.emailVerified,
                        disabled: userRecord.disabled,
                        creationTime: userRecord.metadata.creationTime,
                        lastSignInTime: userRecord.metadata.lastSignInTime,
                        providerData: userRecord.providerData.map(provider => ({
                            providerId: provider.providerId,
                            uid: provider.uid,
                            email: provider.email,
                            displayName: provider.displayName
                        })),
                        hasFirestoreData,
                        firestoreData
                    });
                }
                nextPageToken = listUsersResult.pageToken;
            } while (nextPageToken);
            console.log(`Retrieved ${allUsers.length} users from Firebase Auth`);
            res.status(200).json({
                success: true,
                totalUsers: allUsers.length,
                users: allUsers
            });
        }
        catch (error) {
            console.error('Error in listAllUsers:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
});
//# sourceMappingURL=index.js.map