// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import vitePluginCompression from 'vite-plugin-compression';
export default defineConfig({
    plugins: [
        react(),
        vitePluginCompression({
            algorithm: 'gzip',
            ext: '.gz',
            deleteOriginFile: false
        }),
        // The VitePWA plugin and its configuration have been completely removed.
    ],
    server: {
        port: 3000,
        open: true
    },
    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            },
        },
        rollupOptions: {
            output: {
                manualChunks: function (id) {
                    if (id.includes('node_modules')) {
                        return id.toString().split('node_modules/')[1].split('/')[0].toString();
                    }
                }
            }
        }
    },
    define: {
        global: 'globalThis',
    }
});
