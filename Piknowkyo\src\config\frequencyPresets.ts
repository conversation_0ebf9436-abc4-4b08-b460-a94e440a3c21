// /src/config/frequencyPresets.ts

// This file centralizes the definitions for binaural beat frequency presets,
// making them easier to manage, update, and import where needed.

/**
 * Defines the structure for a single frequency preset option.
 */
export interface FrequencyPreset {
    id: string; // A unique identifier for the preset, e.g., "solfeggio_528"
    value: number; // The actual frequency value in Hz
    labelKey: string; // The i18n key for the display name in the select dropdown
    descriptionKey: string; // The i18n key for the detailed descriptive text
  }
  
  /**
   * Defines the structure for a category of frequency presets, used for <optgroup>.
   */
  export interface PresetCategory {
    categoryId: string; // A unique identifier for the category, e.g., "solfeggio"
    categoryLabelKey: string; // The i18n key for the category's <optgroup> label
    presets: FrequencyPreset[];
  }
  
  /**
   * A utility function to find a specific preset by its ID from a list of categories.
   * @param categories - The array of PresetCategory to search within.
   * @param presetId - The ID of the preset to find.
   * @returns The found FrequencyPreset object or null if not found.
   */
  export const findPresetById = (categories: PresetCategory[], presetId: string | null | undefined): FrequencyPreset | null => {
    if (!presetId) return null;
    for (const category of categories) {
      const found = category.presets.find(p => p.id === presetId);
      if (found) return found;
    }
    return null;
  };
  
  // --- DATA FOR BASE FREQUENCIES ---
  
  export const baseFrequencyPresetCategories: PresetCategory[] = [
    {
      categoryId: "solfeggio", categoryLabelKey: "audioConfig.baseFreqPresets.solfeggio.category",
      presets: [
        { id: "solfeggio_174", value: 174, labelKey: "audioConfig.baseFreqPresets.solfeggio.174.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.174.desc" },
        { id: "solfeggio_285", value: 285, labelKey: "audioConfig.baseFreqPresets.solfeggio.285.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.285.desc" },
        { id: "solfeggio_396", value: 396, labelKey: "audioConfig.baseFreqPresets.solfeggio.396.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.396.desc" },
        { id: "solfeggio_417", value: 417, labelKey: "audioConfig.baseFreqPresets.solfeggio.417.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.417.desc" },
        { id: "solfeggio_528", value: 528, labelKey: "audioConfig.baseFreqPresets.solfeggio.528.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.528.desc" },
        { id: "solfeggio_639", value: 639, labelKey: "audioConfig.baseFreqPresets.solfeggio.639.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.639.desc" },
        { id: "solfeggio_741", value: 741, labelKey: "audioConfig.baseFreqPresets.solfeggio.741.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.741.desc" },
        { id: "solfeggio_852", value: 852, labelKey: "audioConfig.baseFreqPresets.solfeggio.852.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.852.desc" },
        { id: "solfeggio_963", value: 963, labelKey: "audioConfig.baseFreqPresets.solfeggio.963.label", descriptionKey: "audioConfig.baseFreqPresets.solfeggio.963.desc" },
      ]
    },
    {
      categoryId: "chakras", categoryLabelKey: "audioConfig.baseFreqPresets.chakras.category",
      presets: [
        { id: "chakra_root_base", value: 256, labelKey: "audioConfig.baseFreqPresets.chakras.root.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.root.desc" },
        { id: "chakra_sacral_base", value: 288, labelKey: "audioConfig.baseFreqPresets.chakras.sacral.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.sacral.desc" },
        { id: "chakra_solarPlexus_base", value: 320, labelKey: "audioConfig.baseFreqPresets.chakras.solarPlexus.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.solarPlexus.desc" },
        { id: "chakra_heart_base", value: 341.3, labelKey: "audioConfig.baseFreqPresets.chakras.heart.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.heart.desc" },
        { id: "chakra_throat_base", value: 384, labelKey: "audioConfig.baseFreqPresets.chakras.throat.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.throat.desc" },
        { id: "chakra_thirdEye_base", value: 426.7, labelKey: "audioConfig.baseFreqPresets.chakras.thirdEye.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.thirdEye.desc" },
        { id: "chakra_crown_base", value: 480, labelKey: "audioConfig.baseFreqPresets.chakras.crown.label", descriptionKey: "audioConfig.baseFreqPresets.chakras.crown.desc" },
      ]
    },
    {
      categoryId: "planetary", categoryLabelKey: "audioConfig.baseFreqPresets.planetary.category",
      presets: [
        { id: "planetary_om_earth_year", value: 136.10, labelKey: "audioConfig.baseFreqPresets.planetary.om.label", descriptionKey: "audioConfig.baseFreqPresets.planetary.om.desc" },
        { id: "planetary_sun", value: 126.22, labelKey: "audioConfig.baseFreqPresets.planetary.sun.label", descriptionKey: "audioConfig.baseFreqPresets.planetary.sun.desc" },
        { id: "planetary_earth", value: 194.18, labelKey: "audioConfig.baseFreqPresets.planetary.earth.label", descriptionKey: "audioConfig.baseFreqPresets.planetary.earth.desc" },
        { id: "planetary_moon", value: 210.42, labelKey: "audioConfig.baseFreqPresets.planetary.moon.label", descriptionKey: "audioConfig.baseFreqPresets.planetary.moon.desc" },
      ]
    },
    {
      categoryId: "organsDetailed", categoryLabelKey: "audioConfig.baseFreqPresets.organsDetailed.category",
      presets: [
          { id: "organ_pineal", value: 662, labelKey: "audioConfig.baseFreqPresets.organsDetailed.pineal.label", descriptionKey: "audioConfig.baseFreqPresets.organsDetailed.pineal.desc" },
          { id: "organ_pituitary", value: 636, labelKey: "audioConfig.baseFreqPresets.organsDetailed.pituitary.label", descriptionKey: "audioConfig.baseFreqPresets.organsDetailed.pituitary.desc" },
          { id: "organ_brain_general", value: 330, labelKey: "audioConfig.baseFreqPresets.organsDetailed.brain_general.label", descriptionKey: "audioConfig.baseFreqPresets.organsDetailed.brain_general.desc" },
      ]
    },
    {
      categoryId: "otherNotable", categoryLabelKey: "audioConfig.baseFreqPresets.otherNotable.category",
      presets: [
        { id: "other_432hz", value: 432, labelKey: "audioConfig.baseFreqPresets.otherNotable.432hz.label", descriptionKey: "audioConfig.baseFreqPresets.otherNotable.432hz.desc" },
      ]
    }
  ];
  
  // --- DATA FOR BEAT FREQUENCIES ---
  
  export const beatFrequencyPresetCategories: PresetCategory[] = [
    {
      categoryId: "delta", categoryLabelKey: "audioConfig.beatPresets.delta.category",
      presets: [
        { id: "delta_2_5hz", value: 2.5, labelKey: "audioConfig.beatPresets.delta.2_5hz.label", descriptionKey: "audioConfig.beatPresets.delta.2_5hz.desc" },
      ]
    },
    {
      categoryId: "theta", categoryLabelKey: "audioConfig.beatPresets.theta.category",
      presets: [
        { id: "theta_5hz", value: 5, labelKey: "audioConfig.beatPresets.theta.5hz.label", descriptionKey: "audioConfig.beatPresets.theta.5hz.desc" },
        { id: "theta_7_83hz", value: 7.83, labelKey: "audioConfig.beatPresets.theta.7_83hz.label", descriptionKey: "audioConfig.beatPresets.theta.7_83hz.desc" }, 
      ]
    },
    {
      categoryId: "alpha", categoryLabelKey: "audioConfig.beatPresets.alpha.category",
      presets: [
        { id: "alpha_10hz", value: 10, labelKey: "audioConfig.beatPresets.alpha.10hz.label", descriptionKey: "audioConfig.beatPresets.alpha.10hz.desc" },
      ]
    },
    {
      categoryId: "beta", categoryLabelKey: "audioConfig.beatPresets.beta.category",
      presets: [
        { id: "beta_14hz", value: 14, labelKey: "audioConfig.beatPresets.beta.14hz.label", descriptionKey: "audioConfig.beatPresets.beta.14hz.desc" }, 
      ]
    },
    {
      categoryId: "gamma", categoryLabelKey: "audioConfig.beatPresets.gamma.category",
      presets: [
        { id: "gamma_40hz", value: 40, labelKey: "audioConfig.beatPresets.gamma.40hz.label", descriptionKey: "audioConfig.beatPresets.gamma.40hz.desc" },
      ]
    },
    {
      categoryId: "spiritualStates", categoryLabelKey: "audioConfig.beatPresets.spiritualStates.category",
      presets: [
        { id: "spiritual_pineal_activation_theta", value: 7.5, labelKey: "audioConfig.beatPresets.spiritualStates.pinealActivationTheta.label", descriptionKey: "audioConfig.beatPresets.spiritualStates.pinealActivationTheta.desc" },
        { id: "spiritual_chakra_cleansing", value: 6, labelKey: "audioConfig.beatPresets.spiritualStates.chakraCleansing.label", descriptionKey: "audioConfig.beatPresets.spiritualStates.chakraCleansing.desc" },
        { id: "spiritual_astral_projection", value: 6.5, labelKey: "audioConfig.beatPresets.spiritualStates.astralProjection.label", descriptionKey: "audioConfig.beatPresets.spiritualStates.astralProjection.desc" },
        { id: "spiritual_kundalini_support", value: 8, labelKey: "audioConfig.beatPresets.spiritualStates.kundaliniSupport.label", descriptionKey: "audioConfig.beatPresets.spiritualStates.kundaliniSupport.desc" },
        { id: "spiritual_merkaba_meditation", value: 10.5, labelKey: "audioConfig.beatPresets.spiritualStates.merkabaMeditation.label", descriptionKey: "audioConfig.beatPresets.spiritualStates.merkabaMeditation.desc" },
      ]
    },
    {
      categoryId: "cognitiveEnhancement", categoryLabelKey: "audioConfig.beatPresets.cognitiveEnhancement.category",
      presets: [
          { id: "cognitive_creativity_boost_theta", value: 5.5, labelKey: "audioConfig.beatPresets.cognitiveEnhancement.creativityBoostTheta.label", descriptionKey: "audioConfig.beatPresets.cognitiveEnhancement.creativityBoostTheta.desc" },
          { id: "cognitive_anxiety_reduction_alpha", value: 10, labelKey: "audioConfig.beatPresets.cognitiveEnhancement.anxietyReductionAlpha.label", descriptionKey: "audioConfig.beatPresets.cognitiveEnhancement.anxietyReductionAlpha.desc" },
          { id: "cognitive_sleep_improvement_delta", value: 2, labelKey: "audioConfig.beatPresets.cognitiveEnhancement.sleepImprovementDelta.label", descriptionKey: "audioConfig.beatPresets.cognitiveEnhancement.sleepImprovementDelta.desc" },
      ]
    }
  ];