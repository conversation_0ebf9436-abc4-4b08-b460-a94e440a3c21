// src/store/useAppStore.ts

import { create } from 'zustand';
import { doc, onSnapshot, Unsubscribe, setDoc, getDoc, collection, getDocs, runTransaction } from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import CryptoJS from 'crypto-js';
import { db, functions } from '../firebase';
import { BlogPost } from '../types/blog';
import { JournalEntry, SessionAudioConfig } from '../models';
import { AppDefinitionsFile, Dictionary, DictionaryElement, SessionRecommendationTags } from '../types/definitions';
import { SavedGameState, PersonalBestGameScore } from '../games/common/models';
import { getAuth } from 'firebase/auth';

export type { SessionAudioConfig };

// RE-INTRODUCED: The shared key is now used on the client for faster session decryption.
const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;
if (!SHARED_KEY) { throw new Error("VITE_SHARED_ENCRYPTION_KEY is not defined in your environment variables."); }

export interface PricingConfig { trialDays: number; premiumPrice: number; premiumCurrency: string; }
type TTSProvider = 'browser' | 'cloud';
type Language = 'fr' | 'en' | 'es';
export interface QuickAccessLink { type: 'page' | 'session_category' | 'session' | 'game' | 'tool' | 'game_category'; id: string; }

export interface AudioAsset { 
  id: string; 
  name: string; 
  url: string; 
  isUserUploaded: boolean;
  storagePath?: string;
}
export interface AudioAssetState { musics: AudioAsset[]; ambiants: AudioAsset[]; updatedAt?: string; }

export interface SubscriptionState {
  isActive: boolean;
  tier: 'free' | 'premium';
  isTrialActive: boolean;
  trialStarts: string | null;
  trialEnds: string | null;
  renewsAt: string | null;
  willBeCanceled: boolean;
  adUnlocks: { unlockedItems: { [itemId:string]: string } };
  premiumFeatures: {
    games: boolean;
    binauralBeats: boolean;
    ambientSounds: boolean;
    advancedSessions: boolean;
    customAudio: boolean;
    noAds: true,
    cloudTTS: boolean,
    advancedAnalytics: boolean,
  };
  updatedAt?: string;
  stripeCustomerId?: string | null;
  stripeSubscriptionId?: string | null;
}

export type AdRewardPayload = { itemId: string; timestamp: number; duration: number; };

export interface SessionScript {
  id: string;
  type: string;
  title: string;
  description:string;
  duration: number;
  script?: any[];
  benefits?: string[];
  tags?: string[];
  imageUrl?: string;
  rating?: number;
  ratingCount?: number;
  comments?: string[];
  audio?: any;
  updatedAt?: { toDate: () => Date };
  recommendation_metadata?: SessionRecommendationTags;
}

export interface UserProfile { anonymousPseudo: string; updatedAt?: string; }

export interface BinauralConfig {
    enabled: boolean;
    baseFrequency: number;
    beatFrequency: number;
    volume: number;
    baseFrequencyPresetId?: string | null;
    beatFrequencyPresetId?: string | null;
}

export interface MusicConfig {
    enabled: boolean;
    url: string;
    volume: number;
}
export interface AmbientConfig {
    enabled: boolean;
    url: string;
    volume: number;
}

export interface UserPreferences {
    appTheme?: 'light' | 'dark';
    grammaticalGender: 'masculine' | 'feminine' | 'neutral';
    quickAccessLinks?: QuickAccessLink[];
    ttsConfig: { provider: TTSProvider; voice: string; lang: Language; volume: number; };
    binauralConfig: BinauralConfig;
    musicConfig: MusicConfig;
    ambientConfig: AmbientConfig;
    updatedAt?: string;
}
export interface UserActivity {
    completedSessions: { id: string; completedAt: string }[];
    streak: number;
    totalMinutes: number;
    favoriteSessions: string[];
    ratedSessions: { [sessionId: string]: number };
    lastActivityDate?: string;
    updatedAt?: string;
}

type SessionState = {
  categories: string[];
  scripts: { [category: string]: SessionScript[] };
  isLoading: boolean;
  scriptsLoading: { [category: string]: boolean };
};

export interface DefinitionSlice {
  dictionary: Dictionary | null;
  dictionaryMap: Map<string, DictionaryElement>;
  isLoading: boolean;
}

export interface DefinitionModalSlice {
  definitionKey: string | null;
}

interface PrivateDoc {
  id: string;
  data: {
    type: string;
    [key: string]: any;
  };
}

export interface GameProgressState {
    progress: { [gameId: string]: SavedGameState };
    personalBest: { [gameId: string]: PersonalBestGameScore };
    updatedAt?: string;
}

export type SyncStatus = 'idle' | 'syncing' | 'synced' | 'error';

// This is the main state definition for the store
export interface AppState {
  pricingConfig: PricingConfig | null;
  sessionAudioConfig: SessionAudioConfig;
  subscription: SubscriptionState | null;
  sessions: SessionState;
  definitions: DefinitionSlice;
  definitionModal: DefinitionModalSlice;
  profile: UserProfile | null;
  preferences: UserPreferences | null;
  activity: UserActivity | null;
  audioAssets: AudioAssetState | null;
  gameProgress: GameProgressState | null;
  privateData: PrivateDoc[];
  isInitialized: boolean;
  syncStatus: SyncStatus;
  // Actions
  init: (uid: string, lang: string) => Promise<void>;
  cleanup: () => void;
  toggleFavoriteSession: (uid: string, sessionId: string) => Promise<void>;
  rateSession: (uid: string, lang: string, category: string, sessionId: string, newRating: number) => Promise<void>;
  setSessionAudioConfig: (config: SessionAudioConfig) => void;
  initializeNewUser: (uid: string) => Promise<void>;
  applyAdReward: (uid: string, payload: AdRewardPayload) => Promise<void>;
  updateSubscription: (uid: string, updates: Partial<SubscriptionState>) => Promise<void>;
  updateProfile: (uid: string, data: Partial<UserProfile>) => Promise<void>;
  updatePreferences: (uid: string, data: Partial<UserPreferences>) => Promise<void>;
  addAudioAsset: (uid: string, asset: AudioAsset, type: 'musics' | 'ambiants') => Promise<void>;
  deleteAudioAsset: (uid: string, assetId: string, type: 'musics' | 'ambiants') => Promise<void>;
  addCompletedSession: (uid: string, sessionId: string) => Promise<void>;
  addActivityTime: (uid: string, minutesToAdd: number) => Promise<void>;
  showDefinitionInModal: (key: string) => void;
  hideDefinitionModal: () => void;
  saveGameProgress: (uid: string, gameId: string, gameState: SavedGameState) => Promise<void>;
  clearGameProgress: (uid: string, gameId: string) => Promise<void>;
  setPersonalBestScore: (uid: string, gameId: string, score: number, level: number) => Promise<void>;
  addJournalEntry: (uid: string, data: { sessionId: string; note: string; mood?: JournalEntry['mood'] }) => Promise<void>;
  updateJournalEntry: (uid: string, entryId: string, updates: Partial<Pick<JournalEntry, 'note' | 'mood'>>) => Promise<void>;
  deleteJournalEntry: (uid: string, entryId: string) => Promise<void>;
  setSyncStatus: (status: SyncStatus) => void;
  
  // REMOVED: Stripe actions are no longer needed as we use direct links.
  
  // Google TTS action
  getTTSAudioURL: (text: string, lang: string, voiceName: string) => Promise<string>;
}


let unsubscribers: Unsubscribe[] = [];

// REMOVED: All callable functions for Stripe are no longer needed.

// --- Defaults and helper functions ---
const createDefaultSubscriptionState = (): SubscriptionState => ({
  updatedAt: new Date(0).toISOString(),
  isActive: false,
  tier: 'free',
  isTrialActive: false,
  trialStarts: null,
  trialEnds: null,
  renewsAt: null,
  willBeCanceled: false,
  adUnlocks: { unlockedItems: {} },
  premiumFeatures: { games: false, binauralBeats: false, ambientSounds: false, advancedSessions: false, customAudio: false, noAds: true, cloudTTS: false, advancedAnalytics: false },
  stripeCustomerId: null,
  stripeSubscriptionId: null,
});

const createDefaultProfileState = (): UserProfile => ({ updatedAt: new Date(0).toISOString(), anonymousPseudo: '' });
const defaultQuickAccessLinks: QuickAccessLink[] = [ { type: 'page', id: 'sessions' }, { type: 'page', id: 'journal' }, { type: 'session_category', id: 'meditation' }, { type: 'tool', id: 'recommendation' }, ];
const createDefaultBinauralConfig = (): BinauralConfig => ({ enabled: false, baseFrequency: 256, beatFrequency: 10, volume: 0.15, baseFrequencyPresetId: null, beatFrequencyPresetId: null });
const createDefaultMusicConfig = (): MusicConfig => ({ enabled: false, url: '', volume: 0.5 });
const createDefaultAmbientConfig = (): AmbientConfig => ({ enabled: false, url: '', volume: 0.3 });
const createDefaultPreferencesState = (): UserPreferences => ({ updatedAt: new Date(0).toISOString(), appTheme: 'light', grammaticalGender: 'neutral', quickAccessLinks: defaultQuickAccessLinks, ttsConfig: { provider: 'browser', voice: 'auto', lang: 'en', volume: 1.0 }, binauralConfig: createDefaultBinauralConfig(), musicConfig: createDefaultMusicConfig(), ambientConfig: createDefaultAmbientConfig(), });
const createDefaultActivityState = (): UserActivity => ({ updatedAt: new Date(0).toISOString(), completedSessions: [], streak: 0, totalMinutes: 0, favoriteSessions: [], ratedSessions: {}, lastActivityDate: undefined });
const createDefaultAudioAssetsState = (): AudioAssetState => ({ updatedAt: new Date(0).toISOString(), musics: [], ambiants: [] });
const createDefaultGameProgressState = (): GameProgressState => ({ progress: {}, personalBest: {}, updatedAt: new Date().toISOString() }); 
const calculatePremiumFeatures = (sub: Partial<SubscriptionState>): SubscriptionState['premiumFeatures'] => { const hasFullAccess = !!(sub.isActive || sub.isTrialActive); return { games: hasFullAccess, binauralBeats: hasFullAccess, ambientSounds: hasFullAccess, advancedSessions: hasFullAccess, customAudio: hasFullAccess, noAds: true, cloudTTS: hasFullAccess, advancedAnalytics: hasFullAccess, }; };
const createDefaultSessionAudioConfig = (): SessionAudioConfig => ({ enableMusic: false, enableAmbient: false, enableBinaural: false, music: { url: '', volume: 0.5 }, ambient: { url: '', volume: 0.3 }, binaural: { baseFreq: 100, beatFreq: 10, volume: 0.2 }, voice: {}, });

// RE-INTRODUCED: Fast, client-side decryption logic.
const fetchScriptsForCategory = async (lang: string, category: string): Promise<SessionScript[]> => {
    try {
        const scriptsCollectionRef = collection(db, `SessionScripts/${lang}/${category}`);
        const scriptsSnapshot = await getDocs(scriptsCollectionRef);
        return scriptsSnapshot.docs.map(sdoc => {
            const encryptedData = sdoc.data().encrypted;
            const decryptedBytes = CryptoJS.AES.decrypt(encryptedData, SHARED_KEY);
            const decryptedJson = JSON.parse(decryptedBytes.toString(CryptoJS.enc.Utf8));
            const { durationMinutes, ...restOfScript } = decryptedJson;
            return { ...restOfScript, id: sdoc.id, duration: durationMinutes } as SessionScript;
        });
    } catch (error) {
        console.error(`[AppStore] Failed to fetch/decrypt scripts for category '${category}':`, error);
        return [];
    }
};

export const useAppStore = create<AppState>((set, get) => ({
  pricingConfig: null,
  sessionAudioConfig: createDefaultSessionAudioConfig(),
  sessions: { categories: [], scripts: {}, isLoading: true, scriptsLoading: {} },
  definitions: { dictionary: null, dictionaryMap: new Map(), isLoading: true },
  definitionModal: { definitionKey: null },
  subscription: null,
  profile: null,
  preferences: null,
  activity: null,
  audioAssets: null,
  gameProgress: null,
  privateData: [],
  isInitialized: false,
  syncStatus: 'idle',

  showDefinitionInModal: (key) => set({ definitionModal: { definitionKey: key } }),
  hideDefinitionModal: () => set({ definitionModal: { definitionKey: null } }),
  setSessionAudioConfig: (config) => set({ sessionAudioConfig: config }),
  setSyncStatus: (status) => set({ syncStatus: status }),

  init: async (uid, lang) => {
    if (get().isInitialized) return;
    set({ isInitialized: false, sessions: { categories: [], scripts: {}, isLoading: true, scriptsLoading: {} }, definitions: { dictionary: null, dictionaryMap: new Map(), isLoading: true } });
    
    try {
        const pricingDocSnap = await getDoc(doc(db, 'config', 'pricing'));
        set({ pricingConfig: pricingDocSnap.exists() ? pricingDocSnap.data() as PricingConfig : { trialDays: 14, premiumPrice: 0, premiumCurrency: 'USD' } });
    } catch (error) { console.error("[AppStore] Failed to fetch pricing config:", error); }
    
    await get().initializeNewUser(uid);

    const configMap = {
        subscription: { ref: doc(db, 'users', uid, 'subscription', 'main'), setter: (data: any) => set({ subscription: data }), default: createDefaultSubscriptionState },
        profile: { ref: doc(db, 'users', uid, 'profile', 'main'), setter: (data: any) => set({ profile: data }), default: createDefaultProfileState },
        preferences: { ref: doc(db, 'users', uid, 'preferences', 'main'), setter: (data: any) => set({ preferences: data }), default: createDefaultPreferencesState },
        activity: { ref: doc(db, 'users', uid, 'activity', 'main'), setter: (data: any) => set({ activity: data }), default: createDefaultActivityState },
        audioAssets: { ref: doc(db, 'users', uid, 'audioAssets', 'main'), setter: (data: any) => set({ audioAssets: data }), default: createDefaultAudioAssetsState },
        gameProgress: { ref: doc(db, 'users', uid, 'gameProgress', 'main'), setter: (data: any) => set({ gameProgress: data }), default: createDefaultGameProgressState },
        privateData: { ref: doc(db, 'users',uid, 'private', 'main'), setter: (data: any) => set({ privateData: data }), default: () => [] as PrivateDoc[] },
    };

    unsubscribers.forEach(unsub => unsub());
    unsubscribers = [];

    Object.entries(configMap).forEach(([name, config]) => {
      const unsub = onSnapshot(config.ref, (docSnap) => {
        let incomingState;

        if (docSnap.exists() && docSnap.data()?.encrypted) {
          try {
            const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, uid).toString(CryptoJS.enc.Utf8);
            const parsedState = JSON.parse(decrypted || '{}');
            
            if (name === 'privateData') {
                incomingState = parsedState.docs || [];
            } else {
                incomingState = { ...config.default(), ...parsedState };
            }
          } catch (e) {
            console.error(`[AppStore onSnapshot - ${name}] Decryption failed or data corrupt. Using default state.`, e);
            incomingState = config.default();
          }
        } else {
            console.log(`[AppStore onSnapshot - ${name}] Doc does not exist or not encrypted. Using default state.`);
            incomingState = config.default();
        }
        
        if (name === 'subscription') {
            (incomingState as SubscriptionState).premiumFeatures = calculatePremiumFeatures(incomingState as SubscriptionState);
        }
        else if (name === 'preferences' && incomingState) {
            incomingState.binauralConfig = { ...createDefaultBinauralConfig(), ...(incomingState.binauralConfig || {}) };
            incomingState.musicConfig = { ...createDefaultMusicConfig(), ...(incomingState.musicConfig || {}) };
            incomingState.ambientConfig = { ...createDefaultAmbientConfig(), ...(incomingState.ambientConfig || {}) };
        }
        else if (name === 'gameProgress' && incomingState) {
            incomingState.personalBest = { ...(incomingState.personalBest || {}) };
        }

        config.setter(incomingState);
      });
      unsubscribers.push(unsub);
    });

    try {
      // REVERTED: Logic now runs on the client for speed.
      const definitionsPromise = import('../data/definition.json').then(module => module.default as AppDefinitionsFile);
      const categoriesPromise = getDoc(doc(db, 'SessionScripts', lang)).then(catDoc => {
          if (catDoc.exists() && catDoc.data()?.encrypted) {
              const decrypted = CryptoJS.AES.decrypt(catDoc.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8);
              return JSON.parse(decrypted)?.categories || [];
          }
          return [];
      });
      
      const [definitionsFile, categories] = await Promise.all([definitionsPromise, categoriesPromise]);
      const scriptsByCategoryArray = await Promise.all(categories.map((cat: string) => fetchScriptsForCategory(lang, cat)));
      const scripts = categories.reduce((acc: { [key: string]: SessionScript[] }, cat: string, i: number) => ({ ...acc, [cat]: scriptsByCategoryArray[i] }), {});
      
      const dictionaryMap = new Map<string, DictionaryElement>();
      Object.values(definitionsFile.recommendation_metadata.dictionary).filter(Array.isArray).forEach(arr => arr.forEach((el: DictionaryElement) => dictionaryMap.set(el.key, el)));

      set({ sessions: { categories, scripts, isLoading: false, scriptsLoading: {} }, definitions: { dictionary: definitionsFile.recommendation_metadata.dictionary, dictionaryMap, isLoading: false }, isInitialized: true });
    } catch (error) {
      console.error("[AppStore] CRITICAL: Failed to initialize core app data:", error);
      set(state => ({ sessions: { ...state.sessions, isLoading: false }, definitions: { ...state.definitions, isLoading: false }, isInitialized: true }));
    }
  },
  
  cleanup: () => {
    unsubscribers.forEach((unsub) => unsub());
    unsubscribers = [];
    set({
      pricingConfig: null, sessionAudioConfig: createDefaultSessionAudioConfig(), subscription: null,
      sessions: { categories: [], scripts: {}, isLoading: true, scriptsLoading: {} },
      definitions: { dictionary: null, dictionaryMap: new Map(), isLoading: true },
      definitionModal: { definitionKey: null }, profile: null, preferences: null, activity: null,
      audioAssets: null, gameProgress: null, privateData: [], isInitialized: false, syncStatus: 'idle',
    });
  },

  initializeNewUser: async (uid: string) => {
    const pricingConfig = get().pricingConfig;
    const configMap = {
        subscription: { ref: doc(db, 'users', uid, 'subscription', 'main'), createState: createDefaultSubscriptionState },
        profile: { ref: doc(db, 'users', uid, 'profile', 'main'), createState: createDefaultProfileState },
        preferences: { ref: doc(db, 'users', uid, 'preferences', 'main'), createState: createDefaultPreferencesState },
        activity: { ref: doc(db, 'users', uid, 'activity', 'main'), createState: createDefaultActivityState },
        audioAssets: { ref: doc(db, 'users', uid, 'audioAssets', 'main'), createState: createDefaultAudioAssetsState },
        gameProgress: { ref: doc(db, 'users', uid, 'gameProgress', 'main'), createState: createDefaultGameProgressState },
        privateData: { ref: doc(db, 'users', uid, 'private', 'main'), createState: () => ({ docs: [] }) },
    };

    for (const [name, config] of Object.entries(configMap)) {
        const docSnap = await getDoc(config.ref);
        if (!docSnap.exists()) {
            let stateToSave: any;
            if (name === 'subscription') {
                stateToSave = createDefaultSubscriptionState();
                if (pricingConfig && pricingConfig.trialDays > 0) {
                    const now = new Date();
                    const trialEndDate = new Date(now);
                    trialEndDate.setDate(trialEndDate.getDate() + pricingConfig.trialDays);
                    stateToSave.isTrialActive = true;
                    stateToSave.trialStarts = now.toISOString();
                    stateToSave.trialEnds = trialEndDate.toISOString();
                }
            } else { stateToSave = config.createState(); }
            
            if (name !== 'privateData') {
                stateToSave.updatedAt = new Date().toISOString();
            }
            
            const encrypted = CryptoJS.AES.encrypt(JSON.stringify(stateToSave), uid).toString();
            await setDoc(config.ref, { encrypted });
        }
    }
  },

  saveGameProgress: async (uid, gameId, gameState) => {
    if (!uid) { console.error("Cannot save game progress: user not logged in."); return; }
    const currentState = get().gameProgress || createDefaultGameProgressState();
    const newProgress = { ...currentState.progress, [gameId]: gameState };
    const newState: GameProgressState = { ...currentState, progress: newProgress, updatedAt: new Date().toISOString() };
    set({ gameProgress: newState });
    try {
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString();
        await setDoc(doc(db, 'users', uid, 'gameProgress', 'main'), { encrypted });
    } catch (error) {
        console.error("Failed to persist game progress:", error);
    }
  },

  clearGameProgress: async (uid, gameId) => {
    if (!uid) { console.error("Cannot clear game progress: user not logged in."); return; }
    const currentState = get().gameProgress || createDefaultGameProgressState();
    const newProgress = { ...currentState.progress };
    delete newProgress[gameId];
    const newState: GameProgressState = { ...currentState, progress: newProgress, updatedAt: new Date().toISOString() };
    set({ gameProgress: newState });
    try {
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString();
        await setDoc(doc(db, 'users', uid, 'gameProgress', 'main'), { encrypted });
    } catch (error) {
        console.error("Failed to persist game progress clearance:", error);
    }
  },

  setPersonalBestScore: async (uid, gameId, score, level) => {
    if (!uid) { console.error("Cannot set personal best score: user not logged in."); return; }
    
    const currentState = get().gameProgress || createDefaultGameProgressState();
    const currentBest = currentState.personalBest[gameId];
    
    if (!currentBest || score > currentBest.score) {
        const newPersonalBest: PersonalBestGameScore = { score, level, timestamp: new Date() };
        const newPersonalBests = { ...currentState.personalBest, [gameId]: newPersonalBest };
        
        const newState: GameProgressState = { ...currentState, personalBest: newPersonalBests, updatedAt: new Date().toISOString() };
        set({ gameProgress: newState });
        
        try {
            const encrypted = CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString();
            await setDoc(doc(db, 'users', uid, 'gameProgress', 'main'), { encrypted });
        } catch (error) {
            console.error("Failed to persist personal best score:", error);
        }
    }
  },

  addActivityTime: async (uid, minutesToAdd) => {
    if (!uid) { console.error("Cannot add activity time: user not logged in."); return; }

    const currentState = get().activity || createDefaultActivityState();
    let newTotalMinutes = currentState.totalMinutes + minutesToAdd;
    let newStreak = currentState.streak;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayISO = today.toISOString().split('T')[0];

    let lastActivityDateObj: Date | null = null;
    if (currentState.lastActivityDate) {
        lastActivityDateObj = new Date(currentState.lastActivityDate);
        lastActivityDateObj.setHours(0, 0, 0, 0);
    }
    
    if (lastActivityDateObj) {
        const diffTime = today.getTime() - lastActivityDateObj.getTime();
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            newStreak++;
        } else if (diffDays > 1) {
            newStreak = 1;
        }
    } else {
        newStreak = 1;
    }

    const newState: UserActivity = {
        ...currentState,
        totalMinutes: newTotalMinutes,
        streak: newStreak,
        lastActivityDate: todayISO,
        updatedAt: new Date().toISOString(),
    };
    set({ activity: newState });

    try {
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString();
        await setDoc(doc(db, 'users', uid, 'activity', 'main'), { encrypted });
    } catch (error) {
        console.error("Failed to persist activity time and streak:", error);
    }
  },

  addJournalEntry: async (uid, data) => {
    if (!uid) { console.error("Cannot add journal entry: user not logged in."); return; }
    
    const entryData: Omit<JournalEntry, 'id'> = { sessionId: data.sessionId, note: data.note, date: new Date().toISOString(), mood: data.mood || 'neutral' };
    const newPrivateDoc: PrivateDoc = { id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, data: { type: 'journal', ...entryData } };

    const currentPrivateDocs = get().privateData || [];
    const newPrivateDocs = [newPrivateDoc, ...currentPrivateDocs];
    set({ privateData: newPrivateDocs });

    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify({ docs: newPrivateDocs }), uid).toString();
      await setDoc(doc(db, 'users', uid, 'private', 'main'), { encrypted });
    } catch (error) { console.error("Failed to persist new journal entry:", error); }
  },

  updateJournalEntry: async (uid, entryId, updates) => {
    if (!uid) { console.error("Cannot update journal entry: user not logged in."); return; }
    
    const currentPrivateDocs = get().privateData || [];
    const newPrivateDocs = currentPrivateDocs.map(pDoc => {
      if (pDoc.id === entryId) {
        return { ...pDoc, data: { ...pDoc.data, ...updates, date: new Date().toISOString() } };
      }
      return pDoc;
    });

    set({ privateData: newPrivateDocs });

    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify({ docs: newPrivateDocs }), uid).toString();
      await setDoc(doc(db, 'users', uid, 'private', 'main'), { encrypted });
    } catch (error) { console.error("Failed to persist journal entry update:", error); }
  },
  
  deleteJournalEntry: async (uid, entryId) => {
    if (!uid) { console.error("Cannot delete journal entry: user not logged in."); return; }
    
    const currentPrivateDocs = get().privateData || [];
    const newPrivateDocs = currentPrivateDocs.filter(pDoc => pDoc.id !== entryId);

    set({ privateData: newPrivateDocs });
    
    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify({ docs: newPrivateDocs }), uid).toString();
      await setDoc(doc(db, 'users', uid, 'private', 'main'), { encrypted });
    } catch (error) { console.error("Failed to persist journal entry deletion:", error); }
  },

  toggleFavoriteSession: async (uid, sessionId) => {
    const activity = get().activity || createDefaultActivityState();
    const newFavorites = activity.favoriteSessions.includes(sessionId) ? activity.favoriteSessions.filter(id => id !== sessionId) : [...activity.favoriteSessions, sessionId];
    const newState = { ...activity, favoriteSessions: newFavorites, updatedAt: new Date().toISOString() };
    set({ activity: newState });
    try {
      await setDoc(doc(db, 'users', uid, 'activity', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString() });
    } catch (error) { console.error("Failed to persist favorite session update:", error); }
  },

  rateSession: async (uid, lang, category, sessionId, newRating) => {
    // This part is now complex because the script data is encrypted with a key the client doesn't have.
    // This would require a dedicated Cloud Function to update ratings securely.
    // For now, we will just update the user's activity log.
    console.warn("Session rating on the client is disabled for now. This should be moved to a Cloud Function.");
    
    const activity = get().activity || createDefaultActivityState();
    const newActivityState = { ...activity, ratedSessions: { ...activity.ratedSessions, [sessionId]: newRating }, updatedAt: new Date().toISOString() };
    set({ activity: newActivityState });
    
    try {
        await setDoc(doc(db, 'users', uid, 'activity', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newActivityState), uid).toString() });
    } catch(e) { console.error("Rating update failed: ", e); }
  },
  
  addCompletedSession: async (uid, sessionId) => {
    const currentState = get().activity || createDefaultActivityState();
    const newCompletedSessions = [...currentState.completedSessions, { id: sessionId, completedAt: new Date().toISOString() }];
    const newState = { ...currentState, completedSessions: newCompletedSessions, updatedAt: new Date().toISOString() };
    
    set({ activity: newState });
    try {
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString();
        await setDoc(doc(db, 'users', uid, 'activity', 'main'), { encrypted });
    } catch (error) { console.error("Failed to add completed session:", error); }
  },
  
  applyAdReward: async (uid, payload) => {
    const currentState = get().subscription || createDefaultSubscriptionState();
    const { itemId, timestamp, duration } = payload;
    const unlockEndTime = new Date(timestamp + duration).toISOString();
    const newState: SubscriptionState = { ...currentState, adUnlocks: { ...currentState.adUnlocks, unlockedItems: { [itemId]: unlockEndTime } }, updatedAt: new Date().toISOString() };
    set({ subscription: newState });
    try {
        await setDoc(doc(db, 'users', uid, 'subscription', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString() });
    } catch (error) { console.error("Failed to persist ad reward:", error); }
  },
  
  updateSubscription: async (uid, updates) => {
    const currentState = get().subscription || createDefaultSubscriptionState();
    const newState = { ...currentState, ...updates, updatedAt: new Date().toISOString() };
    newState.premiumFeatures = calculatePremiumFeatures(newState);
    set({ subscription: newState });
    try {
      await setDoc(doc(db, 'users', uid, 'subscription', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString() });
    } catch (error) { console.error("Failed to persist subscription update:", error); }
  },
  
  updateProfile: async (uid, data) => {
    const currentState = get().profile || createDefaultProfileState();
    const mergedData = { ...currentState, ...data, updatedAt: new Date().toISOString() };
    set({ profile: mergedData });
    try {
      await setDoc(doc(db, 'users', uid, 'profile', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(mergedData), uid).toString() });
    } catch (error) { console.error("Failed to persist profile update:", error); }
  },
  
  updatePreferences: async (uid, data) => {
    const currentState = get().preferences || createDefaultPreferencesState();
    const mergedData = { ...currentState, ...data, updatedAt: new Date().toISOString() };
    if (data.binauralConfig) mergedData.binauralConfig = { ...(currentState.binauralConfig || createDefaultBinauralConfig()), ...data.binauralConfig };
    if (data.musicConfig) mergedData.musicConfig = { ...(currentState.musicConfig || createDefaultMusicConfig()), ...data.musicConfig };
    if (data.ambientConfig) mergedData.ambientConfig = { ...(currentState.ambientConfig || createDefaultAmbientConfig()), ...data.ambientConfig };
    set({ preferences: mergedData });
    try {
      await setDoc(doc(db, 'users', uid, 'preferences', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(mergedData), uid).toString() });
    } catch (error) { console.error("Failed to persist preferences update:", error); }
  },
  
  addAudioAsset: async (uid, asset, type) => {
    const currentState = get().audioAssets || createDefaultAudioAssetsState();
    const newState = { ...currentState, [type]: [...currentState[type], asset], updatedAt: new Date().toISOString() };
    set({ audioAssets: newState });
    try {
      await setDoc(doc(db, 'users', uid, 'audioAssets', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString() });
    } catch (error) { console.error(`Failed to persist new audio asset:`, error); }
  },
  
  deleteAudioAsset: async (uid, assetId, type) => {
    const currentState = get().audioAssets || createDefaultAudioAssetsState();
    const newState = { ...currentState, [type]: currentState[type].filter(asset => asset.id !== assetId), updatedAt: new Date().toISOString() };
    set({ audioAssets: newState });
    try {
      await setDoc(doc(db, 'users', uid, 'audioAssets', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(newState), uid).toString() });
    } catch (error) { console.error(`Failed to persist audio asset deletion:`, error); }
  },

  getTTSAudioURL: async (text, lang, voiceName) => {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) { throw new Error("User not authenticated for TTS."); }

    try {
      const idToken = await user.getIdToken();
      const ttsFunctionUrl = `https://${functions.region}-piknowkyo-777.cloudfunctions.net/getGoogleTTSAudio`;

      const response = await fetch(ttsFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({ text, lang, voiceName }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`TTS API error: ${response.status} ${response.statusText} - ${errorData}`);
      }

      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);

    } catch (error: any) {
      console.error("Error fetching TTS audio stream:", error);
      throw new Error(`Failed to get TTS audio stream: ${error.message || 'Unknown error'}`);
    }
  },
}));

export type { BlogPost };
// Alias AppState to AppStore as expected by other modules
export type { AppState as AppStore };