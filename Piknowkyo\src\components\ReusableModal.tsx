// src/components/ReusableModal.tsx
import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { FiX } from 'react-icons/fi';

// --- Props for the Modal ---
interface ReusableModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  titleIcon?: ReactNode;
  children: ReactNode;
  footerContent?: ReactNode;
  isLoading?: boolean;
  maxWidth?: string;
  closeOnOverlayClick?: boolean;
}

// --- Styled Components for the Modal ---
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: ${({ $isOpen }) => $isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 2000; // FIX: Increased z-index to be on top of everything
  opacity: ${({ $isOpen }) => $isOpen ? 1 : 0};
  transition: opacity 0.3s ease-in-out;
`;

const ModalDialog = styled.div<{ maxWidth?: string }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: ${({ maxWidth }) => maxWidth || '600px'};
  width: 90%;
  color: ${({ theme }) => theme.text};
  position: relative;
  transform: scale(0.95);
  opacity: 0;
  animation: fadeInScale 0.3s forwards ease-out;
  
  display: flex;
  flex-direction: column;
  max-height: 90vh;

  @keyframes fadeInScale {
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  flex-shrink: 0;

  h3 {
    color: ${({ theme }) => theme.primary};
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const CloseButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.textMuted};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s;

  &:hover {
    color: ${({ theme }) => theme.text};
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
  line-height: 1.6;
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
  
  overflow-y: auto;
  flex-grow: 1;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.border};
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }

  p {
    margin-top: 0;
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.border};
  flex-shrink: 0;
`;

const ReusableModal: React.FC<ReusableModalProps> = ({
  isOpen,
  onClose,
  title,
  titleIcon,
  children,
  footerContent,
  isLoading = false,
  maxWidth,
  closeOnOverlayClick = true,
}) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = () => {
    if (closeOnOverlayClick && !isLoading) {
      onClose();
    }
  };

  return (
    <ModalOverlay $isOpen={isOpen} onClick={handleOverlayClick}>
      <ModalDialog maxWidth={maxWidth} onClick={(e) => e.stopPropagation()}>
        {(title || typeof onClose === 'function') && (
          <ModalHeader>
            {title && <h3>{titleIcon}{title}</h3>}
            {typeof onClose === 'function' && (
              <CloseButton onClick={onClose} disabled={isLoading} aria-label="Fermer la modale">
                <FiX />
              </CloseButton>
            )}
          </ModalHeader>
        )}
        <ModalBody>
          {children}
        </ModalBody>
        {footerContent && (
          <ModalFooter>
            {footerContent}
          </ModalFooter>
        )}
      </ModalDialog>
    </ModalOverlay>
  );
};

export default ReusableModal;