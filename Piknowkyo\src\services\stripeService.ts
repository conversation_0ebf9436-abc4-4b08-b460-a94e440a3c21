// //src/services/stripeService.ts
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getAuth } from 'firebase/auth';

let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY!);
  }
  return stripePromise;
};

export const createCheckoutSession = async (priceId: string) => {
  const auth = getAuth();
  const userId = auth.currentUser?.uid;
  if (!userId) throw new Error('User not authenticated');
  
  const functions = getFunctions();
  const createCheckoutSessionFunction = httpsCallable(functions, 'createCheckoutSession');
  const { data } = await createCheckoutSessionFunction({ priceId, userId });
  return data as { sessionId: string };
};

export const redirectToCheckout = async (sessionId: string) => {
  const stripe = await getStripe();
  if (!stripe) throw new Error('Stripe not initialized');
  
  const { error } = await stripe.redirectToCheckout({ sessionId });
  if (error) throw error;
};

export const updateSubscription = async (newPlan: string) => {
  const auth = getAuth();
  const userId = auth.currentUser?.uid;
  if (!userId) throw new Error('User not authenticated');
  
  const functions = getFunctions();
  const updateSubscriptionFunction = httpsCallable(functions, 'updateSubscription');
  await updateSubscriptionFunction({ newPlan, userId });
};

export const cancelSubscription = async () => {
  const auth = getAuth();
  const userId = auth.currentUser?.uid;
  if (!userId) throw new Error('User not authenticated');
  
  const functions = getFunctions();
  const cancelSubscriptionFunction = httpsCallable(functions, 'cancelSubscription');
  await cancelSubscriptionFunction({ userId });
};

export const getPaymentHistory = async () => {
  const auth = getAuth();
  const userId = auth.currentUser?.uid;
  if (!userId) throw new Error('User not authenticated');
  
  const functions = getFunctions();
  const getPaymentHistoryFunction = httpsCallable(functions, 'getPaymentHistory');
  const { data } = await getPaymentHistoryFunction({ userId });
  return data as Array<{
    id: string;
    amount: number;
    currency: string;
    description: string;
    date: string;
    status: string;
  }>;
};