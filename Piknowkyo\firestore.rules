rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- User-specific Data ---
    // This rule already covers our new `privateData` collection perfectly.
    // It allows a user to read and write ONLY their own documents.
    match /users/{userId}/{subcollection=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // --- NEW: Shared Encrypted Collection ---
    // This rule is for the data shared between all users.
    // Access is granted to any authenticated user because the data's confidentiality
    // is ensured by client-side symmetric encryption, not by Firestore rules.
    match /ExampleCollection/{docId} {
      allow read, write: if request.auth != null;
    }
    
    // --- Public App Translations ---
    // Allows any authenticated user to read a language document.
    match /AppLanguage/{lang} {
      allow read: if request.auth != null;
      allow write: if false; 
    }

    // --- Public Session Scripts ---
    match /SessionScripts/{lang} {
      allow read: if request.auth != null;
    }
    match /SessionScripts/{lang}/{category}/{scriptId} {
      allow read: if request.auth != null;
    }
    match /SessionScripts/{path=**} {
      allow write: if false;
    }
    
    // --- Public Leaderboard ---
    match /leaderboard/{entry} {
      allow read: if request.auth != null;
      allow write: if false;
    }
   
    // --- Blog Rules ---
    match /blogPosts/{postId} {
      // Anyone logged in can read.
      allow read: if request.auth != null;
      
      // The author can create a post. No one can update or delete.
      allow create: if request.auth != null && request.resource.data.authorId == request.auth.uid;
      allow update, delete: if false;
    }

    match /blogComments/{commentId} {
      // Anyone logged in can read comments.
      allow read: if request.auth != null;

      // The author can create a comment. No one can update or delete.
      allow create: if request.auth != null && request.resource.data.authorId == request.auth.uid;
      allow update, delete: if false;
    }


    // Deny all other access
    // This rule MUST be last. Any collection not matched above will be blocked.
    match /{document=**} {
      allow read, write: if false;
    }
  }
}