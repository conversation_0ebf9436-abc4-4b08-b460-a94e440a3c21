// src/components/AudioConfigPanel.tsx

import React, { useRef, useEffect, useMemo, useContext, useState } from 'react';
import styled, { DefaultTheme, css, ThemeContext } from 'styled-components';
import { useAppStore, SubscriptionState, AudioAsset, BinauralConfig, MusicConfig, AmbientConfig } from '../store/useAppStore';
import { FiInfo } from 'react-icons/fi'; // FiInfo is still used for the styled-component TooltipIcon
import { useTranslation } from 'react-i18next';
import PremiumGate from '../components/PremiumGate';
import { useAuth } from '../hooks/useAuth';
import { baseFrequencyPresetCategories, beatFrequencyPresetCategories, findPresetById, FrequencyPreset } from '../config/frequencyPresets';
import AppIcon from './AppIcon'; // Use AppIcon for consistency

// --- Styled Components ---
const PanelContainer = styled.div`
  & > * + * { margin-top: 1.5rem; }
`;

const ConfigSection = styled.div`
  position: relative;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 10px; padding: 1.2rem;
  border: 1px solid ${({ theme }) => theme.border};
`;

const ToggleSwitchContainer = styled.label`
  position: relative; display: inline-block;
  width: 48px; height: 24px; margin-left: auto;
`;
const ToggleInput = styled.input`
  opacity: 0; width: 0; height: 0;
  &:checked + .slider { background-color: ${({ theme }) => theme.primary}; }
  &:focus + .slider { box-shadow: 0 0 1px ${({ theme }) => theme.primary}; }
  &:checked + .slider:before { transform: translateX(22px); }
`;
const ToggleSlider = styled.span`
  position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0;
  background-color: ${({ theme }) => theme.disabled || '#ccc'};
  transition: .4s; border-radius: 24px;
  &:before {
    position: absolute; content: ""; height: 18px; width: 18px;
    left: 3px; bottom: 3px; background-color: white;
    transition: .4s; border-radius: 50%;
  }
`;
const SectionTitleContainer = styled.div`
  display: flex; align-items: center; justify-content: space-between; margin-bottom: 1rem;
  h4 {
    font-size: 1.2rem; color: ${({ theme }) => theme.primary}; margin: 0;
    display: flex; align-items: center; gap: 0.5rem;
  }
`;
const TooltipWrapper = styled.div`
  position: relative; display: inline-flex;
  align-items: center; margin-left: 0.5rem;
`;
const TooltipIcon = styled(FiInfo)<{ $isActive?: boolean }>`
  color: ${({ theme, $isActive }) => $isActive ? theme.primary : theme.textMuted};
  cursor: help; font-size: 1rem; transition: color 0.2s; outline: none;
  &:hover + span, &:focus + span, &.active-tooltip + span { 
    visibility: visible; opacity: 1; z-index: 30;
    transition: opacity 0.3s, visibility 0s linear 0s;
  }
`;
const TooltipText = styled.span<{ $show?: boolean }>`
  visibility: hidden; width: 230px;
  background-color: ${({ theme }) => theme.surface}; color: ${({ theme }) => theme.text};
  text-align: left; font-size: 0.85rem; font-weight: normal; line-height: 1.4;
  border-radius: 6px; padding: 0.8rem; position: absolute; z-index: 20;
  bottom: 140%; left: 50%; transform: translateX(-50%);
  opacity: 0; transition: opacity 0.3s ease-in-out, visibility 0s linear 0.3s; 
  box-shadow: 0 2px 10px rgba(0,0,0,0.15); border: 1px solid ${({ theme }) => theme.border};
  &::after { 
    content: ""; position: absolute; top: 100%; left: 50%; margin-left: -5px;
    border-width: 5px; border-style: solid;
    border-color: ${({ theme }) => theme.surface} transparent transparent transparent;
  }
  ${({ $show }) => $show && css`
      visibility: visible; opacity: 1; transition-delay: 0s; z-index: 30;
  `}
`;
const InputGroup = styled.div`
  margin-bottom: 1.2rem; &:last-child { margin-bottom: 0; }
  label {
    display: block; margin-bottom: 0.4rem; font-weight: 500;
    color: ${({ theme }) => theme.text}; font-size: 0.95rem;
  }
  input[type="range"] { width: 100%; margin-bottom: 0.2rem; }
  select {
    width: 100%; padding: 0.7rem; border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border || '#ddd'};
    background: ${({ theme }) => theme.inputBackground || '#fff'}; color: ${({ theme }) => theme.text};
    margin-top: 0.3rem; font-size: 0.95rem; appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23${({ theme }) => (theme.textSecondary || '6c757d').substring(1)}%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat; background-position: right 0.7em top 50%, 0 0;
    background-size: 0.65em auto, 100%; padding-right: 2.5em;
  }
  .description-text { 
    font-size: 0.85rem; color: ${({ theme }) => theme.textSecondary}; margin-top: 0.5rem; min-height: 3em; 
  }
`;

const AssetSelectionWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  select {
    flex-grow: 1;
    margin-top: 0;
  }

  button {
    flex-shrink: 0;
    padding: 0.6rem;
    height: 44px;
    width: 44px;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.primary};
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
`;

const Button = styled.button`
  background: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight || '#fff'}; 
  border: none; border-radius: 8px; padding: 0.7rem 1.5rem; font-size: 1rem;
  font-weight: 500; cursor: pointer;
  transition: background-color 0.2s ease-in-out, transform 0.1s ease;
  display: inline-flex; align-items: center; gap: 0.5rem;
  &:hover { opacity: 0.9; } &:active { transform: scale(0.98); }
  &:disabled {
    background: ${({ theme }) => theme.disabled || '#ccc'};
    color: ${({ theme }) => theme.textMuted || '#666'}; cursor: not-allowed;
  }
`;

// --- Helper Components ---
const findPresetByValue = (categories: typeof baseFrequencyPresetCategories, value: number, precision = 0.1): FrequencyPreset | null => {
    for (const category of categories) {
        const found = category.presets.find(p => Math.abs(p.value - value) < precision);
        if (found) return found;
    }
    return null;
};
interface AudioManifestItem { id: string; name: string; url: string; isUserUploaded: boolean; }
interface AudioManifest { musics: AudioManifestItem[]; ambiants: AudioManifestItem[]; }
interface SectionTitleWithToggleProps {
  icon: React.ReactNode; title: string; tooltipText: string; tooltipId: string;
  isChecked: boolean; onToggle: (checked: boolean) => void; isDisabled?: boolean;
}
const SectionHeader: React.FC<SectionTitleWithToggleProps> = ({ icon, title, tooltipText, tooltipId, isChecked, onToggle, isDisabled = false }) => (
  <SectionTitleContainer>
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <h4>{icon} {title}</h4> <InfoTooltip id={tooltipId} text={tooltipText} />
    </div>
    <ToggleSwitchContainer htmlFor={`toggle-${tooltipId}`}>
      <ToggleInput id={`toggle-${tooltipId}`} type="checkbox" checked={isChecked} onChange={(e) => onToggle(e.target.checked)} disabled={isDisabled} aria-label={`Enable/Disable ${title}`} />
      <ToggleSlider className="slider" />
    </ToggleSwitchContainer>
  </SectionTitleContainer>
);
interface AudioConfigPanelProps { userMusicAssets: AudioAsset[]; userAmbientAssets: AudioAsset[]; subscription: SubscriptionState | null; }
const InfoTooltip: React.FC<{ text: string; id: string }> = ({ text, id }) => {
    const [showTooltipState, setShowTooltipState] = React.useState(false);
    const tooltipRef = React.useRef<HTMLDivElement>(null);
    const handleClick = (event: React.MouseEvent | React.KeyboardEvent) => { event.stopPropagation(); setShowTooltipState(prev => !prev); };
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => { if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) { setShowTooltipState(false); } };
        if (showTooltipState) { document.addEventListener('mousedown', handleClickOutside); } else { document.removeEventListener('mousedown', handleClickOutside); }
        return () => { document.removeEventListener('mousedown', handleClickOutside); };
    }, [showTooltipState]);
    return (
        <TooltipWrapper ref={tooltipRef}>
            <TooltipIcon onClick={handleClick} onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); handleClick(e); }}} $isActive={showTooltipState} aria-describedby={`tooltip-text-${id}`} tabIndex={0} />
            <TooltipText id={`tooltip-text-${id}`} role="tooltip" $show={showTooltipState}>{text}</TooltipText>
        </TooltipWrapper>
    );
};

// --- Main Component ---
const AudioConfigPanel: React.FC<AudioConfigPanelProps> = ({ userMusicAssets, userAmbientAssets, subscription }) => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const { user } = useAuth();
  const preferences = useAppStore((state) => state.preferences);
  const updatePreferences = useAppStore((state) => state.updatePreferences);
  
  const [baseFreqDesc, setBaseFreqDesc] = useState<string>('');
  const [beatFreqDesc, setBeatFreqDesc] = useState<string>('');
  const [playingPreview, setPlayingPreview] = useState<string | null>(null);
  const previewAudioRef = useRef<HTMLAudioElement | null>(null);

  const musicPrefs = preferences?.musicConfig;
  const ambientPrefs = preferences?.ambientConfig;
  const binauralPrefs = preferences?.binauralConfig;

  useEffect(() => {
    if (user?.uid && preferences && subscription) {
      let changed = false;
      const newPreferences = {
        musicConfig: { ...preferences.musicConfig },
        ambientConfig: { ...preferences.ambientConfig },
        binauralConfig: { ...preferences.binauralConfig },
      };
      if (!subscription.premiumFeatures.ambientSounds && newPreferences.ambientConfig.enabled) {
        newPreferences.ambientConfig.enabled = false; changed = true;
      }
      if (!subscription.premiumFeatures.binauralBeats && newPreferences.binauralConfig.enabled) {
        newPreferences.binauralConfig.enabled = false; changed = true;
      }
      if (changed) { updatePreferences(user.uid, { musicConfig: newPreferences.musicConfig, ambientConfig: newPreferences.ambientConfig, binauralConfig: newPreferences.binauralConfig }); }
    }
  }, [subscription, user?.uid, preferences, updatePreferences]);

  useEffect(() => {
    let desc = '';
    if (binauralPrefs?.enabled) {
      const presetFromId = findPresetById(baseFrequencyPresetCategories, binauralPrefs.baseFrequencyPresetId);
      if (presetFromId) desc = t(presetFromId.descriptionKey, presetFromId.id);
      else {
        const presetFromValue = findPresetByValue(baseFrequencyPresetCategories, binauralPrefs.baseFrequency);
        if (presetFromValue) desc = t(presetFromValue.descriptionKey, presetFromValue.id);
        else desc = t('audioConfig.customFrequencyDescPlaceholder');
      }
    }
    setBaseFreqDesc(desc);
  }, [binauralPrefs?.enabled, binauralPrefs?.baseFrequency, binauralPrefs?.baseFrequencyPresetId, t]);

  useEffect(() => {
    let desc = '';
    if (binauralPrefs?.enabled) {
      const presetFromId = findPresetById(beatFrequencyPresetCategories, binauralPrefs.beatFrequencyPresetId);
      if (presetFromId) desc = t(presetFromId.descriptionKey, presetFromId.id);
      else {
        const presetFromValue = findPresetByValue(beatFrequencyPresetCategories, binauralPrefs.beatFrequency, 0.05);
        if (presetFromValue) desc = t(presetFromValue.descriptionKey, presetFromValue.id);
        else desc = t('audioConfig.customFrequencyDescPlaceholder');
      }
    }
    setBeatFreqDesc(desc);
  }, [binauralPrefs?.enabled, binauralPrefs?.beatFrequency, binauralPrefs?.beatFrequencyPresetId, t]);

  const handleBinauralConfigChange = (updates: Partial<BinauralConfig>) => {
      if (user?.uid && preferences) {
          const newConfig = { ...preferences.binauralConfig, ...updates };
          if ('baseFrequencyPresetId' in updates && updates.baseFrequencyPresetId) { const preset = findPresetById(baseFrequencyPresetCategories, updates.baseFrequencyPresetId); if (preset) newConfig.baseFrequency = preset.value; }
          if ('beatFrequencyPresetId' in updates && updates.beatFrequencyPresetId) { const preset = findPresetById(beatFrequencyPresetCategories, updates.beatFrequencyPresetId); if (preset) newConfig.beatFrequency = preset.value; }
          if('baseFrequency' in updates && !('baseFrequencyPresetId' in updates)) newConfig.baseFrequencyPresetId = null;
          if('beatFrequency' in updates && !('beatFrequencyPresetId' in updates)) newConfig.beatFrequencyPresetId = null;
          updatePreferences(user.uid, { binauralConfig: newConfig });
      }
  };
  const handleMusicConfigChange = (updates: Partial<MusicConfig>) => {
    if (user?.uid && preferences) {
        updatePreferences(user.uid, { musicConfig: { ...preferences.musicConfig, ...updates } });
    }
  };
  const handleAmbientConfigChange = (updates: Partial<AmbientConfig>) => {
    if (user?.uid && preferences) {
        updatePreferences(user.uid, { ambientConfig: { ...preferences.ambientConfig, ...updates } });
    }
  };

  const [musicManifestOptions, setMusicManifestOptions] = React.useState<AudioManifestItem[]>([]);
  const [ambientManifestOptions, setAmbientManifestOptions] = React.useState<AudioManifestItem[]>([]);
  
  useEffect(() => {
    fetch('/assets/audio_manifests/audio_manifest.json').then(response => response.ok ? response.json() : Promise.reject(response))
      .then((data: AudioManifest) => {
        setMusicManifestOptions([{ id: 'none-music', name: t('audioConfig.music.none', 'None'), url: '', isUserUploaded: false }, ...data.musics, ...userMusicAssets]);
        setAmbientManifestOptions([{ id: 'none-ambient', name: t('audioConfig.ambient.none', 'None'), url: '', isUserUploaded: false }, ...data.ambiants, ...userAmbientAssets]);
      }).catch(() => {
        setMusicManifestOptions([{ id: 'none-music', name: t('audioConfig.music.none', 'None'), url: '', isUserUploaded: false }, ...userMusicAssets]);
        setAmbientManifestOptions([{ id: 'none-ambient', name: t('audioConfig.ambient.none', 'None'), url: '', isUserUploaded: false }, ...userAmbientAssets]);
      });
  }, [t, userMusicAssets, userAmbientAssets]);

  const musicOptions = useMemo(() => musicManifestOptions.map(item => ({ label: item.name, value: item.url, id: item.id })), [musicManifestOptions]);
  const ambientOptions = useMemo(() => ambientManifestOptions.map(item => ({ label: item.name, value: item.url, id: item.id })), [ambientManifestOptions]);

  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});
  const [binauralPlaying, setBinauralPlaying] = React.useState(false);
    
  const stopBinauralSound = () => {
    if (audioCtxRef.current) {
        try {
            binauralOscs.current.left?.stop();
            binauralOscs.current.right?.stop();
            if (audioCtxRef.current.state !== 'closed') audioCtxRef.current.close().catch(()=>{});
        } catch (e) {}
        audioCtxRef.current = null; binauralOscs.current = {};
    }
    setBinauralPlaying(false);
  };
    
  const handlePreview = (type: 'music' | 'ambient') => {
    const config = type === 'music' ? musicPrefs : ambientPrefs;
    if (!config || !config.url) return;
    const { url, volume } = config;

    if (previewAudioRef.current && playingPreview === url) {
      previewAudioRef.current.pause();
      return;
    }
    
    if (previewAudioRef.current) {
      previewAudioRef.current.pause();
    }
    
    const audio = new Audio(url);
    audio.volume = volume;
    previewAudioRef.current = audio;
    setPlayingPreview(url);

    audio.play().catch(() => setPlayingPreview(null));

    const onEnd = () => {
      if (previewAudioRef.current === audio) {
        setPlayingPreview(null);
        previewAudioRef.current = null;
      }
    };
    audio.addEventListener('ended', onEnd);
    audio.addEventListener('pause', onEnd);
  };
  
  useEffect(() => {
    if (previewAudioRef.current && playingPreview) {
      if (musicPrefs && playingPreview === musicPrefs.url) {
        previewAudioRef.current.volume = musicPrefs.volume;
      }
      if (ambientPrefs && playingPreview === ambientPrefs.url) {
        previewAudioRef.current.volume = ambientPrefs.volume;
      }
    }
  }, [musicPrefs?.volume, ambientPrefs?.volume, playingPreview]);

  useEffect(() => { 
    return () => {
      stopBinauralSound();
      if (previewAudioRef.current) previewAudioRef.current.pause();
    }; 
  }, []);

  if (!preferences || !musicPrefs || !ambientPrefs || !binauralPrefs) {
    return <div>{t('loading.audioConfig', 'Loading audio settings...')}</div>;
  }
  
  const handleTestBinaural = () => {
    if (binauralPlaying) { stopBinauralSound(); return; }
    if (!binauralPrefs) return;
    const { volume, baseFrequency, beatFrequency } = binauralPrefs;
    const Ctx = window.AudioContext || (window as any).webkitAudioContext;
    if (!Ctx) return alert(t('audioConfig.webAudioNotSupported'));
    const newCtx = new Ctx(); audioCtxRef.current = newCtx;
    const gainNode = newCtx.createGain(); gainNode.gain.setValueAtTime(volume, newCtx.currentTime); gainNode.connect(newCtx.destination);
    const left = newCtx.createOscillator(); const right = newCtx.createOscillator();
    left.type = right.type = 'sine';
    left.frequency.setValueAtTime(baseFrequency, newCtx.currentTime);
    right.frequency.setValueAtTime(baseFrequency + beatFrequency, newCtx.currentTime);
    const merger = newCtx.createChannelMerger(2);
    left.connect(merger, 0, 0); right.connect(merger, 0, 1);
    merger.connect(gainNode); left.start(); right.start();
    binauralOscs.current = { left, right, gain: gainNode };
    setBinauralPlaying(true);
  };

  return (
    <PanelContainer>
      <ConfigSection>
        <SectionHeader icon={<AppIcon name="music" size={24}/>} title={t('audioConfig.musicTitle')} tooltipText={t('audioConfig.musicTooltip')} tooltipId="music" isChecked={!!musicPrefs.enabled} onToggle={(c) => handleMusicConfigChange({ enabled: c })} />
        {musicPrefs.enabled && (
            <>
                <InputGroup>
                  <label htmlFor="music-select">{t('audioConfig.musicTrack')}</label>
                  <AssetSelectionWrapper>
                    <select id="music-select" value={musicPrefs.url} onChange={(e) => handleMusicConfigChange({ url: e.target.value })}>
                        {musicOptions.map(opt => <option key={opt.id} value={opt.value}>{opt.label}</option>)}
                    </select>
                    <button onClick={() => handlePreview('music')} disabled={!musicPrefs.url} title={playingPreview === musicPrefs.url ? t('actions.stopPreview') : t('actions.previewSound')}>
                      {playingPreview === musicPrefs.url ? <AppIcon name="pause" size={20} /> : <AppIcon name="play" size={20} />}
                    </button>
                  </AssetSelectionWrapper>
                </InputGroup>
                <InputGroup>
                  <label htmlFor="music-volume">{t('audioConfig.volume')} : {Math.round(musicPrefs.volume * 100)}%</label>
                  <input id="music-volume" type="range" min={0} max={1} step={0.01} value={musicPrefs.volume} onChange={e => handleMusicConfigChange({ volume: Number(e.target.value) })} />
                </InputGroup>
            </>
        )}
      </ConfigSection>

      <PremiumGate feature="ambientSounds" context="audio-asset" viewMode="list" subscription={subscription} title={t('audioConfig.ambientTitle')}>
        <ConfigSection>
          <SectionHeader icon={<AppIcon name="radio" size={24}/>} title={t('audioConfig.ambientTitle')} tooltipText={t('audioConfig.ambientTooltip')} tooltipId="ambient" 
            isChecked={!!ambientPrefs.enabled} 
            onToggle={(c) => handleAmbientConfigChange({ enabled: c })} 
            isDisabled={!subscription?.premiumFeatures?.ambientSounds}
          />
          {ambientPrefs.enabled && (
              <>
                  <InputGroup>
                    <label htmlFor="ambient-select">{t('audioConfig.ambientSound')}</label>
                    <AssetSelectionWrapper>
                      <select id="ambient-select" value={ambientPrefs.url} onChange={(e) => handleAmbientConfigChange({ url: e.target.value })}>
                          {ambientOptions.map(opt => <option key={opt.id} value={opt.value}>{opt.label}</option>)}
                      </select>
                      <button onClick={() => handlePreview('ambient')} disabled={!ambientPrefs.url} title={playingPreview === ambientPrefs.url ? t('actions.stopPreview') : t('actions.previewSound')}>
                        {playingPreview === ambientPrefs.url ? <AppIcon name="pause" size={20} /> : <AppIcon name="play" size={20} />}
                      </button>
                    </AssetSelectionWrapper>
                  </InputGroup>
                  <InputGroup>
                    <label htmlFor="ambient-volume">{t('audioConfig.volume')} : {Math.round(ambientPrefs.volume * 100)}%</label>
                    <input id="ambient-volume" type="range" min={0} max={1} step={0.01} value={ambientPrefs.volume} onChange={e => handleAmbientConfigChange({ volume: Number(e.target.value) })} />
                  </InputGroup>
              </>
          )}
        </ConfigSection>
      </PremiumGate>
      
      <PremiumGate feature="binauralBeats" context="audio-asset" viewMode="list" subscription={subscription} title={t('audioConfig.binauralBeats')}>
        <ConfigSection>
          <SectionHeader icon={<AppIcon name="volume-2" size={24}/>} title={t('audioConfig.binauralBeats')} tooltipText={t('audioConfig.binauralTooltip')} tooltipId="binaural" 
            isChecked={!!binauralPrefs.enabled} 
            onToggle={(c) => handleBinauralConfigChange({ enabled: c })}
            isDisabled={!subscription?.premiumFeatures?.binauralBeats}
          />
          {binauralPrefs.enabled && (
              <>
                  <InputGroup>
                    <label htmlFor="binaural-base-freq-preset">{t('audioConfig.baseFrequencyPreset')}</label>
                    <select id="binaural-base-freq-preset" value={binauralPrefs.baseFrequencyPresetId || 'custom'} onChange={e => handleBinauralConfigChange({ baseFrequencyPresetId: e.target.value === 'custom' ? null : e.target.value })}>
                        <option value="custom">{t('audioConfig.customOption', 'Custom')}</option>
                        {baseFrequencyPresetCategories.map(category => ( <optgroup label={t(category.categoryLabelKey, category.categoryId)} key={category.categoryId}> {category.presets.map(preset => <option key={preset.id} value={preset.id}>{t(preset.labelKey, `${preset.value} Hz`)}</option>)} </optgroup> ))}
                    </select>
                    <p className="description-text">{baseFreqDesc || t('audioConfig.selectPresetToSeeDescription')}</p>
                    <label htmlFor="base-freq-slider" style={{marginTop: '0.5rem'}}>{t('audioConfig.baseFrequency')} : {binauralPrefs.baseFrequency.toFixed(1)} Hz</label>
                    <input id="base-freq-slider" type="range" min={30} max={1000} step={0.1} value={binauralPrefs.baseFrequency} onChange={e => handleBinauralConfigChange({ baseFrequency: Number(e.target.value) })} />
                  </InputGroup>
                  
                  <InputGroup>
                    <label htmlFor="binaural-beat-freq-preset">{t('audioConfig.beatFrequencyPreset')}</label>
                    <select id="binaural-beat-freq-preset" value={binauralPrefs.beatFrequencyPresetId || 'custom'} onChange={e => handleBinauralConfigChange({ beatFrequencyPresetId: e.target.value === 'custom' ? null : e.target.value })}>
                        <option value="custom">{t('audioConfig.customOption', 'Custom')}</option>
                        {beatFrequencyPresetCategories.map(category => ( <optgroup label={t(category.categoryLabelKey, category.categoryId)} key={category.categoryId}> {category.presets.map(preset => <option key={preset.id} value={preset.id}>{t(preset.labelKey, `${preset.value} Hz`)}</option>)} </optgroup> ))}
                    </select>
                    <p className="description-text">{beatFreqDesc || t('audioConfig.selectPresetToSeeDescription')}</p>
                    <label htmlFor="beat-freq-slider" style={{marginTop: '0.5rem'}}>{t('audioConfig.beatFrequency')} : {binauralPrefs.beatFrequency.toFixed(1)} Hz</label>
                    <input id="beat-freq-slider" type="range" min={0.5} max={50} step={0.1} value={binauralPrefs.beatFrequency} onChange={e => handleBinauralConfigChange({ beatFrequency: Number(e.target.value) })} />
                  </InputGroup>

                  <InputGroup>
                      <label htmlFor="binaural-volume">{t('audioConfig.binauralBeatsSetup.volume')} : {Math.round((binauralPrefs.volume) * 100)}%</label>
                      <input id="binaural-volume" type="range" min={0} max={1} step={0.01} value={binauralPrefs.volume} onChange={e => handleBinauralConfigChange({ volume: Number(e.target.value) })} />
                  </InputGroup>

                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <p style={{display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.9rem', margin: 0}}> <AppIcon name="headphones" /> {t('audioConfig.headphonesRequired')} </p>
                    <Button onClick={handleTestBinaural} style={{background: binauralPlaying ? theme.errorColor || '#e74c3c' : undefined}}>
                      {binauralPlaying ? <><AppIcon name="pause" size={16} /> {t('actions.stopTest')}</> : <><AppIcon name="play" size={16} /> {t('actions.testSound')}</>}
                    </Button>
                  </div>
              </>
          )}
        </ConfigSection>
      </PremiumGate>
    </PanelContainer>
  );
};

export default AudioConfigPanel;