// src/services/syncService.ts
import { Timestamp, doc, setDoc } from 'firebase/firestore';
import { SyncResult } from '../classes/interfaces/syncInterfaces';
import { JournalEntry } from '../models'; // Ensure JournalEntry has 'id' if needed for proper sync logic
import { useAppStore } from '../store/useAppStore';
import { db } from '../firebase';
import CryptoJS from 'crypto-js';
import { v4 as uuidv4 } from 'uuid';

// Define an extended JournalEntry type that includes the PrivateDoc's ID for retrieval
// Assuming JournalEntry from ../models does NOT have an 'id' property directly.
interface JournalEntryWithId extends JournalEntry {
    id: string;
}

// Main synchronization service
export class SyncService {
  // Helper to get the user's encryption key (assuming UID is the key)
  private static getUserEncryptionKey(uid: string): string {
    return uid;
  }

  static async performFullSync(uid: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      conflicts: [],
      errors: [],
    };

    const { setSyncStatus } = useAppStore.getState();
    setSyncStatus('syncing');

    try {
      // TODO: Implement full synchronization with Zustand
      // This section would be responsible for comparing local data (Zustand)
      // with remote data (Firebase) and resolving conflicts.
      // Currently, save/load operations are handled by store actions and onSnapshot listeners.
      // This "FullSync" would be for more complex scenarios like data recovery
      // after extended offline periods or migration.

      console.log('Full sync placeholder executed. No data currently synchronized in this method.');
      // Example of setting status:
      // const { preferences } = useAppStore.getState();
      // await setDoc(doc(db, 'users', uid, 'preferences', 'main'), { encrypted: CryptoJS.AES.encrypt(JSON.stringify(preferences), uid).toString() });
      // result.syncedCount++;

      setSyncStatus('synced');
    } catch (error: any) {
      console.error('Error during full sync:', error);
      setSyncStatus('error');
      result.success = false;
      result.errors.push(error.message);
    }

    return result;
  }

  /**
   * Replaces ALL journal entries for the user on Firebase with the provided list.
   * This is a destructive operation for existing journal entries if 'entries'
   * does not contain them, as new IDs are generated for incoming entries.
   * Other private data types (non-journal) are preserved.
   * @param uid The user's unique ID.
   * @param entries The complete list of journal entries to save.
   */
  static async syncJournal(uid: string, entries: JournalEntry[]): Promise<void> {
    if (!uid) {
        console.error("UID is required for syncJournal.");
        throw new Error("User not authenticated for journal sync.");
    }
    const { setSyncStatus, privateData } = useAppStore.getState();
    setSyncStatus('syncing');

    try {
        // 1. Filter out existing journal entries from privateData to preserve other types
        const nonJournalPrivateDocs = privateData.filter(doc => doc.data.type !== 'journal');

        // 2. Create new PrivateDoc objects for each incoming JournalEntry
        // IMPORTANT: This generates new IDs, effectively replacing existing journal entries
        // if this function is called with data that previously existed but without its original ID.
        const newJournalPrivateDocs = entries.map(entry => {
            // JournalEntry from ../models is assumed not to have 'id', so we generate one.
            const id = uuidv4();
            return { id, data: { type: 'journal', ...entry } };
        });

        // 3. Combine non-journal docs with new journal docs
        const updatedPrivateData = [...nonJournalPrivateDocs, ...newJournalPrivateDocs];

        // 4. Encrypt and save the entire privateData document to Firebase
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify({ docs: updatedPrivateData }), SyncService.getUserEncryptionKey(uid)).toString();
        await setDoc(doc(db, 'users', uid, 'private', 'main'), { encrypted });

        setSyncStatus('synced');
    } catch (error: any) {
        console.error("Error during journal sync:", error);
        setSyncStatus('error');
        throw new Error(error.message);
    }
  }

  /**
   * Retrieves current journal entries from the Zustand store's privateData.
   * @returns A promise resolving to an array of JournalEntryWithId.
   */
  static async getJournalEntries(): Promise<JournalEntryWithId[]> {
    const { privateData } = useAppStore.getState();
    return privateData
      .filter(doc => doc.data.type === 'journal')
      .map(doc => {
        // Map PrivateDoc to JournalEntryWithId, including the 'id' from PrivateDoc
        const { id, data } = doc;
        const { type, ...journalData } = data; // Destructure 'type' out of data
        return { id, ...journalData } as JournalEntryWithId;
      });
  }

  static detectConflicts(localData: any, remoteData: any): boolean {
    // Simple conflict detection logic based on timestamps
    if (!localData.updatedAt || !remoteData.updatedAt) {
      return false;
    }

    const localTime = localData.updatedAt instanceof Timestamp
      ? localData.updatedAt.toMillis()
      : new Date(localData.updatedAt).getTime();

    const remoteTime = remoteData.updatedAt instanceof Timestamp
      ? remoteData.updatedAt.toMillis()
      : new Date(remoteData.updatedAt).getTime();

    // Conflict if both were modified within the last 5 minutes
    const timeDiff = Math.abs(localTime - remoteTime);
    return timeDiff < 5 * 60 * 1000; // 5 minutes
  }
}