import styled from 'styled-components';

interface ButtonProps {
  $variant?: 'primary' | 'secondary' | 'outline' | 'text';
  $size?: 'small' | 'medium' | 'large';
  $fullWidth?: boolean;
}

const getBackgroundColor = (variant: string, theme: any) => {
  switch (variant) {
    case 'primary':
      return theme.primary;
    case 'secondary':
      return theme.secondary;
    case 'outline':
    case 'text':
      return 'transparent';
    default:
      return theme.primary;
  }
};

const getColor = (variant: string, theme: any) => {
  switch (variant) {
    case 'primary':
      return '#ffffff';
    case 'secondary':
      return '#ffffff';
    case 'outline':
      return theme.primary;
    case 'text':
      return theme.primary;
    default:
      return '#ffffff';
  }
};

const getBorder = (variant: string, theme: any) => {
  switch (variant) {
    case 'outline':
      return `2px solid ${theme.primary}`;
    default:
      return 'none';
  }
};

const getPadding = (size: string) => {
  switch (size) {
    case 'small':
      return '0.5rem 1rem';
    case 'medium':
      return '0.75rem 1.5rem';
    case 'large':
      return '1rem 2rem';
    default:
      return '0.75rem 1.5rem';
  }
};

const getFontSize = (size: string) => {
  switch (size) {
    case 'small':
      return '0.875rem';
    case 'medium':
      return '1rem';
    case 'large':
      return '1.125rem';
    default:
      return '1rem';
  }
};

const Button = styled.button.withConfig({ 
  shouldForwardProp: (prop) => !['$variant', '$size', '$fullWidth'].includes(prop) 
})<ButtonProps>`
  background: ${({ theme, $variant = 'primary' }) => getBackgroundColor($variant, theme)};
  color: ${({ theme, $variant = 'primary' }) => getColor($variant, theme)};
  border: ${({ theme, $variant = 'primary' }) => getBorder($variant, theme)};
  border-radius: 8px;
  padding: ${({ $size = 'medium' }) => getPadding($size)};
  font-size: ${({ $size = 'medium' }) => getFontSize($size)};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: ${({ $fullWidth }) => $fullWidth ? '100%' : 'auto'};
  font-family: 'Poppins', sans-serif;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: none;
  }
  
  &:disabled {
    background: ${({ theme }) => theme.border};
    color: ${({ theme }) => theme.textSecondary};
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
  }
  
  svg {
    margin-right: ${({ children }) => children ? '0.5rem' : '0'};
  }
`;

export default Button;
