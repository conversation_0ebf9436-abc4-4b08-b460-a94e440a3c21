// src/components/QuickAccessSettingsModal.tsx

import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore, QuickAccessLink, SessionScript } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import AppIcon from './AppIcon';
import Button from './ui/Button';
import { GAMES_LIST, GameInfoForList } from '../games/gameData'; // Import GAMES_LIST

// --- STYLED COMPONENTS ---

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed; top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0, 0, 0, 0.6); z-index: 1000;
  display: flex; align-items: center; justify-content: center;
  opacity: ${({ $isOpen }) => ($isOpen ? 1 : 0)};
  visibility: ${({ $isOpen }) => ($isOpen ? 'visible' : 'hidden')};
  transition: opacity 0.3s ease, visibility 0.3s ease;
`;

const ModalContainer = styled.div<{ $isOpen: boolean }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  width: 90%; max-width: 600px; max-height: 85vh;
  padding: 1.5rem; display: flex; flex-direction: column;
  transform: ${({ $isOpen }) => ($isOpen ? 'scale(1)' : 'scale(0.95)')};
  transition: transform 0.3s ease;
  @media (min-width: 768px) { padding: 2rem; }
`;

const ModalHeader = styled.div`
  display: flex; justify-content: space-between; align-items: center;
  margin-bottom: 1.5rem;
  h3 { font-size: 1.5rem; color: ${({ theme }) => theme.primary}; margin: 0; }
`;

const CloseButton = styled.button`
  background: none; border: none; color: ${({ theme }) => theme.textMuted};
  cursor: pointer; padding: 0.5rem; display: flex;
  &:hover { color: ${({ theme }) => theme.primary}; }
`;

const Content = styled.div`
  overflow-y: auto; padding-right: 1rem;
  margin-right: -1rem;
`;

const LinksGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  @media (min-width: 768px) { grid-template-columns: 1fr 1fr; }
`;

const Section = styled.div`
  margin-bottom: 1.5rem;
  h4 {
    font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.8px;
    color: ${({ theme }) => theme.textSecondary}; margin-bottom: 1rem;
    padding-left: 0.5rem;
  }
`;

const BaseItem = styled.div`
  display: flex; align-items: center; padding: 0.75rem;
  border-radius: 8px; background: ${({ theme }) => theme.surfaceAlt};
  cursor: pointer; transition: background-color 0.2s;
  &:hover { background: ${({ theme }) => theme.inputBackground}; }
  
  .icon { margin-right: 1rem; color: ${({ theme }) => theme.primary}; flex-shrink: 0; }
  span { font-weight: 500; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
  input[type="checkbox"] {
    width: 20px; height: 20px; margin-right: 1rem; flex-shrink: 0;
    accent-color: ${({ theme }) => theme.primary};
  }
`;

const AccordionContent = styled.div`
  padding: 1rem 0 0.5rem 1rem;
  border-left: 2px solid ${({ theme }) => theme.border};
  margin: 0.75rem 0 0.75rem 1.25rem;
  display: flex; flex-direction: column; gap: 0.75rem;
`;

const SearchContainer = styled.div`
  position: relative; margin-bottom: 0.5rem;
  .search-icon { position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: ${({ theme }) => theme.textMuted}; }
`;

const SearchInput = styled.input`
  width: 100%; padding: 0.6rem 0.6rem 0.6rem 2.2rem;
  border: 1px solid ${({ theme }) => theme.border}; background: ${({ theme }) => theme.inputBackground};
  border-radius: 6px; color: ${({ theme }) => theme.text};
`;

const AccordionChevron = styled.div<{ $isOpen: boolean }>`
  margin-left: auto;
  transition: transform 0.2s;
  transform: rotate(${({ $isOpen }) => ($isOpen ? '180deg' : '0deg')});
  display: flex;
  align-items: center;
`;

const ModalFooter = styled.div`
  display: flex; justify-content: flex-end; gap: 1rem;
  margin-top: 1.5rem; padding-top: 1.5rem;
  border-top: 1px solid ${({ theme }) => theme.border};
`;

// --- DATA DEFINITIONS ---
// Added 'lexicon' to the list of main pages
const mainPages = ['lexicon', 'profile', 'journal', 'stats', 'audio-assets', 'about'];
const toolLinks: QuickAccessLink[] = [{ type: 'tool', id: 'recommendation' }, { type: 'tool', id: 'favorites' }];

// --- SUB-COMPONENTS ---
interface CheckboxItemProps { link: QuickAccessLink; isChecked: boolean; onToggle: (link: QuickAccessLink) => void; label: string; iconName: string; }
const CheckboxItem: React.FC<CheckboxItemProps> = ({ link, isChecked, onToggle, label, iconName }) => (
  <BaseItem as="label">
    <input type="checkbox" checked={isChecked} onChange={() => onToggle(link)} />
    <AppIcon name={iconName} size={22} className="icon" />
    <span>{label}</span>
  </BaseItem>
);

interface AccordionItemProps {
  categoryLink: QuickAccessLink;
  sessions: SessionScript[];
  isChecked: (link: QuickAccessLink) => boolean;
  onToggle: (link: QuickAccessLink) => void;
}
const AccordionItem: React.FC<AccordionItemProps> = ({ categoryLink, sessions, isChecked, onToggle }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSessions = useMemo(() => {
    if (!searchTerm) return sessions;
    return sessions.filter(s => s.title.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [sessions, searchTerm]);

  return (
    <div>
      <BaseItem onClick={() => setIsOpen(!isOpen)}>
        <input type="checkbox" checked={isChecked(categoryLink)} onChange={e => { e.stopPropagation(); onToggle(categoryLink); }} />
        <AppIcon name={categoryLink.id} size={22} className="icon" />
        <span>{t(`sessionTypes.${categoryLink.id}`)}</span>
        <AccordionChevron $isOpen={isOpen}>
          <AppIcon name="chevron-down" size={20} />
        </AccordionChevron>
      </BaseItem>
      {isOpen && (
        <AccordionContent>
          <SearchContainer>
            <AppIcon name="search" size={16} className="search-icon" />
            <SearchInput type="text" placeholder={t('home.modal.searchPlaceholder', 'Search sessions...')} value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
          </SearchContainer>
          {filteredSessions.map(session => {
            const sessionLink: QuickAccessLink = { type: 'session', id: session.id };
            return <CheckboxItem key={session.id} link={sessionLink} isChecked={isChecked(sessionLink)} onToggle={onToggle} label={session.title} iconName={session.type} />;
          })}
        </AccordionContent>
      )}
    </div>
  );
};

interface GamesAccordionItemProps {
  games: GameInfoForList[];
  isChecked: (link: QuickAccessLink) => boolean;
  onToggle: (link: QuickAccessLink) => void;
}
const GamesAccordionItem: React.FC<GamesAccordionItemProps> = ({ games, isChecked, onToggle }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredGames = useMemo(() => {
    if (!searchTerm) return games;
    return games.filter(game => t(game.titleKey).toLowerCase().includes(searchTerm.toLowerCase()));
  }, [games, searchTerm, t]);

  const categoryLink: QuickAccessLink = { type: 'game_category', id: 'games' };

  return (
    <div>
      <BaseItem onClick={() => setIsOpen(!isOpen)}>
        <input type="checkbox" checked={isChecked(categoryLink)} onChange={e => { e.stopPropagation(); onToggle(categoryLink); }} />
        <AppIcon name="games" size={22} className="icon" />
        <span>{t('navigation.games')}</span> {/* Label for the "Games" category */}
        <AccordionChevron $isOpen={isOpen}>
          <AppIcon name="chevron-down" size={20} />
        </AccordionChevron>
      </BaseItem>
      {isOpen && (
        <AccordionContent>
          <SearchContainer>
            <AppIcon name="search" size={16} className="search-icon" />
            <SearchInput type="text" placeholder={t('home.modal.searchGamesPlaceholder', 'Search games...')} value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
          </SearchContainer>
          {filteredGames.map(game => {
            const gameLink: QuickAccessLink = { type: 'game', id: game.id };
            return <CheckboxItem key={game.id} link={gameLink} isChecked={isChecked(gameLink)} onToggle={onToggle} label={t(game.titleKey)} iconName={game.icon} />;
          })}
        </AccordionContent>
      )}
    </div>
  );
};

// --- MAIN COMPONENT ---
interface QuickAccessSettingsModalProps { isOpen: boolean; onClose: () => void; }
const QuickAccessSettingsModal: React.FC<QuickAccessSettingsModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  
  const preferences = useAppStore(state => state.preferences);
  const updatePreferences = useAppStore(state => state.updatePreferences);
  const sessionCategories = useAppStore(state => state.sessions.categories);
  const favoriteSessionIds = useAppStore(state => state.activity?.favoriteSessions) || [];
  const allScripts = useAppStore(state => state.sessions.scripts);
  
  const [currentLinks, setCurrentLinks] = useState<QuickAccessLink[]>([]);

  useEffect(() => { if (isOpen && preferences?.quickAccessLinks) setCurrentLinks(preferences.quickAccessLinks); }, [isOpen, preferences]);

  const favoriteSessions = useMemo(() => {
    return favoriteSessionIds
      .map(id => Object.values(allScripts).flat().find(s => s.id === id))
      .filter((s): s is SessionScript => !!s);
  }, [favoriteSessionIds, allScripts]);

  const handleToggle = (link: QuickAccessLink) => {
    const isSelected = currentLinks.some(l => l.type === link.type && l.id === link.id);
    setCurrentLinks(prev => isSelected ? prev.filter(l => l.type !== link.type || l.id !== link.id) : [...prev, link]);
  };

  const isChecked = (link: QuickAccessLink) => currentLinks.some(l => l.type === link.type && l.id === link.id);

  const handleSave = async () => { if (user?.uid) { await updatePreferences(user.uid, { quickAccessLinks: currentLinks }); onClose(); } };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContainer $isOpen={isOpen} onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <h3>{t('home.modal.title', 'Customize Quick Access')}</h3>
          <CloseButton onClick={onClose}><AppIcon name="close" size={24} /></CloseButton>
        </ModalHeader>

        <Content>
          <Section>
            <h4>{t('home.modal.toolsSection', 'Tools & Actions')}</h4>
            <LinksGrid>
              {toolLinks.map(link => (
                <CheckboxItem key={`${link.type}-${link.id}`} link={link} isChecked={isChecked(link)} onToggle={handleToggle} label={t(`navigation.${link.id}`)} iconName={link.id} />
              ))}
            </LinksGrid>
          </Section>

          <Section>
            <h4>{t('home.modal.pagesSection', 'Main Pages')}</h4>
            <LinksGrid>
              {mainPages.map(id => {
                const link: QuickAccessLink = { type: 'page', id };
                return <CheckboxItem key={id} link={link} isChecked={isChecked(link)} onToggle={handleToggle} label={t(`navigation.${id}`)} iconName={id} />;
              })}
            </LinksGrid>
          </Section>

          <Section>
            <h4>{t('home.modal.categoriesSection', 'Session Categories')}</h4>
            {sessionCategories.map(catId => {
              const categoryLink: QuickAccessLink = { type: 'session_category', id: catId };
              return <AccordionItem key={catId} categoryLink={categoryLink} sessions={allScripts[catId] || []} isChecked={isChecked} onToggle={handleToggle} />;
            })}
          </Section>
          
          <Section>
            <h4>{t('home.modal.gamesSection', 'Games')}</h4>
            {/* Replaced simple checkbox with new GamesAccordionItem */}
            <GamesAccordionItem games={GAMES_LIST} isChecked={isChecked} onToggle={handleToggle} />
          </Section>

          {favoriteSessions.length > 0 && (
            <Section>
              <h4>{t('home.modal.favoritesSection', 'Favorite Sessions')}</h4>
              <LinksGrid>
                {favoriteSessions.map(session => {
                  const link: QuickAccessLink = { type: 'session', id: session.id };
                  return <CheckboxItem key={link.id} link={link} isChecked={isChecked(link)} onToggle={handleToggle} label={session.title} iconName={session.type} />;
                })}
              </LinksGrid>
            </Section>
          )}
        </Content>

        <ModalFooter>
          <Button variant="secondary" onClick={onClose}>{t('common.cancel', 'Cancel')}</Button>
          <Button variant="primary" onClick={handleSave}>{t('common.save', 'Save')}</Button>
        </ModalFooter>
      </ModalContainer>
    </ModalOverlay>
  );
};

export default QuickAccessSettingsModal;