// src/styles/styled.d.ts

import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    // --- Propriétés existantes (vérifiez qu'elles correspondent à vos thèmes) ---
    name: string;

    background: string;
    surface: string;
    surfaceAlt: string;

    primary: string;
    secondary: string;
    accent: string;
    primaryHover?: string;
    accentHover?: string;

    text: string;
    textSecondary: string;
    textLight: string;
    textMuted?: string;
    textLightOnPrimary?: string;

    border: string;
    
    cardShadow: string;
    cardHoverShadow: string; // MODIFIED: Made non-optional for consistency. Was cardHoverShadow?: string;
    headerShadow: string;

    logoBg?: string;
    navActive?: string;
    navInactive?: string;
    
    gradientStart?: string;
    gradientEnd?: string;
    primaryGradient?: string;

    inputBackground?: string;
    errorColor?: string;
    successColor?: string;
    
    disabled?: string;
    disabledBackground?: string;
    disabledText?: string;

    heroOverlay?: string;

    borderSlight: string;
    shadowSmall: string;

    // --- NOUVELLES Propriétés pour les boutons Google ---
    googleButtonBackground?: string;
    googleButtonText?: string;
    googleButtonBorder?: string;
    googleButtonHoverBackground?: string;

    // --- Propriétés Zen Tetris ---
    zenTetrisPiece1?: string;
    zenTetrisPiece2?: string;
    zenTetrisPiece3?: string;
    zenTetrisPiece4?: string;
    zenTetrisPiece5?: string;
    zenTetrisPiece6?: string;
    zenTetrisPiece7?: string;
    zenTetrisBackgroundCell?: string;
    zenTetrisBoardBackground?: string;
    hoverShadow?: string;

    // Table properties
    tableHeaderBackground: string;
    tableRowEvenBackground: string;
    tableRowHoverBackground: string;
    tableBorder: string;
  }
}