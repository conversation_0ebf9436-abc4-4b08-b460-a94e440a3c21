// scripts/upload-content-to-firestore.js

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import admin from 'firebase-admin';
import CryptoJS from 'crypto-js'; // Import crypto-js
import dotenv from 'dotenv'; // Import dotenv to read .env files

// --- Environment Configuration ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Load environment variables from the root .env file
dotenv.config({ path: path.join(__dirname, '../.env') });

const { VITE_SHARED_ENCRYPTION_KEY } = process.env;

if (!VITE_SHARED_ENCRYPTION_KEY) {
  console.error("ERROR: VITE_SHARED_ENCRYPTION_KEY is not defined in your .env file.");
  console.error("Please add it to the .env file at the root of your project.");
  process.exit(1); // Exit the script if the key is missing
}

// --- Firebase Admin Initialization ---
const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');
const serviceAccount = fs.readJsonSync(serviceAccountPath);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
const db = admin.firestore();
const { FieldValue } = admin.firestore;

// --- Path Configuration ---
const SESSIONS_SOURCE_DIR = path.join(__dirname, '../public/assets/SessionScript');
const TRANSLATIONS_SOURCE_DIR = path.join(__dirname, '../public/locales');
const LANGUAGES = ['fr', 'en', 'es'];

/**
 * Encrypts session scripts and their category index before uploading to Firestore.
 */
async function uploadSessionScripts() {
  console.log('Starting ENCRYPTED session scripts upload...');

  for (const lang of LANGUAGES) {
    const langDir = path.join(SESSIONS_SOURCE_DIR, lang);
    if (!fs.existsSync(langDir)) {
      console.warn(`- Directory not found for language '${lang}', skipping.`);
      continue;
    }

    const batch = db.batch();
    const categories = new Set();

    const files = fs.readdirSync(langDir);
    for (const file of files) {
      if (path.extname(file) !== '.json') continue;

      const filePath = path.join(langDir, file);
      try {
        const sessionData = fs.readJsonSync(filePath);
        if (!sessionData.id || !sessionData.type) {
          console.error(`  - Skipping ${file} due to missing id or type.`);
          continue;
        }

        categories.add(sessionData.type);

        const docRef = db.collection('SessionScripts').doc(lang).collection(sessionData.type).doc(sessionData.id);

        const structuredData = {
          ...sessionData,
          updatedAt: FieldValue.serverTimestamp(),
        };

        // Encrypt the entire session object
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(structuredData), VITE_SHARED_ENCRYPTION_KEY).toString();
        
        // Store the encrypted string in a single field
        batch.set(docRef, { encrypted }, { merge: true });
        console.log(`  - Prepared ENCRYPTED script '${sessionData.id}' for lang '${lang}'.`);

      } catch (error) {
        console.error(`  - Error processing file ${file} for lang '${lang}':`, error);
      }
    }

    // Encrypt the category index document as well
    const langDocRef = db.collection('SessionScripts').doc(lang);
    const categoryList = Array.from(categories);
    const categoryPayload = { categories: categoryList };
    const encryptedCategories = CryptoJS.AES.encrypt(JSON.stringify(categoryPayload), VITE_SHARED_ENCRYPTION_KEY).toString();
    
    // Store the encrypted category list
    batch.set(langDocRef, { encrypted: encryptedCategories }, { merge: true });
    console.log(`  - Prepared ENCRYPTED category index for '${lang}'.`);

    try {
      await batch.commit();
      console.log(`✅ Successfully committed ENCRYPTED batch for language '${lang}'.\n`);
    } catch (error) {
      console.error(`❌ Failed to commit batch for language '${lang}':`, error);
    }
  }
}

// uploadTranslations remains unchanged as this content is not sensitive.
async function uploadTranslations() {
    console.log('Starting translations upload...');
    const batch = db.batch();
    for (const lang of LANGUAGES) {
        const translationFilePath = path.join(TRANSLATIONS_SOURCE_DIR, lang, 'translation.json');
        if (!fs.existsSync(translationFilePath)) {
            console.warn(`- Translation file not found for '${lang}', skipping.`);
            continue;
        }
        try {
            const translationData = fs.readJsonSync(translationFilePath);
            const docRef = db.collection('AppLanguage').doc(lang);
            batch.set(docRef, { ...translationData, updatedAt: FieldValue.serverTimestamp() }, { merge: true });
            console.log(`  - Prepared translation for language '${lang}'.`);
        } catch (error) {
            console.error(`- Error processing translation for '${lang}':`, error);
        }
    }
    try {
        await batch.commit();
        console.log(`✅ Successfully committed batch for translations.\n`);
    } catch (error) {
        console.error(`❌ Failed to commit translations batch:`, error);
    }
}

async function main() {
  console.log('--- Connecting to Firestore and starting content upload ---\n');
  await uploadSessionScripts();
  console.log('--------------------------------------------------\n');
  await uploadTranslations();
  console.log('\n--- Content upload process finished. ---');
}

main().catch(console.error);