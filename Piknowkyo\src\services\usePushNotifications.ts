import { useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { PushNotifications } from '@capacitor/push-notifications';

// Fonction pour vérifier si on est sur le web
const isWeb = () => {
  return Capacitor.getPlatform() === 'web';
};

// Interface commune pour les notifications
interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  data?: any;
}

// Implémentation pour le web
class WebPushNotifications {
  static async requestPermissions() {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications web');
      return { receive: 'denied' };
    }

    const permission = await Notification.requestPermission();
    return { receive: permission };
  }

  static async register() {
    // Pour le web, l'enregistrement est plus complexe et nécessite un service worker
    // Cette implémentation est simplifiée
    console.log('Web notifications registered');
    return;
  }

  static showNotification(options: NotificationOptions) {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications web');
      return;
    }

    if (Notification.permission === 'granted') {
      new Notification(options.title, {
        body: options.body,
        icon: options.icon,
        data: options.data
      });
    }
  }

  // Méthodes pour simuler l'API Capacitor
  static addListener(event: string) {
    // Pour le web, on ne peut pas vraiment écouter les événements de la même façon
    // On pourrait implémenter cela avec un service worker
    console.log(`Web notification listener added for ${event}`);
    return {
      remove: () => console.log(`Web notification listener removed for ${event}`)
    };
  }
}

export function usePushNotifications() {
  useEffect(() => {
    if (isWeb()) {
      // Utiliser l'API Web Notifications
      WebPushNotifications.requestPermissions().then(result => {
        if (result.receive === 'granted') {
          WebPushNotifications.register();
        }
      });

      // Pour le web, on n'a pas besoin d'ajouter des listeners comme avec Capacitor
      // car les notifications sont gérées différemment
    } else {
      // Utiliser Capacitor pour les plateformes mobiles
      PushNotifications.requestPermissions().then(result => {
        if (result.receive === 'granted') {
          PushNotifications.register();
        }
      });

      PushNotifications.addListener('registration', token => {
        // Enregistre le token pour l'utilisateur si besoin
        console.log('Push registration success, token:', token.value);
      });

      PushNotifications.addListener('registrationError', err => {
        console.error('Push registration error:', err);
      });

      PushNotifications.addListener('pushNotificationReceived', notification => {
        alert('Notification reçue: ' + notification.title + '\n' + notification.body);
      });

      PushNotifications.addListener('pushNotificationActionPerformed', action => {
        // Gère l'action sur la notification
        console.log('Notification action performed', action);
      });
    }
  }, []);
}

// Exporter une fonction pour envoyer des notifications (utile pour les tests)
export function sendNotification(options: NotificationOptions) {
  if (isWeb()) {
    WebPushNotifications.showNotification(options);
  } else {
    // Pour les plateformes mobiles, cela nécessiterait un backend
    console.log('Sending push notification on mobile requires a backend service');
  }
}
