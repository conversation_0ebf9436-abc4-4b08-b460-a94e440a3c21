// src/hooks/useSubscription.ts

import { useMemo, useState } from 'react';
import { useAppStore, type SubscriptionState as StoreSubscriptionState } from '../store/useAppStore';
import { useAuth } from './useAuth';

/**
 * A hook to manage and access the user's subscription status.
 * It reads the subscription state directly from the Zustand store.
 */
export const useSubscription = () => {
  const { user } = useAuth();
  const subscription = useAppStore((state) => state.subscription);
  const isStoreInitialized = useAppStore((state) => state.isInitialized);
  const updateSubscription = useAppStore((state) => state.updateSubscription);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // --- Derived State ---
  const trialDaysRemaining = useMemo(() => {
    // FIX: Use 'trialEnds' to match the updated SubscriptionState interface
    if (!subscription?.isTrialActive || !subscription.trialEnds) {
      return 0;
    }
    const now = new Date();
    // FIX: Use 'trialEnds' to match the updated SubscriptionState interface
    const trialEnd = new Date(subscription.trialEnds);
    const days = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return Math.max(0, days);
    // FIX: Use 'trialEnds' to match the updated SubscriptionState interface
  }, [subscription?.isTrialActive, subscription?.trialEnds]);

  const hasPremiumAccess = !!subscription &&
    (subscription.isActive || (subscription.isTrialActive && trialDaysRemaining > 0));

  return {
    subscription,
    trialDaysRemaining,
    hasPremiumAccess,
    isLoading: !isStoreInitialized,
    isUpdating,
    error,
    updateSubscription: async (updates: Partial<StoreSubscriptionState>) => {
      if (!user) {
        const error = new Error('User must be authenticated to update subscription.');
        setError(error);
        return { success: false, error };
      }

      setIsUpdating(true);
      setError(null);
      
      try {
        await updateSubscription(user.uid, updates);
        return { success: true, error: null };
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        return { success: false, error };
      } finally {
        setIsUpdating(false);
      }
    }
  };
};