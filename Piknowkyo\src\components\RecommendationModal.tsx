// src/components/RecommendationModal.tsx

import React, { useState, useEffect, useMemo, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAppStore } from '../store/useAppStore';
import { useShallow } from 'zustand/react/shallow';
import { SessionScript } from '../store/useAppStore';
import { Dictionary } from '../types/definitions';
import AppIcon from './AppIcon';
import SessionsList from './SessionsList';
import LexiconText from './LexiconText';
import { ConversationState, Language } from '../lib/conversation/types';
import { conversationStates } from '../lib/conversation/states';
import { calculateRecommendations, ScoredSession } from '../lib/conversation/engine';

// --- STYLED COMPONENTS ---
const fadeIn = keyframes`from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); }`;
const ModalOverlay = styled.div<{ $isOpen: boolean }>` position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.8); backdrop-filter: blur(8px); z-index: 1000; display: flex; justify-content: center; align-items: center; `;
const ModalContainer = styled.div` background: ${({ theme }) => theme.surface}; width: 100%; height: 100%; display: flex; flex-direction: column; overflow: hidden; `;
const ModalHeader = styled.header` padding: 1rem 1.5rem; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid ${({ theme }) => theme.border}; flex-shrink: 0; h2 { font-size: 1.2rem; color: ${({ theme }) => theme.primary}; margin: 0; display: flex; align-items: center; gap: 0.75rem; } `;
const HeaderButton = styled.button` background: none; border: none; padding: 0.5rem; color: ${({ theme }) => theme.textSecondary}; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; font-size: 0.9rem; &:hover { color: ${({ theme }) => theme.primary}; } `;
const ChatContainer = styled.div` flex-grow: 1; overflow-y: auto; padding: 1.5rem; display: flex; flex-direction: column; gap: 1.5rem; `;
const MessageBubble = styled.div<{ $isUser?: boolean }>` background: ${({ theme, $isUser }) => ($isUser ? theme.primary + '20' : theme.surfaceAlt)}; align-self: ${({ $isUser }) => ($isUser ? 'flex-end' : 'flex-start')}; color: ${({ theme }) => theme.text}; padding: 1rem 1.25rem; border-radius: 18px; max-width: 80%; line-height: 1.5; animation: ${fadeIn} 0.5s ease-out forwards; `;
const OptionsContainer = styled.div` display: flex; flex-wrap: wrap; gap: 0.75rem; justify-content: flex-start; margin-top: 1rem; animation: ${fadeIn} 0.5s 0.2s ease-out forwards; opacity: 0; `;
const OptionButton = styled.button`
  display: inline-flex; align-items: center; gap: 0.5rem; justify-content: space-between;
  background: ${({ theme }) => theme.surface}; border: 1px solid ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.primary}; padding: 0.6rem 1rem; border-radius: 20px;
  cursor: pointer; font-weight: 500; transition: all 0.2s; text-align: left;
  &:hover { background: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight}; }
`;
const LexiconIcon = styled.span`
  display: inline-flex; align-items: center; justify-content: center;
  margin-left: 0.25rem; padding: 0.2rem; border-radius: 50%; cursor: pointer; transition: background-color 0.2s;
  &:hover { background-color: rgba(0,0,0,0.15); }
`;
const FinalRecommendation = styled.div` padding: 2rem; text-align: center; animation: ${fadeIn} 0.5s ease-out forwards; h3 { font-size: 1.5rem; margin-bottom: 0.5rem; } p { color: ${({ theme }) => theme.textSecondary}; max-width: 600px; margin: 0 auto 2rem auto; } `;
const LoadingContainer = styled.div` display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; gap: 1rem; color: ${({ theme }) => theme.textSecondary}; `;

const createInitialState = (): ConversationState => ({
  currentStepId: 'start',
  userProfile: { emotions: [], somatic_markers: [], cognitive_patterns: [], behavioral_patterns: [], metaphorical_images: [], energetic_states: [], contextual_triggers: [], desired_outcomes: [] },
  completedDomains: ['initial'],
  pathHistory: [],
});

interface RecommendationModalProps {
  isOpen: boolean;
  onClose: () => void;
  allSessions: SessionScript[];
}

const RecommendationModal: React.FC<RecommendationModalProps> = ({ isOpen, onClose, allSessions }) => {
  const { t, i18n } = useTranslation(['translation', 'dictionary']);
  const lang = i18n.language as Language;

  const { dictionary, dictionaryMap, isLoading: isLoadingDefinitions, showDefinitionInModal } = useAppStore(useShallow(state => ({
    dictionary: state.definitions.dictionary,
    dictionaryMap: state.definitions.dictionaryMap,
    isLoading: state.definitions.isLoading,
    showDefinitionInModal: state.showDefinitionInModal,
  })));
  
  const [conversationState, setConversationState] = useState<ConversationState>(createInitialState);
  const [conversationLog, setConversationLog] = useState<{ id: number, text: string, isUser?: boolean, options?: any[] }[]>([]);
  const [finalRecommendation, setFinalRecommendation] = useState<ScoredSession | null>(null);
  const chatEndRef = useRef<null | HTMLDivElement>(null);

  const lexiconKeyByTagName = useMemo(() => {
    const map = new Map<string, string>();
    if (dictionaryMap.size === 0) return map;
    for (const [key, element] of dictionaryMap.entries()) {
      if (element.name && typeof element.name === 'object' && lang in element.name) {
        const tagName = element.name[lang];
        if(tagName) map.set(tagName.toLowerCase(), key);
      }
      map.set(element.key.toLowerCase(), key);
    }
    return map;
  }, [dictionaryMap, lang]);

  useEffect(() => { if (isOpen) startConversation(); }, [isOpen]);
  useEffect(() => { chatEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }, [conversationLog]);

  useEffect(() => {
    if (!isOpen || !dictionary || isLoadingDefinitions || finalRecommendation) {
      return;
    }

    const currentStateNode = conversationStates[conversationState.currentStepId];
    if (!currentStateNode) {
      console.error(`[RecommendationModal] Unknown conversation stepId: ${conversationState.currentStepId}`);
      return;
    }

    const addNodeToLog = () => {
      const text = currentStateNode.getText(t, conversationState.userProfile, dictionary, lang);
      if (text) {
        const newLogEntry = {
          id: Date.now() + Math.random(),
          text: text,
          options: currentStateNode.getOptions?.(dictionary, conversationState.userProfile, lang, t)
        };
        if (conversationLog.length === 0 || conversationLog[conversationLog.length - 1]?.text !== newLogEntry.text) {
             setConversationLog(prevLog => [...prevLog, newLogEntry]);
        }
      }
    };
    
    if (currentStateNode.type === 'proposing_exploration') {
      addNodeToLog();
      setTimeout(() => {
        const { nextStateId, updatedProfile, updatedCompletedDomains } = currentStateNode.determineNextState(null, conversationState);
        setConversationState(prevState => ({
          ...prevState,
          currentStepId: nextStateId,
          userProfile: updatedProfile,
          completedDomains: updatedCompletedDomains,
          pathHistory: [...prevState.pathHistory, prevState.currentStepId],
        }));
      }, 1200);
    } else if (currentStateNode.type !== 'recommendation_end') {
      addNodeToLog();
    }
  
    if (conversationState.currentStepId !== 'start' && currentStateNode.type !== 'recommendation_end') {
      const { bestMatch, hasConfidentResult } = calculateRecommendations(allSessions, conversationState.userProfile, dictionary, lang);
      if (hasConfidentResult) {
        setFinalRecommendation(bestMatch);
        setConversationState(prev => ({ ...prev, currentStepId: 'end_conversation' }));
      } else if (conversationState.currentStepId === 'ask_outcome' && !hasConfidentResult && bestMatch) {
        setFinalRecommendation(bestMatch);
        setConversationState(prev => ({ ...prev, currentStepId: 'end_conversation' }));
      }
    } else if (currentStateNode.type === 'recommendation_end' && !finalRecommendation) {
        const { bestMatch } = calculateRecommendations(allSessions, conversationState.userProfile, dictionary, lang);
        setFinalRecommendation(bestMatch);
    }
  }, [conversationState.currentStepId, isOpen, dictionary, allSessions, finalRecommendation, lang, t, isLoadingDefinitions]);

  const startConversation = () => {
    setConversationState(createInitialState());
    setConversationLog([]);
    setFinalRecommendation(null);
  };

  const handleOptionSelect = (optionKey: string, optionLabel: string) => {
    if (!dictionary || finalRecommendation) return;

    setConversationLog(log => [
      ...log.map(item => ({ ...item, options: undefined })),
      { id: Date.now() + Math.random(), text: optionLabel, isUser: true },
    ]);
    
    const currentStateNode = conversationStates[conversationState.currentStepId];
    if (!currentStateNode) return;

    const { nextStateId, updatedProfile, updatedCompletedDomains } = currentStateNode.determineNextState(optionKey, conversationState);
    
    setConversationState(prevState => ({
      currentStepId: nextStateId,
      userProfile: updatedProfile,
      completedDomains: updatedCompletedDomains,
      pathHistory: [...prevState.pathHistory, prevState.currentStepId],
    }));
  };
  
  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContainer onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <h2><AppIcon name="recommendation" /> {t('recommendationAssistant.title')}</h2>
          <div>
            <HeaderButton onClick={startConversation}><AppIcon name="clear-filters" size={16} />{t('common.restart')}</HeaderButton>
            <HeaderButton onClick={onClose}><AppIcon name="close" size={20} /></HeaderButton>
          </div>
        </ModalHeader>
        
        {isLoadingDefinitions || !dictionary ? (
            <LoadingContainer>
                <AppIcon name="loader" size={40}/>
                <p>{t('common.loading')}</p>
            </LoadingContainer>
        ) : (
            <ChatContainer>
                {conversationLog.map(msg => (
                    <MessageBubble key={msg.id} $isUser={msg.isUser}>
                    {msg.isUser ? msg.text : <LexiconText text={msg.text} />}
                    {msg.options && !finalRecommendation && (
                        <OptionsContainer>
                          {msg.options.map(opt => {
                            const lexiconKey = lexiconKeyByTagName.get(opt.label.toLowerCase());
                            return (
                              <OptionButton key={opt.key} onClick={() => handleOptionSelect(opt.key, opt.label)}>
                                <span>{opt.label}</span>
                                {lexiconKey && (
                                  <LexiconIcon
                                    onClick={(e) => { e.preventDefault(); e.stopPropagation(); showDefinitionInModal(lexiconKey); }}
                                    title={t('sessionDetails.viewInLexicon', { term: opt.label })}
                                  >
                                    <AppIcon name="book-open" size={14} />
                                  </LexiconIcon>
                                )}
                              </OptionButton>
                            );
                          })}
                        </OptionsContainer>
                    )}
                    </MessageBubble>
                ))}
                {finalRecommendation && finalRecommendation.session ? (
                    <FinalRecommendation>
                      <h3>{t('recommendationAssistant.results_title')}</h3>
                      <p><LexiconText text={t('recommendationAssistant.default_reason')} /></p>
                      <SessionsList sessions={[finalRecommendation.session]} viewMode="grid" />
                    </FinalRecommendation>
                ) : (
                    conversationState.currentStepId === 'end_conversation' && (
                        <MessageBubble>
                            <p><LexiconText text={t('recommendationAssistant.no_specific_match')} /></p>
                        </MessageBubble>
                    )
                )}
                <div ref={chatEndRef} />
            </ChatContainer>
        )}
      </ModalContainer>
    </ModalOverlay>
  );
};

export default RecommendationModal;