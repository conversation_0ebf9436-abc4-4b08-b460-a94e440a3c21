{"navigation": {"home": "<PERSON><PERSON>o", "sessions": "Sesiones", "games": "<PERSON><PERSON><PERSON>", "journal": "Diario", "stats": "Estadísticas", "blog": "Blog", "profile": "Perfil", "monetization": "Premium", "settings": "<PERSON><PERSON><PERSON><PERSON>", "about": "Acerca de", "audio-assets": "Mis Audios", "recommendation": "Recomendación", "favorites": "<PERSON><PERSON>", "lexicon": "Léxico"}, "lexicon": {"relatedConcepts": "Conceptos relacionados", "title": "Léxico", "subtitle": "Explore los conceptos, las emociones y las técnicas de nuestro enfoque.", "searchPlaceholder": "Buscar un término...", "allCategories": "Todo", "noResults": "No se encontraron definiciones. Intente ajustar su búsqueda o filtro.", "categories": {"basic_emotions": "Emociones básicas", "sentiments": "Sentimiento<PERSON>", "cognitive_patterns": "Pat<PERSON>s cognitivos", "somatic_sensations": "Sensaciones somáticas", "desired_outcomes": "Resultados deseados", "sensory_channels": "Canales sensoriales", "modalities": "Modalidades", "durations": "Duraciones", "intensities": "Intensidades", "techniques": "Técnicas", "energetic_systems": "<PERSON><PERSON><PERSON> energé<PERSON>", "spiritual_concepts": "Conceptos espirituales"}}, "trial": {"banner": {"message": "Tu prueba termina el {{date}} ({{days}} días restantes)"}}, "common": {"welcome": "Bienvenido a PiKnowKyo", "ok": "OK", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Volver", "next": "Siguient<PERSON>", "restart": "Reiniciar", "previous": "Anterior", "loading": "Cargando...", "error": "Error", "success": "Éxito", "days": "días", "unknown": "una fecha desconocida"}, "home": {"title": "Bienvenido a PiKnowKyo", "subtitle": "Tu espacio para cultivar la paz interior y el crecimiento.", "exploreButton": "Explorar Sesiones", "welcomeText": "Comienza tu viaje hacia el bienestar. Elige una práctica o explora tus herramientas personalizadas.", "quickAccess": "Acceso Rápido", "learnMore": "Aprende más sobre PiKnowKyo", "welcomeUser": "¡Hola de nuevo, {{name}}! ¿Listo para tu próximo paso?", "customizeQuickAccess": "Personalizar Acceso Rápido", "modal": {"title": "Personalizar Acceso Rápido", "toolsSection": "Herramientas y Acciones", "pagesSection": "Páginas Principales", "categoriesSection": "Categorías de Sesiones", "gamesSection": "<PERSON><PERSON><PERSON>", "favoritesSection": "Tus Sesiones Favoritas", "searchPlaceholder": "Buscar sesiones...", "searchGamesPlaceholder": "Buscar juegos..."}}, "sessions": {"title": "Explorar Sesiones", "meditation": "Meditación", "hypnosis": "Hipnosis", "affirmations": "Afirmaciones", "custom": "<PERSON><PERSON>", "description": "Descubre nuestra colección de sesiones guiadas para tu crecimiento personal.", "searchPlaceholder": "Buscar una sesión...", "allTypes": "Todos los tipos", "allDurations": "Todas las duraciones", "durationLabel": "Duración", "type": "Tipo", "category": "Categoría", "difficulty": "Dificultad", "noSessionsFound": "No se encontraron sesiones que coincidan con tus criterios.", "noCategoriesAvailable": "No hay categorías de sesiones disponibles en este momento.", "noResultsInGroup": "Ninguna sesión en esta categoría coincide con tus criterios.", "clearFilters": "Limpiar filtros", "filterBy": "Filtrar por", "sessionType": "Tipo de Sesión", "gridView": "Vista de Cuadrícula", "listView": "Vista de Lista", "noResultsMatchCriteria": "Ninguna sesión coincide con tus criterios.", "noSessionsAvailable": "No hay sesiones disponibles por el momento.", "story": "Historia", "favoritesFilter": "Solo favoritos", "noResults": "No se encontraron sesiones para esta categoría o filtro.", "noResults.filtered": "Ninguna sesión coincide con tus filtros actuales.", "recommendation.button": "Obtener una Recomendación", "filter": {"searchPlaceholder": "Buscar en todas las sesiones...", "showAll": "<PERSON><PERSON>", "showFavorites": "<PERSON><PERSON><PERSON><PERSON>", "filteringByTag": "Filtrando por etiqueta:", "duration": {"all": "Cualquier duración"}, "allCategories": "Todas las categorías", "toggleView": "Cambiar vista", "clear": "Limpiar filtros"}, "filters": {"title": "Filtrar Sesiones", "showFavorites": "Mostrar solo favoritos", "reset": "Restable<PERSON>", "apply": "Aplicar"}, "card": {"new": "Nuevo", "toggleFavorite": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>ar de favoritos"}, "viewModes": {"grid": "Cuadrícula", "list": "Lista"}, "duration": {"label": "Duración", "under15": "Menos de 15 min", "15to30": "15 - 30 min", "over30": "Más de 30 min"}, "mood": {"label": "Estado de ánimo", "all": "Todos los ánimos", "calm": "Calma", "energized": "Enérgico", "focused": "Concentrado", "stressed": "Estresado"}, "toggleView": "Cambiar vista"}, "games": {"title": "Minijuegos de Desarrollo Personal", "intro": "Prueba y mejora tus habilidades con nuestros divertidos y desafiantes minijuegos.", "zenTetris": {"title": "Zen Tetris", "description": "Una versión relajante del famoso juego de bloques. Mejora tu concentración y gestión del estrés.", "rules": "Coloca las piezas que caen para completar líneas horizontales. Cuantas más líneas elimines a la vez, más puntos obtendrás. El juego se acelera gradualmente.", "controls": "Controles Táctiles", "tips": "Man<PERSON>n la calma, planifica tus movimientos e intenta crear combos para maximizar tu puntuación.", "touchTap": "Toque Rápido: <PERSON><PERSON><PERSON>", "touchLeft": "<PERSON><PERSON><PERSON>: <PERSON><PERSON>", "touchRight": "<PERSON><PERSON><PERSON>: <PERSON><PERSON> <PERSON><PERSON>a", "touchDown": "<PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON>", "touchButtons": "Botones de control en la parte inferior"}, "cardiacCoherence": {"title": "Coherencia Cardíaca", "description": "Un ejercicio de respiración guiada para sincronizar tu corazón y tu mente. Mejora tu concentración y reduce el estrés.", "setup": {"title": "Configu<PERSON>", "mode": "Modo", "adult": "Adulto", "child": "<PERSON><PERSON>", "duration": "Duración", "useTTS": "<PERSON><PERSON><PERSON> vocal (TTS)", "useSoundEffects": "Efectos de sonido"}, "getReady": "Prepárate...", "inhale": "Inhala...", "exhale": "Exhala...", "hold": "Mantén...", "finished": "Sesión completada", "finishedTitle": "Sesión <PERSON>tad<PERSON>", "finishedMessage": "¡Buen trabajo! Has completado tu sesión de respiración."}, "estimatedDuration": "Duración estimada", "personalBest": "Récord personal", "savedGameProgress": "Partida guardada", "maxLevels": "Este juego tiene {{maxLevels}} niveles de dificultad.", "yourBestScore": "Tu mejor puntuación en este juego es de {{score}} puntos.", "moveLeft": "Mover a la Izquierda", "moveRight": "Mover a la Derecha", "softDrop": "Caída Suave", "rotate": "R<PERSON><PERSON>", "level": "<PERSON><PERSON>", "lines": "Líneas", "nextPiece": "Siguiente Pieza", "info": "Info", "keywords": "Palabra<PERSON>", "continueGame": "Continuar Partida", "newGame": "Nueva Partida", "gameInfo": "Información del Juego", "gameRules": "Reglas del Juego", "gameOver": "Fin del Juego", "finalScore": "Puntuación Final", "newRecord": "¡Nuevo Récord!", "playAgain": "<PERSON><PERSON>", "backToGames": "Volver a los Juegos", "pause": "Pausa", "resume": "<PERSON><PERSON><PERSON>", "quit": "Salir", "gameOverSummary": "¡Felicidades! Tu puntuación final es de {{score}} puntos y alcanzaste el nivel {{level}} en {{time}} segundos."}, "game": {"start": "Comenzar", "resume": "<PERSON><PERSON><PERSON>", "pauseButton": "Pausar", "orientationHint": "Gira para una mejor experiencia", "modal": {"rulesTitle": "Reglas del Juego", "pausedTitle": "Juego en Pausa", "gameOverTitle": "¡Fin del Juego!", "return": "Volver", "restart": "Reiniciar", "resume": "<PERSON><PERSON><PERSON>", "start": "Comenzar", "pausedMessage": "Tu partida está en pausa. Reanuda cuando estés listo.", "gameOverMessage": "¡Bien hecho! Tu puntuación final es de {{score}} y alcanzaste el nivel {{level}}.", "gameOverSummary": "¡Felicidades! Tu puntuación final es de {{score}} puntos y alcanzaste el nivel {{level}} en {{time}} segundos."}, "zenTetris": {"ruleMoveLeft": "Mover a la izquierda", "ruleMoveRight": "Mover a la derecha", "ruleSoftDrop": "Caída suave (soft drop)", "ruleRotate": "R<PERSON><PERSON>", "rulePause": "Pausa", "rules1": "Apila los bloques para formar líneas completas y sumar puntos. ¡La velocidad aumenta con los niveles!", "rules2": "Controles:"}, "controls": {"keyboard": "Teclado", "touch": "Táctil"}, "info": "Info", "level": "<PERSON><PERSON>", "lines": "Líneas", "score": "Puntuación", "time": "Tiempo", "nextPiece": "Siguiente Pieza", "moveLeft": "Mover <PERSON>", "moveRight": "Mover <PERSON><PERSON>", "rotate": "R<PERSON><PERSON>", "softDrop": "Caída Suave"}, "journal": {"title": "Diario de Seguimiento", "trackingJournal": "Diario de Seguimiento", "yourNotes": "Tu Diario para esta Sesión", "noEntries": "Aún no hay entradas en el diario.", "addEntry": "Añade tu reflexión sobre esta sesión...", "description": "Encuentra todas tus notas personales aquí, organizadas por sesión. Reflexiona sobre tus experiencias y sigue tu progreso.", "noNotesYet": "Tu diario aún está vacío.", "startSessionPrompt": "Inicia una sesión y toma notas para ver tus reflexiones aquí.", "unknownSession": "Sesión (ID: {{id}})", "noteSingular": "{{count}} nota", "notesPlural": "{{count}} notas", "seeAllNotes": "Ver todas las {{count}} notas...", "loginToUse": "Inicia sesión para usar la función de diario.", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "deleteConfirmText": "¿Estás seguro de que quieres eliminar permanentemente esta entrada del diario?", "previousEntriesTitle": "Entradas anteriores"}, "rating": {"loginToRate": "Inicia sesión para calificar esta sesión."}, "and": " y ", "stats": {"title": "Tus Estadísticas de Bienestar", "sessionsFollowed": "Sesiones Practicadas", "daysDaysStreakDesc": "Días que inició sesión y completó una actividad.", "duration": {"hours": "h", "min": "min", "h": "h"}, "timesExploredSingular": "vez explorado", "timesExploredPlural": "veces explorados", "and": " y ", "affinityAnalysis.noConcepts": "nada específico", "affinityAnalysis.intro": "Basado en tus sesiones completadas, aquí tienes un resumen de tu viaje y afinidades:", "affinityAnalysis.summary": "Con mayor frecuencia has explorado temas relacionados con: ", "affinityAnalysis.emotions": "Emociones y Sentimientos como {{list}}.", "affinityAnalysis.outcomes": "Resultados deseados como {{list}}.", "stats.affinityAnalysis.techniques": "Técnicas utilizadas incluyendo {{list}}.", "affinityAnalysis.somatic": "Sensaciones somáticas exploradas incluyendo {{list}}.", "affinityAnalysis.cognitive": "Patrones cognitivos abordados como {{list}}.", "affinityAnalysis.modalities": "Modalidades de bienestar como {{list}}.", "affinityAnalysis.energetic": "Conceptos energéticos incluyendo {{list}}.", "affinityAnalysis.duration": "Duraciones de sesión típicas como {{list}}.", "affinityAnalysis.intensity": "Intensidades de sesión como {{list}}.", "affinityAnalysis.noDetails": "Aún no hay detalles específicos para esta categoría. ¡Sigue explorando!", "affinityAnalysis.overallTopConcepts": "Tus conceptos más explorados (todas las categorías) son: {{list}}.", "affinityAnalysis.moreDetailsLink": "Ver todos los conceptos explorados para más detalles.", "journalEntries": "Tus Entradas de Diario", "showAllJournalEntries": "Ver todas las {{count}} entradas", "showAllActivityHistory": "Ver todo el historial de actividad", "noJournalEntries": "Aún no tienes entradas en el diario. ¡Completa sesiones y toma notas para ver tus reflexiones aquí!", "viewEntry": "Ver entrada", "forSession": "para la sesión", "sessionsCompleted": "Sesiones completadas", "daysStreak": "Días consecutivos", "totalMinutes": "Minutos totales", "noData": "Aún no has completado ninguna sesión. ¡Inicia una sesión para ver tus estadísticas aquí!", "sessionsCompletedDesc": "Número total de sesiones que has completado.", "completionsPlural": "completadas", "completionsSingular": "completada", "activityHistory": "Historial de Actividad", "noActivityHistory": "No hay historial de actividad disponible. ¡Comienza una sesión o un juego para verlo aquí!", "completedOn": "Completado el", "sleepPatterns": "<PERSON><PERSON><PERSON>", "heartRateVariability": "Variabilidad de la Frecuencia Cardíaca", "exerciseTracking": "Seguimiento de Ejercicio", "nutritionHydration": "Nutrición e Hidratación", "futureFeature": "Característica Futura", "comingSoon": "Próximamente", "description": "Sigue tu viaje, celebra tu progreso y descubre tus tendencias.", "sessionsFollowedDesc": "Número de sesiones únicas con notas.", "totalTime": "Tiempo Total en Sesión", "totalTimeDesc": "Tiempo acumulado estimado.", "favoriteSession": "Sesión Favorita", "favoriteSessionDesc": "La más anotada.", "notesWritten": "Total de Notas Escritas", "notesWrittenDesc": "Número de reflexiones guardadas.", "typesFollowed": "Desglose por Tipo de Sesión", "timePerSession": "Detalle por Sesión (Estimado)", "noTypesYet": "Aún no se ha seguido ningún tipo de sesión específico.", "noTimePerSession": "No hay datos de tiempo por sesión disponibles.", "timesPlural": "veces", "timesSingular": "vez", "notesPlural": "notas", "noteSingular": "nota", "gameHighScores": "Mejores Puntuaciones de Juegos", "noGameData": "Aún no has establecido ninguna puntuación alta en los juegos. ¡Ve a jugar para ver tus récords aquí!", "levelLabel": "<PERSON><PERSON>", "yourAffinityProfile": "Tu Perfil de Afinidad", "affinityDescription": "Los conceptos y emociones que más has explorado a través de tus sesiones.", "noAffinityData": "No hay datos de afinidad disponibles. ¡Completa sesiones para ver tu perfil aquí!", "timesExplored": "veces explorado", "healthMetrics": "Métricas de Salud y Bienestar", "futureDevMessage": "Esta sección pronto integrará datos de dispositivos conectados para proporcionar información sobre tus patrones de sueño, variabilidad de la frecuencia cardíaca, ejercicio, nutrición, hidratación y más. ¡Mantente atento para una vista holística completa de tu bienestar!"}, "blog": {"title": "Diario Comunitario", "description": "Comparte tus experiencias, descubrimientos e inspiraciones con la comunidad de PiKnowKyo. Todas las publicaciones son anónimas.", "searchPlaceholder": "Buscar publicaciones...", "allCategories": "Todas las categorías", "writeNewPost": "Escribir una nueva publicación", "postPlaceholder": "Tu publicación (se publicará de forma anónima)...", "category": "Categoría", "publishing": "Publicando...", "publish": "Publicar", "loginToPost": "Debes iniciar sesión para publicar.", "noPostsYet": "Aún no hay publicaciones en esta categoría o que coincidan con tu búsqueda.", "noPostsFound": "No se encontraron publicaciones para esta categoría o término de búsqueda.", "unsyncedPostTooltip": "Esta publicación está guardada localmente y se sincronizará cuando estés en línea.", "unsyncedCommentTooltip": "Este comentario está guardado localmente y se sincronizará cuando estés en línea.", "like": "Me gusta", "comments": "Comentarios", "addComment": "Añadir un comentario", "commentPlaceholder": "<PERSON> comentario (anónimo)...", "postComment": "Publicar Comentario", "noCommentsYet": "Aún no hay comentarios. ¡Sé el primero en comentar!", "backToBlog": "Volver al blog", "postNotFound": "Publicación no encontrada", "commentsSectionTitle": "Comentarios", "yourCommentPlaceholder": "Tu comentario...", "sending": "Enviando...", "sendComment": "Enviar", "loginToComment": "Inicia sesión para añadir un comentario.", "sampleAuthor": "Autor <PERSON>", "samplePostContent": "Contenido detallado de la publicación. Esta publicación habla sobre la importancia de la atención plena en nuestra estresante vida diaria y cómo ejercicios simples pueden traer una gran paz interior.", "sampleCommenter1": "Comentarista1", "sampleCommenter2": "OtraPersona", "sampleComment1": "¡Gran publicación!", "sampleComment2": "<PERSON><PERSON>, gracias por compartir.", "anonymousUser": "<PERSON><PERSON><PERSON>", "unknownDate": "Fecha desconocida", "categories": {"général": "General", "gratitude": "<PERSON><PERSON><PERSON><PERSON>", "défis": "Desafíos", "inspirations": "Inspiraciones", "questions": "Preguntas"}}, "about": {"title": "Acerca de", "description": "Descubre PiKnowKyo, tu compañero para el crecimiento personal y el bienestar.", "features": {"customizable": "Sesiones totalmente personalizables para satisfacer tus necesidades.", "journal": "Diario personal para seguir tu progreso y reflexiones.", "stats": "Estadísticas detalladas para visualizar tu evolución.", "games": "Minijuegos para desarrollar tus habilidades cognitivas."}, "philosophy": {"title": "Nuestra Filosofía: El Viaje de Pi a Kyo", "pi": {"title": "Pi (π)", "description": "El infinito, el misterio sagrado del universo y la armonía fundamental que nos une a todos. Es el punto de partida, la apertura a lo desconocido."}, "know": {"title": "Know (<PERSON><PERSON><PERSON>)", "description": "La exploración, el aprendizaje estructurado y la claridad mental. Es la adquisición de herramientas y comprensiones para navegar el camino."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "La enseñanza, la sabiduría encarnada, la iluminación y el compartir altruista de la luz descubierta. Es la culminación y la irradiación."}, "conclusion": "PiKnowKyo es más que una aplicación; es una brújula para tu crecimiento interior, inspirada por"}, "tools": {"title": "Nuestras Herramientas para Tu Crecimiento", "description": "Ofrecemos una diversa gama de sesiones y herramientas diseñadas para apoyarte en tu camino de crecimiento personal:", "hypnosis": "Hipnosis Evolutiva para explorar tu subconsciente e iniciar cambios profundos.", "meditation": "Meditaciones Guiadas para cultivar la atención plena, la paz interior y la resiliencia emocional.", "affirmations": "Afirmaciones Positivas para reprogramar tus pensamientos y fortalecer tu autoconfianza.", "nlp": "Entrenamiento PNL (Programación Neuro-Lingüística) para mejorar tu comunicación y alcanzar tus metas.", "stories": "Historias Metafóricas para estimular tu imaginación y facilitar la integración de nuevas perspectivas."}, "experience": {"title": "Una Experiencia Holística Diseñada para Ti", "audio": "Audio 100% configurable (música, ambiente, voz, binaural).", "guidedPaths": "<PERSON>utas guiadas y creación de sesiones a medida.", "community": "Comunidad de apoyo y clasificación anónima (opcional).", "blog": "Blog interno con artículos, consejos y recursos inspiradores.", "multilang": "Soporte multi-idioma y temas claro/oscuro personalizables.", "notifications": "Notificaciones motivacionales suaves para acompañarte."}, "monetization": {"title": "Monetización Ética:", "description": "Ofrecemos una prueba gratuita, una suscripción opcional para acceso completo, anuncios mínimos y no intrusivos (evitables con la suscripción) y la posibilidad de donaciones para apoyar nuestra misión."}, "community": {"title": "Únete a Nuestra Comunidad", "description": "PiKnowKyo está diseñado para aquellos que valoran el autoconocimiento, la organización de sus pensamientos y la productividad personal con un enfoque en el bienestar. Ya seas estudiante, profesional, investigador o simplemente una mente curiosa en busca de armonía, nuestra aplicación es tu aliada.", "contact": "Para cualquier pregunta, sugerencia o si necesitas ayuda, no dudes en enviarnos un correo electrónico a:", "website": "También puedes visitar nuestro sitio web", "moreInfo": "para más información"}}, "monetization": {"title": "Mejora Tu Experiencia", "description": "Desbloquea todas las funciones para maximizar tu viaje de bienestar, o continúa con nuestro generoso plan gratuito.", "plans": {"free": {"name": "Plan Gratuito"}, "premium": {"name": "Piknowkyo Premium"}, "billing": {"month": "mes"}}, "features": {"free": {"meditations": "Acceso ilimitado a sesiones de meditación e historias", "musicAndTTS": "Música de fondo y voces TTS básicas", "stats": "Estadísticas de progreso", "journal": "Diario de seguimiento", "adUnlock": "Ver un anuncio para desbloquear una sesión por 1 hora", "support": "Soporte a través de redes sociales"}, "premium": {"allSessions": "Acceso ilimitado a TODAS las sesiones (hipnosis, PNL, etc.)", "cloudTTS": "Voces TTS de la nube", "ambientSounds": "Sonidos ambientales y binaurales avanzados", "stats": "Estadísticas de progreso", "games": "Acceso a minijuegos", "journal": "Diario de seguimiento", "noAds": "Experiencia sin anuncios"}}, "status": {"title": "Estado de la Suscripción", "trialActive": "Actualmente estás en una prueba Premium.", "renewsOn": "Tu plan Premium se renueva el <strong>{{date}}</strong>.", "cancelsOn": "Tu plan Premium expirará el <strong>{{date}}</strong>.", "freePlan": "Actualmente estás en el plan Gratuito.", "cancelsOnDateTime": "Tu acceso terminará el <strong>{{date}} a las {{time}}</strong>.", "renewsOnDateTime": "Tu suscripción se renueva el <strong>{{date}} a las {{time}}</strong>."}, "trial": {"endsIn_one": "Tu prueba termina en {{count}} día.", "endsIn_other": "Tu prueba termina en {{count}} días.", "ended": "Tu período de prueba ha terminado."}, "actions": {"manage": "Administrar Suscripción", "upgradeNow": "<PERSON><PERSON><PERSON><PERSON>ora", "currentPlan": "Plan Actual", "subscribe": "Suscribirse", "subscribeError": "No se pudo obtener la URL de pago de Stripe. Inténtalo de nuevo.", "manageError": "No se pudo obtener la URL del portal del cliente de Stripe. Inténtalo de nuevo."}, "toast": {"checkoutSuccess": "¡Suscripción exitosa! Tu plan se actualizará en breve.", "checkoutCanceled": "Proceso de suscripción cancelado."}}, "sessionDetails": {"yourNotes": "Tu Diario para esta Sesión", "viewInLexicon": "Ver '{{term}}' en el Léxico", "averageRating": "Calificación promedio", "noRatingsYet": "Aún no hay valoraciones. ¡Sé el primero en calificar!", "description": "Descripción", "expectedBenefits": "<PERSON><PERSON><PERSON><PERSON>", "keywords": "Palabra<PERSON>", "startSession": "<PERSON><PERSON><PERSON>", "backToSessions": "Volver a Sesiones", "audioConfigGlobal": "Configuración de Audio de la Sesión", "userReviews": "Opiniones de Usuarios", "previousNotes": "Notas Anteriores", "voiceConfigTitle": "Configuración de Voz", "yourRating": "Tu Calificación", "tagsTitle": "Etiquetas", "lastUpdated": "Última actualización el {{date}}", "tagTooltip": "Ver definición en el Léxico", "benefitsTitle": "<PERSON><PERSON><PERSON><PERSON>", "dnd": {"label": "No Molestar (App)", "permissionNeededInfo": "Al activarlo, se solicitará permiso de notificación para optimizar este modo.", "permissionDeniedWarning": "Permiso de notificación denegado. El modo No Molestar de la app está activo, pero las notificaciones del sistema no se verán afectadas."}}, "units": {"minutes": "min", "points": "pts", "notAvailable": "N/D", "seconds": "seg", "min": "min", "ratings_one": "calificación", "ratings_other": "calificaciones"}, "menu": {"navigation": "Navegación", "account": "C<PERSON><PERSON>"}, "notFound": {"message": "Lo sentimos, la página que buscas no existe.", "backHome": "Volver al Inicio"}, "quiz": {"title": "Quiz", "description": "¡Selecciona un quiz para empezar a probar tus conocimientos!", "comingSoon": "¡Los quizzes llegarán pronto! Mantente atento."}, "history": {"title": "Historial", "description": "Consulta tus resultados y progreso.", "comingSoon": "El historial estará disponible pronto."}, "categories": {"title": "Categorías", "description": "Elige una categoría para explorar los quizzes relacionados.", "comingSoon": "¡Las categorías llegarán pronto!"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "Este navegador no soporta notificaciones."}, "pseudoGenerator": {"adjectives": {"light": "Luz", "wind": "Viento", "ocean": "<PERSON><PERSON>ano", "mountain": "Montaña", "star": "Estrella", "forest": "Bosque", "river": "Río", "sun": "Sol", "moon": "Luna", "aurora": "Aurora"}, "nouns": {"serene": "<PERSON><PERSON>", "calm": "Calmo", "wise": "Sabio", "peaceful": "Pacífico", "clairvoyant": "Clarividente", "harmonious": "<PERSON><PERSON><PERSON>", "awakened": "<PERSON><PERSON><PERSON><PERSON>", "free": "Libre", "creative": "Creativo", "intuitive": "Intuitivo"}}, "app": {"name": "Piknowkyo", "theme_light": "Cambiar a tema claro", "theme_dark": "Cambiar a tema oscuro", "logo_alt": "Logo de Piknowkyo"}, "auth": {"common": {"email_placeholder": "Dirección de Correo Electrónico", "password_placeholder": "Contraseña", "or_separator": "O", "please_wait_loading": "Por favor, espera...", "success_redirect": "¡Inicio de sesión o registro exitoso! Redirigiendo..."}, "login": {"subtitle": "¡Bienvenido de vuelta!", "button": "<PERSON><PERSON><PERSON>", "button_loading": "Iniciando se<PERSON>...", "google_button": "Iniciar se<PERSON><PERSON> con Google", "google_button_loading": "Iniciando sesión con Google...", "error_invalid_credentials": "Correo electrónico o contraseña incorrectos.", "error_google_popup_closed": "La ventana de inicio de sesión de Google se cerró. Por favor, inténtalo de nuevo.", "error_google_popup_cancelled": "Ya hay una solicitud de ventana emergente de Google pendiente o fue cancelada. Por favor, inténtalo de nuevo.", "error_general": "Error al iniciar sesión. Por favor, inténtalo de nuevo.", "toggle_signup": "¿Aún no tienes cuenta? Regístrate"}, "signup": {"subtitle": "¡<PERSON>rea tu cuenta!", "confirm_password_placeholder": "Con<PERSON><PERSON><PERSON>", "button": "Registrarse", "button_loading": "Registrando...", "google_button": "Registrarse con Google", "google_button_loading": "Registrando con Google...", "error_password_mismatch": "Las contraseñas no coinciden.", "error_email_in_use": "Este correo electrónico ya está en uso. Por favor, inicia sesión.", "error_weak_password": "La contraseña es demasiado débil (mínimo 6 caracteres).", "error_general": "Error al registrarse. Por favor, inténtalo de nuevo.", "toggle_login": "¿Ya tienes cuenta? Inicia sesión"}, "logout": {"button": "<PERSON><PERSON><PERSON>", "button_aria_label": "<PERSON><PERSON><PERSON> sesi<PERSON> de tu cuenta"}}, "preferences": {"language": {"question": "Elige tu idioma preferido"}, "notifications": {"question": "¿Te gustaría recibir notificaciones motivacionales?"}, "premium": {"question": "¿Quieres probar las funciones premium gratis (con anuncios no intrusivos)?"}, "yes": "Sí", "no": "No", "thanks": "¡<PERSON><PERSON><PERSON>!", "validate": "Validar mis preferencias"}, "questionnaire": {"goal": {"question": "¿Cuál es tu objetivo principal?", "relaxation": "Relajación", "confidence": "Confianza en uno mismo", "stress": "Manejo del estrés", "spirituality": "Espiritualidad", "other": "<PERSON><PERSON>"}, "experience": {"question": "¿Alguna vez has practicado hipnosis o meditación?", "never": "Nunca", "sometimes": "A veces", "regularly": "Regularmente"}, "audio": {"question": "¿Prefieres una sesión con música, sonidos naturales o silencio?", "music": "Música", "nature": "Sonidos naturales", "silence": "<PERSON><PERSON><PERSON>"}, "thanks": "¡<PERSON><PERSON><PERSON>!", "viewSuggestions": "Ver mis sugerencias"}, "notificationTest": {"heading": "Prueba de Notificaciones", "platform": "Plataforma actual:", "status": "Estado de las notificaciones web:", "title": "Prueba de Notificación", "body": "Esta es una notificación de prueba desde PiKnowKyo", "sendButton": "Enviar una notificación de prueba"}, "actions": {"back": "Volver", "backToBlog": "Volver al Blog", "backToHome": "Volver al Inicio", "backToSessionDetails": "Volver a Detalles", "backToSessions": "Volver a Sesiones", "backToSettings": "Volver a Ajustes", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "deleteConfirm": "Confirmar <PERSON>", "deleting": "Eliminando...", "enterFullscreen": "<PERSON><PERSON> Pan<PERSON>", "exitFullscreen": "Salir de Pantalla Completa", "ok": "OK", "pause": "Pausa", "play": "Reproducir", "preview": "Previsualizar", "restart": "Reiniciar", "startSession": "<PERSON><PERSON><PERSON>", "stopPreview": "Detener", "stopTest": "Detener Prueba", "testSound": "Probar Sonido", "testVoice": "Probar <PERSON>", "upload": "Subir", "saving": "Guardando...", "previewSound": "Previsualizar Sonido"}, "dictionary": {"basic_emotions": {"anger": {"name": "Ira"}, "sadness": {"name": "Tristeza"}, "joy": {"name": "Alegría"}, "fear": {"name": "Miedo"}, "calm": {"name": "Calma"}}, "somatic_sensations": {"chest_oppression": {"name": "Opresión en el pecho"}, "knotted_stomach": {"name": "Estó<PERSON><PERSON>"}, "shoulder_neck_tension": {"name": "Tensión en hombros y cuello"}, "heaviness_limbs": {"name": "Pesadez en las extremidades"}, "racing_heart": {"name": "Corazón acelerado"}}, "cognitive_patterns": {"mental_rumination": {"name": "Rumiación mental"}, "future_anxiety": {"name": "Ansiedad por el futuro"}, "critical_inner_dialogue": {"name": "Diálogo interno crítico"}, "mental_fog": {"name": "Niebla mental"}, "comparison": {"name": "Comparación con otros"}}, "desired_outcomes": {"deep_calm": {"name": "Calma profunda"}, "letting_go": {"name": "Soltar"}, "grounding": {"name": "Enraizamiento"}, "stress_reduction": {"name": "Reducción del estrés"}, "increased_self_esteem": {"name": "Aumento de la autoestima"}, "joyful_living": {"name": "Vivir con alegría"}}}, "audioAssets": {"title": "Gestionar Archivos de Audio", "musicTitle": "Música", "ambientTitle": "Sonidos Ambientales", "noMusics": "No hay música disponible", "noAmbiants": "No hay sonidos ambientales disponibles", "selectFile": "Seleccionar un archivo", "changeFile": "Cambiar archivo", "uploadMusicPrompt": "Subir nueva música", "uploadAmbientPrompt": "Subir nuevo sonido ambiental", "uploading": "Subiendo...", "uploadSuccess": "¡Archivo {{fileName}} subido con éxito!", "uploadError": "Error durante la subida", "previewError": "No se pudo reproducir la vista previa del audio", "cannotDeleteDefault": "Los archivos predeterminados no se pueden eliminar", "confirmDeleteTitle": "Confirmar <PERSON>", "confirmDeleteMessage": "¿Estás seguro de que quieres eliminar este archivo?", "deleteSuccess": "Archivo eliminado con éxito", "deleteError": "Error durante la eliminación"}, "audioConfig": {"musicTrack": "Pista de música", "musicTitle": "Música de Fondo", "musicTooltip": "Elige música de fondo para acompañar tu sesión. Puedes ajustar el volumen.", "ambientSound": "Sonido ambiental:", "ambientTitle": "Sonido Ambiental", "ambientTooltip": "Añade sonidos naturales o ambientales para crear la atmósfera perfecta.", "brainwavePresets": "<PERSON><PERSON> (Latido):", "testVoiceText": "Esta es una prueba de la voz seleccionada.", "testVoiceError": "No se pudo completar la prueba.", "binauralBeats": "Ritmos Binaurales / Isocrónicos", "binauralTooltip": "Genera sonidos para influir en tus ondas cerebrales. Se requieren auriculares para un efecto binaural óptimo.", "binauralBeatsSetup": {"volume": "Volumen Binaural:"}, "baseFrequency": "Frecuencia Base (Hz)", "baseFrequencyPreset": "Preajuste Frecuencia Base:", "beatFrequency": "Frecuencia de Ritmo (Hz)", "beatFrequencyPreset": "Preajuste Frecuencia de Ritmo (Onda Cerebral):", "customOption": "Personalizado", "customFrequencyDescPlaceholder": "Frecuencia personalizada seleccionada o ningún preajuste activo.", "selectPresetToSeeDescription": "Selecciona un preajuste para ver su descripción.", "headphonesRequired": "Se requieren auriculares para el efecto binaural", "baseFreqPresets": {"solfeggio": {"category": "Frecuencias Solfeggio", "174": {"label": "174 Hz - Fundación", "desc": "Asociada con el anclaje, la seguridad y el alivio del dolor. Ayuda a crear una fundación estable."}, "285": {"label": "285 Hz - Regeneración Tisular", "desc": "Vinculada a la curación y regeneración de tejidos a nivel celular. Promueve el rejuvenecimiento."}, "396": {"label": "396 Hz (Ut) - Liberando Culpa y Miedo", "desc": "Ayuda a liberar la culpa, el miedo y los bloqueos subconscientes. Apoya el anclaje y el empoderamiento."}, "417": {"label": "417 Hz (Re) - Facilitando el Cambio", "desc": "Ayuda a limpiar experiencias traumáticas y facilitar el cambio positivo. Limpia la energía negativa."}, "528": {"label": "528 Hz (Mi) - Transformación y Milagros", "desc": "Conocida como la 'frecuencia del amor'. Asociada con la reparación del ADN, aumento de energía y claridad."}, "639": {"label": "639 Hz (Fa) - Conectando y Relaciones", "desc": "Promueve la armonía en las relaciones, la comprensión, la tolerancia y el amor. Mejora la comunicación."}, "741": {"label": "741 Hz (Sol) - Despertar Intuición y Expresión", "desc": "Vinculada a la limpieza de toxinas, el despertar de la intuición y la promoción de la autoexpresión."}, "852": {"label": "852 Hz (La) - Regreso al Orden Espiritual", "desc": "Ayuda a despertar la fuerza interior y la autorrealización. Conecta con un orden espiritual superior."}, "963": {"label": "963 Hz (Si) - Conciencia Divina y Unidad", "desc": "Asociada con el despertar al estado perfecto, la unidad y la conexión con la conciencia divina."}}, "chakras": {"category": "Frecuencias de los Chakras", "root": {"label": "Chakra Raíz (~256 Hz)", "desc": "Relacionado con el anclaje, la seguridad, los instintos de supervivencia y la vitalidad física."}, "sacral": {"label": "Chakra Sacro (~288 Hz)", "desc": "Gobierna la creatividad, las emociones, la sexualidad y el placer."}, "solarPlexus": {"label": "Chakra Plexo Solar (~320 Hz)", "desc": "Centro del poder personal, la autoestima y la fuerza de voluntad."}, "heart": {"label": "Chakra <PERSON> (~341.3 Hz)", "desc": "Relacionado con el amor, la compasión, el perdón y el equilibrio emocional."}, "throat": {"label": "Chakra Garganta (~384 Hz)", "desc": "Centro de la comunicación, la autoexpresión y la verdad."}, "thirdEye": {"label": "Chakra Tercer <PERSON> (~426.7 Hz)", "desc": "Gobierna la intuición, la perspicacia, la sabiduría y las habilidades psíquicas."}, "crown": {"label": "Chakra Corona (~480 Hz)", "desc": "Conecta con la espiritualidad, la conciencia divina y la iluminación."}}, "planetary": {"category": "Frecuencias Planetarias", "om": {"label": "OM / A<PERSON> (136.10 Hz) - Alineación", "desc": "La frecuencia OM, correspondiente al período orbital de la Tierra. Usada para un anclaje profundo y alineación espiritual."}, "sun": {"label": "Sol (126.22 Hz) - Vitalidad", "desc": "Promueve la vitalidad, la alegría y el sentido de uno mismo. Asociado con la intuición y la fuerza vital."}, "earth": {"label": "Tierra (Día) (194.18 Hz) - Anclaje", "desc": "Para el anclaje, la estabilidad y la conexión con la energía de la Tierra. Promueve el equilibrio físico."}, "moon": {"label": "Luna (210.42 Hz) - Emociones", "desc": "Relacionado con las emociones, la sensibilidad y el ciclo femenino. Apoya el flujo emocional."}}, "organsDetailed": {"category": "Frecuencias de Órganos y Glándulas (Detallado)", "pineal": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (662 Hz)", "desc": "Una frecuencia citada específicamente para la resonancia de la glándula pineal, distinta de frecuencias espirituales más amplias."}, "pituitary": {"label": "Glándula Pituitaria (636 Hz)", "desc": "Apoya la función pituitaria, el equilibrio hormonal y la intuición superior."}, "brain_general": {"label": "Cerebro (Resonancia General - 330 Hz)", "desc": "Una frecuencia de resonancia general a veces asociada con la salud y actividad general del cerebro."}}, "otherNotable": {"category": "<PERSON><PERSON>s Notable<PERSON>", "432hz": {"label": "432 Hz - Afinación Natural", "desc": "Considerada por algunos como una frecuencia de afinación más natural, alineada con el universo. Promueve la calma."}}}, "beatPresets": {"delta": {"category": "Ondas Delta (0.5-4 Hz) - Sueño Profundo", "2_5hz": {"label": "2.5 Hz - <PERSON><PERSON> del Dolor y Relajación", "desc": "Asociado con la liberación de endorfinas, puede ayudar en el alivio del dolor e inducir una relajación profunda."}}, "theta": {"category": "<PERSON><PERSON> (4-8 Hz) - Meditación Profunda", "5hz": {"label": "5 Hz - Intuición y Recuerdo de Sueños", "desc": "Estimula la intuición, la creatividad; ideal para la meditación profunda y el acceso a los recuerdos de los sueños."}, "7_83hz": {"label": "7.83 Hz - <PERSON><PERSON><PERSON><PERSON> (Tierra)", "desc": "<PERSON><PERSON><PERSON><PERSON> principal. Promueve el anclaje, la reducción del estrés y una sensación de conexión."}}, "alpha": {"category": "Ondas Alfa (8-12 Hz) - Enfoque <PERSON>", "10hz": {"label": "10 Hz - Pico Alfa (Aprendizaje y Serenidad)", "desc": "Pico Alfa. Mejora el aprendizaje, la memoria, la serenidad y reduce la ansiedad. Elevador del estado de ánimo."}}, "beta": {"category": "Ondas Beta (12-38 Hz) - Pensamiento Activo", "14hz": {"label": "14 Hz - Enfoque Activo (SMR)", "desc": "Aumenta la concentración, la alerta; ideal para la resolución de problemas y el pensamiento analítico."}}, "gamma": {"category": "<PERSON><PERSON> Gamma (38Hz+) - Rendimiento Máximo", "40hz": {"label": "40 Hz - Rendimiento Óptimo y Perspicacia", "desc": "Procesamiento de información de alto nivel, percepción aumentada, resolución de problemas complejos."}}, "spiritualStates": {"category": "Estados Espirituales y Conciencia", "pinealActivationTheta": {"label": "Sintonización Pineal (Theta - 7.5Hz)", "desc": "Ondas Theta para meditación profunda dirigida a armonizar y estimular suavemente la glándula pineal."}, "chakraCleansing": {"label": "Limpieza de Chakras (Theta - 6Hz)", "desc": "Ondas Theta para apoyar la meditación profunda para la limpieza energética y alineación de los chakras."}, "astralProjection": {"label": "<PERSON>yuda Proyección Astral (Theta - 6.5Hz)", "desc": "Ondas Theta a menudo asociadas con la inducción de estados propicios para experiencias extracorporales."}, "kundaliniSupport": {"label": "<PERSON><PERSON><PERSON> (Alfa/Theta - 8Hz)", "desc": "Frecuencia límite Alfa-Theta para apoyar prácticas meditativas dirigidas a despertar la energía Kundalini."}, "merkabaMeditation": {"label": "Meditación Merkaba (Alfa - 10.5Hz)", "desc": "Ondas Alfa para ayudar en las meditaciones de activación del Merkaba (cuerpo de luz)."}}, "cognitiveEnhancement": {"category": "Mejora Cognitiva y Bienestar", "creativityBoostTheta": {"label": "Impulso Creatividad (Theta - 5.5Hz)", "desc": "Ondas Theta para mejorar la perspicacia, la inspiración y la resolución creativa de problemas."}, "anxietyReductionAlpha": {"label": "Reducción Ansiedad (Alpha - 10Hz)", "desc": "Ondas Alfa máximas para promover la calma, reducir el estrés y aliviar la ansiedad."}, "sleepImprovementDelta": {"label": "Mejora del Sueño (Delta - 2Hz)", "desc": "Ondas Delta para entrenar al cerebro hacia patrones de sueño profundo y reparador."}}}, "music": {"none": "Ninguna"}, "ambient": {"none": "<PERSON><PERSON><PERSON>"}, "noDescription": "No hay descripción disponible", "presets": {"chakras": "Chakras", "organs": "Ó<PERSON><PERSON>", "otherNotable": "Otras frecuencias notables"}, "webAudioNotSupported": "La API de Web Audio no es compatible con tu navegador.", "ttsTitle": "Texto a Voz", "ttsTooltip": "Ajusta el volumen de la voz del guía. El tipo de voz y el idioma se gestionan en los ajustes generales de la aplicación.", "musicSound": "Música:", "volume": "Volumen", "selectPreset": "-- <PERSON><PERSON><PERSON><PERSON><PERSON> un preset --", "selectState": "-- Seleccionar un estado --", "targetFrequencyInfo": "<PERSON><PERSON><PERSON>q: {{leftEar}} Hz, <PERSON><PERSON><PERSON>: {{rightEar}} Hz"}, "errors": {"missingPostId": "Falta el ID de la publicación.", "paymentError": "Ocurrió un error al procesar tu pago.", "manageSubscriptionError": "No se pudo acceder a la gestión de tu suscripción.", "postNotFound": "Publicación no encontrada.", "cantLoadPost": "No se pudo cargar la publicación.", "cantLoadComments": "No se pudieron cargar los comentarios.", "cantAddComment": "Error al añadir el comentario.", "cantAddPost": "Error al publicar la publicación.", "cantLoadSessions": "No se pudieron cargar los datos de las sesiones.", "encryptionFailed": "Falló el cifrado", "cantLoadJournal": "No se pudieron cargar las entradas del diario.", "cantLoadUserData": "No se pudieron cargar tus datos personales.", "blogLoadLocalFailed": "Error al cargar los datos del blog desde el almacenamiento local.", "blogSyncFailed": "Error al sincronizar con el servidor.", "postSaveFailed": "Tu publicación no pudo guardarse localmente.", "commentSaveFailed": "Tu comentario no pudo guardarse localmente.", "commentLoadFailed": "Error al cargar los comentarios.", "userNotAuthenticated": "Debes iniciar sesión para subir archivos.", "cantLoadAssets": "No se pudieron cargar los archivos de audio.", "cloudVoicesPremium": "Las voces en la nube son una función premium. Seleccione el proveedor Navegador o actualice.", "cantLoadDictionary": "No se pudieron cargar los datos del diccionario."}, "warnings": {"decryptionFailed": "No se pudieron descifrar los datos. Podrían estar corruptos o ser de una sesión anterior.", "missingEncryptionKey": "Alerta de seguridad: La clave de cifrado no está definida. Los datos se almacenarán en texto plano."}, "player": {"sessionEnded": "Sesión terminada.", "readyToStart": "Listo para empezar...", "audioSettings": "<PERSON><PERSON><PERSON><PERSON>", "volumeControls": "<PERSON><PERSON><PERSON><PERSON>", "music": "Música", "ambient": "Ambiente", "voice": "Voz", "binaural": "Sonidos Binaurales"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "appLanguage": "Idioma de la App", "appLanguageInfo": "Esto cambia el idioma de toda la aplicación.", "audio": "Audio", "theme": "<PERSON><PERSON>", "lightMode": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON>", "language": "Idioma", "voice": "Voz", "autoVoice": "Voz automática", "testVoice": "Probar voz", "saveConfig": "Guardar Configuración", "ttsSectionTitle": "Texto a Voz (TTS)", "ttsProvider": "Proveedor de TTS", "ttsTestText": "Esta es una prueba de texto a voz.", "ttsTestError": "Error al probar la voz", "downloadingVoice": "Descargando voz...", "voiceDownloaded": "Voz <PERSON>", "noVoiceForSelection": "No hay voz disponible para esta selección", "noSpecificVoiceForLang": "No hay voz específica para este idioma. Aquí están todas las voces disponibles:", "explanationsTitle": "Explicaciones", "audioAssetsManagementTitle": "Gestión de Archivos de Audio", "audioAssetsInfo": "Gestiona tu música y sonidos ambientales personalizados", "goToAudioAssets": "Gestionar Archivos de Audio", "ttsProviderBrowser": "Navegador (Sin conexión y Rápido)", "ttsProviderCloud": "Google Cloud (Alta Calidad)", "premiumOnly": "Solo Premium", "providerLabels": {"browser": "<PERSON><PERSON><PERSON><PERSON>", "cloud": "Nube (IA)"}, "ttsProviderInfo": {"browser": "Usa las voces integradas del navegador", "cloud": "Voces de IA de alta calidad (Próximamente...)"}, "modal": {"saveSuccessTitle": "Configuración Guardada", "saveSuccessMessage": "<PERSON><PERSON> ajustes se han guardado con éxito", "testErrorTitle": "<PERSON><PERSON><PERSON>"}}, "test": {"newSongTitle": "Título de la nueva canción", "addNewSong": "Añadir nueva canción"}, "sync": {"offline": "Sin conexión", "syncing": "Sincronizando...", "error": "Error de sinc ({{count}})", "pending": "{{count}} pendiente(s)", "synchronized": "Sincronizado", "syncedMinutesAgo": "Sinc hace {{minutes}}min", "syncedHoursAgo": "Sinc hace {{hours}}h", "online": "En línea", "clickToSync": "Clic para sincronizar"}, "loading": {"user": "Cargando información del usuario...", "profile": "Cargando perfil...", "blog": "Cargando blog...", "comments": "Cargando comentarios...", "post": "Cargando publicación...", "content": "Cargando contenido...", "stats": "Cargando estadísticas...", "sessions": "Cargando sesion<PERSON>...", "session": "Cargando sesione...", "journal": "Cargando tu diario...", "default": "Cargando...", "authenticating": "Autenticando...", "language": "Configurando idioma...", "initializing": "Iniciali<PERSON><PERSON> da<PERSON>...", "category": "Cargando...", "pseudo": "Generando...", "audioAssets": "Cargando archivos de audio...", "categories": "Cargando categorías...", "voices": "Cargando voces..."}, "plans": {"free": {"title": "Plan Gratuito", "price": "$0", "currentPlan": "Tu Plan Actual", "switchToFree": "Cambiar a Plan Gratuito"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Facturado mensualmente, cancela en cualquier momento.", "manageSub": "Gestionar Suscripción", "subscribe": "Actualizar a Premium"}, "billing": {"month": "mes"}}, "features": {"free": {"baseMeditations": "Acceso a meditaciones e historias básicas", "backgroundMusic": "Música de fondo y voces TTS básicas", "stats": "Estadísticas de progreso", "blog": "Acceso al blog de la comunidad"}, "premium": {"allSessions": "Acceso ilimitado a TODAS las sesiones (hipnosis, PNL, etc.)", "ambientSounds": "Sonidos ambientales avanzados y ritmos binaurales", "customSessions": "Creación de sesiones personalizadas", "games": "Acceso a minijuegos de atención plena", "journal": "Diario de seguimiento detallado", "motivationNotifs": "Notificaciones motivacionales personalizadas", "calendar": "Calendario y programas personalizados (próximamente)", "customAudio": "Posibilidad de usar tus propios sonidos y música", "noAds": "Experiencia sin anuncios", "prioritySupport": "Soporte prioritario"}}, "legal": {"privacy": "Política de Privacidad", "terms": "Términos y Condiciones"}, "profile": {"title": "<PERSON>", "notConnectedTitle": "Perfil de Usuario", "pleaseLogin": "Por favor, inicia sesión para acceder a tu perfil.", "publicPseudo": "<PERSON><PERSON><PERSON><PERSON>", "regeneratePseudo": "Generar nuevo seudónimo", "preferencesTitle": "Preferencias", "appLanguage": "Idioma de la App", "grammaticalGenderLabel": "¿Cómo prefieres que nos dirijamos a ti en los guiones?", "grammaticalGenderInfo": "Esto nos ayudará a adaptar algunos textos para una experiencia más personalizada.", "accountActionsTitle": "Gestión de la Cuenta", "logout": "<PERSON><PERSON><PERSON>", "deleteAccount": "Eliminar mi cuenta", "deleteConfirmTitle": "Confirmar <PERSON>", "deleteConfirmMessage": "¿Estás seguro de que quieres eliminar tu cuenta? Todos tus datos, incluyendo tu progreso y entradas de diario, serán eliminados permanentemente. Esta acción es irreversible.", "accountDeletedSuccess": "Tu cuenta y todos tus datos han sido eliminados.", "memberSince": "Miembro desde el {{date}}", "statsTitle": "Tu Actividad", "stats": {"sessionsCompleted": "Sesiones completadas", "daysStreak": "Días consecutivos", "totalMinutes": "Minutos totales", "noData": "Aún no has completado ninguna sesión. ¡Inicia una sesión para ver tus estadísticas aquí!", "sessionsCompletedDesc": "Número total de sesiones que has completado.", "completionsPlural": "completadas", "completionsSingular": "completada"}}, "gender": {"masculine": "<PERSON><PERSON><PERSON><PERSON>", "feminine": "Femenino", "neutral": "Neutro"}, "sessionTypes": {"hypnosis": "Hipnosis", "meditation": "Meditación", "training": "Entrenamiento", "story": "Historia", "journaling": "Diario", "visualization": "Visualización", "relaxation": "Relajación", "coaching": "Coaching", "sleep induction": "Inducción del Sueño", "sleep-induction": "Inducción del Sueño", "roleplay": "<PERSON><PERSON>", "affirmation": "Afirmación", "gratitude practice": "Práctica de Gratitud", "gratitude-practice": "Práctica de Gratitud", "breathwork": "Respiración", "motivational speech": "Discurso Motivacional", "motivational-speech": "Discurso Motivacional", "guided imagery": "<PERSON><PERSON><PERSON>", "guided-imagery": "<PERSON><PERSON><PERSON>", "problem solving": "Resolución de Problemas", "problem-solving": "Resolución de Problemas", "creative writing": "Escritura Creativa", "creative-writing": "Escritura Creativa", "mindful movement": "Movimiento Consciente", "mindful-movement": "Movimiento Consciente", "self-compassion": "Autocompasión", "focus enhancement": "Mejora del Enfoque", "focus-enhancement": "Mejora del Enfoque", "silence": "<PERSON><PERSON><PERSON>", "beginner": "Principiante", "work_break": "Pausa en el trabajo"}, "sessionTypesDescriptions": {"hypnosis": "Una sesión guiada para inducir un estado profundo de relajación y sugestión, utilizando un tono lento y repetitivo para reducir el estrés o apuntar a metas subconscientes como la confianza o el cambio de hábitos.", "meditation": "Una sesión guiada de atención plena o relajación centrada en la respiración, la conciencia corporal o la calma mental, con un tono tranquilizador y pausas frecuentes para mejorar la presencia.", "training": "Una sesión enérgica para motivar y guiar un entrenamiento físico o mental, entregando instrucciones claras y ánimo para actividades como rutinas de ejercicio o tareas de productividad.", "story": "Una sesión narrativa inmersiva, como una historia de fantasía o aventura, con descripciones vívidas y un tono cautivador para involucrar la imaginación del oyente.", "journaling": "Una sesión introspectiva que guía al oyente a través de preguntas o pautas de reflexión, con un tono tranquilo y alentador y pausas para permitir la escritura de respuestas.", "visualization": "Una sesión guiada para crear imágenes mentales vívidas, como visualizar metas o escenas tranquilas, utilizando un tono descriptivo e inmersivo para mejorar el enfoque.", "relaxation": "Una sesión centrada en la relajación física y mental, utilizando un tono suave y un ritmo lento para liberar la tensión y promover un descanso profundo.", "coaching": "Una sesión motivacional que ofrece orientación y estrategias para el crecimiento personal o profesional, con un tono optimista y empoderador para inspirar la acción.", "sleep induction": "Una sesión calmante diseñada para ayudar al oyente a conciliar el sueño, utilizando un tono lento y tranquilizador con imágenes suaves y una cadencia progresiva para fomentar el descanso.", "roleplay": "Una sesión narrativa interactiva que sitúa al oyente en un rol (por ejemplo, un explorador, un detective), con un tono dinámico y atractivo y pausas para respuestas imaginadas.", "affirmation": "Una sesión que entrega declaraciones positivas y empoderadoras para aumentar la confianza o la mentalidad, utilizando un tono claro y edificante con pausas para que las afirmaciones se asimilen.", "gratitude practice": "Una sesión que guía al oyente a reflexionar sobre las cosas por las que está agradecido, con un tono cálido y reflexivo y pausas para fomentar una profunda conexión emocional.", "breathwork": "Una sesión que guía técnicas de respiración específicas para reducir el estrés o aumentar la energía, con un tono constante y rítmico e instrucciones claras para el ritmo de la respiración.", "motivational speech": "Una sesión inspiradora que entrega un discurso poderoso para aumentar la determinación y el enfoque, utilizando un tono apasionado y edificante para energizar al oyente.", "guided imagery": "Una sesión que guía al oyente a través de escenas mentales detalladas (por ejemplo, una playa pacífica), con un tono descriptivo y tranquilizador para mejorar la relajación o la creatividad.", "problem solving": "Una sesión que guía al oyente a través de pasos estructurados para abordar un desafío personal o profesional, con un tono claro y de apoyo y pausas para la reflexión.", "creative writing": "Una sesión que proporciona pautas o escenarios para inspirar la escritura creativa, con un tono imaginativo y alentador y pausas para que el oyente escriba.", "mindful movement": "Una sesión que guía movimientos físicos suaves (por ejemplo, yoga o estiramientos) con un tono tranquilo e instructivo, sincronizado con señales de respiración.", "self-compassion": "Una sesión que fomenta la autocompasión a través de reflexiones guiadas y afirmaciones, con un tono cálido y nutritivo para promover la sanación emocional.", "silence": "Una sesión de silencio completo, que permite al oyente concentrarse internamente, meditar o simplemente disfrutar de sonidos ambientales, ritmos binaurales, música o una combinación de estos durante una duración establecida.", "focus enhancement": "Una sesión diseñada para mejorar la concentración y la claridad mental, utilizando un tono constante y motivador con técnicas como el anclaje o intervalos de enfoque cronometrados."}, "premium": {"actions": {"subscribe": "Suscribirse a Premium", "watchAd": "Ver anuncio para desbloquear", "watch": "<PERSON><PERSON>", "loadingAd": "Cargando anuncio...", "stopPreview": "Detener", "preview": "Vista Previa", "delete": "Eliminar", "upload": "Subir", "subscribeShort": "Suscribir", "manageShort": "Gestionar", "watchShort": "<PERSON><PERSON>"}, "ads": {"comingSoon": "¡Esta función estará disponible próximamente! G<PERSON><PERSON> por tu paciencia."}, "features": {"advancedSessions": {"title": "<PERSON><PERSON><PERSON>", "description": "Desbloquea el acceso exclusivo a sesiones avanzadas como hipnosis y entrenamientos especializados."}, "ambient": {"title": "Desbloquear Sonidos Ambientales", "description": "Mejora tus sesiones con una biblioteca de sonidos ambientales relajantes como lluvia, bosques u océanos."}, "binaural": {"title": "Desbloquear Ritmos Binaurales", "description": "Accede a una amplia gama de frecuencias para influir en tus ondas cerebrales para una meditación profunda, concentración o relajación."}, "custom": {"titleInList": "Subir Audio Personalizado"}}}, "main_menu": {"open_menu_aria_label": "<PERSON><PERSON><PERSON>"}, "adReward": {"unlockItemTitle": "Desbloquear Función", "unlockItemDescription": "Mira un breve anuncio para desbloquear esta función por tiempo limitado.", "unlockNow": "Desbloquear con Anuncio", "watching": "Viendo anuncio..."}, "subscription": {"title": "Estado de la Suscripción", "upgradePrompt": "Desbloquea todas las funciones actualizando a un plan premium.", "upgradeButton": "Actualizar a Premium", "manage": "Gestionar Suscripción", "status": {"free": "Plan Gratuito", "premium": "Premium Activo", "trial": "Prue<PERSON> G<PERSON>uita"}, "trial": {"endsIn": "Tu prueba termina en {{count}} días.", "endsToday": "¡Tu prueba termina hoy!", "ended": "Tu período de prueba ha terminado."}}, "recommendationKeywords": {"calm": "calma|serenidad|paz|aliviar|relajar|ansiedad|estrés", "focus": "enfocar|concentrar|atención|alerta|claridad|rendimiento|meta", "energy": "energía|motivación|vitalidad|dinamismo|fuerza|poder", "comfort": "consuelo|calmar|tristeza|compasión|dulzura|emoción", "creativity": "creatividad|inspiración|idea|imaginar|solución|problema", "sleep": "sueño|dormir|noche|insomnio|conciliar el sueño", "approach": {"guided": "guiada|historia|voz|hipnosis|meditación", "explore": "explorar|pensamientos|diario|escribir|pregunta", "visualize": "visualizar|imaginar|sueño|objetivo|lugar", "body": "cuerpo|respiración|movimiento|sensación|físico"}}, "recommendationAssistant": {"title": "<PERSON><PERSON><PERSON>", "results_title": "Basado en nuestra conversación, aquí tienes una sugerencia:", "default_reason": "Esta sesión está diseñada para ayudarte a recentrarte y encontrar la calma.", "q_initial": ["Bienvenido/a. <PERSON> ayudarte a encontrar lo que necesitas, dime, ¿cómo te sientes ahora mismo?", "Hola. Encontremos la sesión adecuada para ti. ¿Cuál es la emoción principal que sientes hoy?"], "affirm_propose_somatic": ["<PERSON><PERSON><PERSON> por compartir. Conectemos con el cuerpo. A menudo, las emociones se manifiestan como sensaciones físicas.", "Te entiendo. Exploremos cómo se manifiesta este sentimiento en tu cuerpo."], "q_somatic_location": ["¿En qué parte de tu cuerpo parece centrarse este sentimiento?", "Si escaneas tu cuerpo, ¿dónde está más presente esta sensación?"], "q_verification": ["Enton<PERSON>, una sensación de {{context}}. ¿Te parece correcto?", "De acuerdo, entonces estamos notando {{context}}. ¿Es una descripción precisa?"], "affirm_propose_cognitive": ["<PERSON> acuerdo, exploremos la mente. Nuestros pensamientos y sentimientos están profundamente conectados.", "Entendido. Veamos los pensamientos que podrían estar acompañando a este sentimiento."], "q_cognitive_pattern": ["¿Cuál de estos patrones de pensamiento resuena más con lo que estás experimentando?", "¿Cómo suena la voz en tu cabeza en este momento?"], "affirm_propose_behavioral": ["Eso es útil. Veamos cómo este sentimiento podría estar influyendo en tus acciones.", "De acuerdo. A veces, nuestros sentimientos se traducen en comportamientos específicos. Veámoslo."], "q_behavioral_pattern": ["¿Cómo se ha manifestado este sentimiento en tus acciones recientemente?", "¿Alguno de estos comportamientos recientes te resulta familiar?"], "affirm_propose_contextual": ["Entendido. Dónde estamos puede influir en cómo nos sentimos. Exploremos el contexto.", "G<PERSON><PERSON>. <PERSON><PERSON>, consideremos las situaciones en las que este sentimiento tiende a aparecer."], "q_contextual_trigger": ["¿En qué área de tu vida parece desencadenarse más este sentimiento?", "¿Cuándo suele aparecer este sentimiento?"], "affirm_propose_metaphorical": ["De acuerdo, probemos un enfoque diferente. A veces, las imágenes hablan más que las palabras.", "Demos un paso atrás por un momento y usemos nuestra imaginación."], "q_metaphorical_image": ["Si este sentimiento fuera un paisaje o un objeto, ¿cuál de estos sería?", "¿Cuál de estas imágenes representa mejor tu estado interior actual?"], "affirm_propose_energetic": ["G<PERSON><PERSON>. Sintonicecmos con un nivel más sutil. Hablemos de tu energía.", "De acuerdo. Más allá de los pensamientos y los sentimientos, está tu energía vital."], "q_energetic_sensation": ["¿Cómo describirías tu energía personal en este momento?", "¿Cuál de estas opciones describe mejor tu estado energético actual?"], "q_outcome": ["Esto ha sido muy revelador. Gracias. Para terminar, ¿cuál sería el resultado ideal para ti después de una sesión?", "De acuerdo, con todo esto en mente, ¿qué estado esperas cultivar?"], "opt_idk": "No estoy seguro/a", "opt_idk_or_other": "Ninguna de estas / No estoy seguro/a", "opt_surprise_me": "Sorpréndeme", "opt_yes": "Sí, es correcto", "opt_no": "No, no exactamente", "opt_pivot_cognitive_from_somatic": "No está realmente en mi cuerpo, más bien en mis pensamientos...", "opt_pivot_metaphorical_from_cognitive": "Es difícil de describir con palabras, es más bien una imagen...", "opt_behavior_procrastination": "Dejar las cosas para después", "opt_behavior_isolation": "Aislarme de los demás", "opt_behavior_irritability": "Estar fácilmente irritable", "opt_behavior_avoidance": "Evitar situaciones difíciles", "opt_context_work": "En el trabajo o relacionado con él", "opt_context_relationships": "En mis relaciones", "opt_context_home": "En casa", "opt_context_internal": "<PERSON>iene de adentro, sin importar el contexto", "opt_metaphor_storm": "Una tormenta interior", "opt_metaphor_fog": "Una niebla espesa", "opt_metaphor_weight": "Un peso pesado", "opt_metaphor_knot": "Un nudo apretado", "opt_energy_stuck": "Atascada o estancada", "opt_energy_overactive": "Hiperactiva o acelerada", "opt_energy_drained": "Agotada o sin energía", "opt_energy_scattered": "Dispersa o desenfocada"}, "voiceSelector": {"groupTitle": {"en-AU": "<PERSON><PERSON><PERSON><PERSON> (Australia)", "en-GB": "Inglés (Reino Unido)", "en-IN": "<PERSON><PERSON><PERSON><PERSON> (India)", "en-US": "Inglés (Estados Unidos)", "es-ES": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)", "es-US": "Español (Estados Unidos)", "fr-CA": "<PERSON><PERSON><PERSON><PERSON> (Canadá)", "fr-FR": "<PERSON><PERSON><PERSON><PERSON> (Francia)"}}}