// src/LangProvider.tsx

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from './hooks/useAuth';
import { useAppStore, UserPreferences } from './store/useAppStore';
import { fetchTranslations } from './utils/translationUtils';

export type Language = 'fr' | 'en' | 'es';
const LANG_STORAGE_KEY = 'app_lang';
const DEFAULT_LANG: Language = 'en';

interface LangContextType {
  lang: Language;
  setLang: (l: Language) => void;
  isLangLoading: boolean;
}

export const LangContext = createContext<LangContextType>({
  lang: DEFAULT_LANG,
  setLang: () => console.warn('setLang called before LangProvider was initialized'),
  isLangLoading: true,
});

export const useLang = () => useContext(LangContext);

export const LangProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const { user } = useAuth();
  const userPreferences = useAppStore((state) => state.preferences);
  const updatePreferences = useAppStore((state) => state.updatePreferences);
  const isStoreInitialized = useAppStore((state) => state.isInitialized);

  const [currentLang, setCurrentLang] = useState<Language>(i18n.language as Language);
  const [isLangLoading, setIsLangLoading] = useState(true);
  
  // --- FIX: Use useRef instead of useState to prevent dependency loops ---
  const fullyLoadedLanguages = useRef(new Set<Language>());
  
  const loadingRef = useRef<string | null>(null);

  const loadAndSetLanguage = useCallback(async (langToLoad: Language) => {
    if (loadingRef.current === langToLoad) return;
    
    setIsLangLoading(true);
    loadingRef.current = langToLoad;
    
    console.log(`[LangProvider] Processing language: ${langToLoad}`);

    try {
      // Access the Set via .current
      if (!fullyLoadedLanguages.current.has(langToLoad)) {
        console.log(`[LangProvider] Fetching FULL language bundle for: ${langToLoad}`);
        const translations = await fetchTranslations(langToLoad);
        i18n.addResourceBundle(langToLoad, 'translation', translations, true, true);
        // Mutate .current directly. This does not cause a re-render.
        fullyLoadedLanguages.current.add(langToLoad);
        console.log(`[LangProvider] Full bundle for '${langToLoad}' loaded.`);
      }

      if (i18n.language !== langToLoad) {
        await i18n.changeLanguage(langToLoad);
      }
      
      setCurrentLang(langToLoad);
      localStorage.setItem(LANG_STORAGE_KEY, langToLoad);
      console.log(`[LangProvider] Successfully set language to '${langToLoad}'.`);
    } catch (error) {
      console.error(`[LangProvider] Failed to load language '${langToLoad}'.`, error);
    } finally {
      setIsLangLoading(false);
      loadingRef.current = null;
    }
  }, [i18n]); // Dependency on fullyLoadedLanguages is removed, making the function stable

  // Effect 1: On mount, ensure the FULL bundle for the initial language is loaded.
  useEffect(() => {
    const initialLang = i18n.language as Language;
    console.log(`[LangProvider] Component mounted. Ensuring full bundle for initial language: '${initialLang}'`);
    loadAndSetLanguage(initialLang);
  }, []); // The dependency array is now empty (as loadAndSetLanguage is stable), ensuring it runs only ONCE.

  // Effect 2: Sync with user preferences when they become available.
  useEffect(() => {
    const preferredLang = userPreferences?.ttsConfig?.lang;
    if (isStoreInitialized && user && preferredLang && preferredLang !== currentLang) {
      console.log(`[LangProvider] User preferences found. Switching to preferred language: ${preferredLang}`);
      loadAndSetLanguage(preferredLang);
    }
  }, [user, isStoreInitialized, userPreferences, currentLang, loadAndSetLanguage]);

  const handleSetLang = async (newLang: Language) => {
    if (newLang === currentLang || loadingRef.current === newLang) return;
    
    await loadAndSetLanguage(newLang);
    
    if (user?.uid) {
      const existingTtsConfig = userPreferences?.ttsConfig || {};
      const newTtsConfig: UserPreferences['ttsConfig'] = {
        provider: 'browser', 
        voice: 'auto',       
        volume: 1.0,         
        ...existingTtsConfig, 
        lang: newLang,       
      };
      
      await updatePreferences(user.uid, { ttsConfig: newTtsConfig });
      console.log(`[LangProvider] User language preference updated to '${newLang}'.`);
    }
  };
  
  const contextValue = {
    lang: currentLang,
    setLang: handleSetLang,
    isLangLoading: isLangLoading,
  };

  return (
    <LangContext.Provider value={contextValue}>
      {children}
    </LangContext.Provider>
  );
};