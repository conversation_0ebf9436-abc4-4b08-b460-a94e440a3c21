// src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';

import App from './App';
import reportWebVitals from './reportWebVitals';
import { ThemeProvider } from './ThemeProvider';
import { LangProvider } from './LangProvider';
import GlobalStyles from './GlobalStyles';
import './i18n';


const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <LangProvider>
      <ThemeProvider>
        <GlobalStyles />
        <App />
      </ThemeProvider>
    </LangProvider>
  </React.StrictMode>
);

reportWebVitals();