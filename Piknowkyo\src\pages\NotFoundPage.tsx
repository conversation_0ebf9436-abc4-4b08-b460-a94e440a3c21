import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FiHome, FiAlertCircle } from 'react-icons/fi';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.text};
`;

const ErrorCode = styled.h1`
  font-size: 6rem;
  font-weight: bold;
  color: ${({ theme }) => theme.primary};
  margin: 0;
  opacity: 0.8;
`;

const ErrorMessage = styled.p`
  font-size: 1.2rem;
  margin: 1rem 0 2rem 0;
  color: ${({ theme }) => theme.textSecondary};
`;

const HomeButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: ${({ theme }) => theme.primary};
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.primary};
    opacity: 0.9;
    transform: translateY(-1px);
  }
`;

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Container>
      <FiAlertCircle size={80} style={{ marginBottom: '1rem', opacity: 0.6 }} />
      <ErrorCode>404</ErrorCode>
      <ErrorMessage>{t('notFound.message')}</ErrorMessage>
      <HomeButton to="/">
        <FiHome />
        {t('notFound.backHome')}
      </HomeButton>
    </Container>
  );
};

export default NotFoundPage;
