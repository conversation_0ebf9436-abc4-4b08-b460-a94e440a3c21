// src/games/zen-tetris/GameComponent.tsx

import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { GameProps, SavedGameState } from '../common/models';
import { initializeTetrisGame, updateGame, move<PERSON><PERSON>ce, rotatePiece, TetrisGameState, GRID_WIDTH, GRID_HEIGHT } from './logic';
import GameModal from '../common/components/GameModal';
import GameTimer from '../common/components/GameTimer';
import ScoreDisplay from '../common/components/ScoreDisplay';

import {
  GameContainer, TetrisBoard, Cell, SidePanel, NextPieceDisplay, InfoDisplay,
  PauseButton, MainControls, getPieceColor, GameArea, MobileControls, ControlButton
} from './styles';
import { Fi<PERSON>lock, FiAward, FiHash, FiRotateCcw, FiArrowLeft, FiArrowRight, FiArrowDown, FiMenu, FiArrowUp, FiRotateCw } from 'react-icons/fi';
import { ThemeContext, DefaultTheme } from 'styled-components';
import { useOrientation } from './hooks/useOrientation';
import { hapticFeedback, isHapticSupported } from './utils/haptics';
import OrientationHint from './components/OrientationHint';
import { useAppStore } from '../../store/useAppStore'; // <-- Import the store

const GAME_ID = "zen-tetris";

const ZenTetrisGame: React.FC<GameProps> = ({
  userId, onGameEnd, onGameQuit, onPauseChange, initialGameState
}) => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;
  
  // --- Zustand Store Actions ---
  const saveGameProgress = useAppStore(state => state.saveGameProgress);
  const clearGameProgress = useAppStore(state => state.clearGameProgress);

  const [gameState, setGameState] = useState<TetrisGameState>(() => {
    if (initialGameState && initialGameState.specificGameState) {
      const loadedState = initialGameState.specificGameState;
      // Make sure we parse the date correctly if it exists
      const lastPlayed = initialGameState.lastPlayed ? new Date(initialGameState.lastPlayed) : new Date();
      return {
        ...initializeTetrisGame(loadedState.level),
        ...loadedState,
        lastPlayed,
        currentPiece: loadedState.currentPiece ? { ...loadedState.currentPiece } : null,
        nextPiece: loadedState.nextPiece ? { ...loadedState.nextPiece } : null,
        gameOver: false,
        isDropping: false,
      };
    }
    return initializeTetrisGame();
  });

  const [isPaused, setIsPaused] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(() => {
    // Logic to show rules only if it's not a resumed game
    return !initialGameState;
  });
  const [currentTimerSeconds, setCurrentTimerSeconds] = useState(initialGameState?.currentTimerSeconds || 0);

  const gameStateRef = useRef(gameState);
  gameStateRef.current = gameState;

  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const gameLoopIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const boardRef = useRef<HTMLDivElement>(null);

  const orientation = useOrientation();
  const hapticEnabled = isHapticSupported();

  // --- Game Management Functions ---

  const clearTimers = useCallback(() => {
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    if (gameLoopIntervalRef.current) clearInterval(gameLoopIntervalRef.current);
    timerIntervalRef.current = null;
    gameLoopIntervalRef.current = null;
  }, []);

  const resumeGameLoop = useCallback(() => {
    clearTimers();

    if (gameStateRef.current.gameOver || isPaused || showRulesModal) {
        return;
    }

    timerIntervalRef.current = setInterval(() => {
      setCurrentTimerSeconds(prev => prev + 1);
    }, 1000);

    const dropInterval = Math.max(100, 1000 - (gameStateRef.current.level - 1) * 9);

    gameLoopIntervalRef.current = setInterval(() => {
      setGameState(prev => {
        if (prev.gameOver || isPaused || showRulesModal) {
             clearTimers();
             return prev;
        }
        const newState = updateGame(prev);
        if (newState.gameOver) {
          clearTimers();
          onGameEnd(newState.score, newState.level, currentTimerSeconds);
          clearGameProgress(userId, GAME_ID); // <-- Use store action
        }
        return newState;
      });
    }, dropInterval);
  }, [isPaused, showRulesModal, onGameEnd, userId, currentTimerSeconds, clearTimers, clearGameProgress]);


  const startGame = useCallback(() => {
    setShowRulesModal(false);
    setIsPaused(false);
    onPauseChange(false);

    if (!initialGameState || (initialGameState && showRulesModal)) {
      setGameState(initializeTetrisGame());
      setCurrentTimerSeconds(0);
    }
  }, [initialGameState, onPauseChange, showRulesModal]);

  const pauseGame = useCallback(() => {
    setIsPaused(true);
    onPauseChange(true);
    clearTimers();

    saveGameProgress(userId, GAME_ID, { // <-- Use store action
      score: gameStateRef.current.score,
      level: gameStateRef.current.level,
      currentTimerSeconds: currentTimerSeconds,
      specificGameState: gameStateRef.current,
      lastPlayed: new Date(),
    });
  }, [currentTimerSeconds, onPauseChange, userId, clearTimers, saveGameProgress]);

  const resumeGame = useCallback(() => {
    setIsPaused(false);
    onPauseChange(false);
  }, [onPauseChange]);

  const restartGame = useCallback(() => {
    clearGameProgress(userId, GAME_ID); // <-- Use store action
    setGameState(initializeTetrisGame());
    setCurrentTimerSeconds(0);
    setIsPaused(false);
    onPauseChange(false);
    setShowRulesModal(false);
  }, [userId, onPauseChange, clearGameProgress]);

  const quitGame = useCallback(() => {
    clearTimers();
    if (!gameStateRef.current.gameOver) {
      saveGameProgress(userId, GAME_ID, { // <-- Use store action
        score: gameStateRef.current.score,
        level: gameStateRef.current.level,
        currentTimerSeconds: currentTimerSeconds,
        specificGameState: gameStateRef.current,
        lastPlayed: new Date(),
      });
    } else {
      clearGameProgress(userId, GAME_ID); // <-- Use store action
    }
    onGameQuit();
  }, [userId, currentTimerSeconds, onGameQuit, clearTimers, saveGameProgress, clearGameProgress]);

  // --- Touch Controls Functions ---
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isPaused || gameState.gameOver || showRulesModal) return;

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }, [isPaused, gameState.gameOver, showRulesModal]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (isPaused || gameState.gameOver || showRulesModal || !touchStartRef.current) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    const minSwipeDistance = 30;
    const maxTapTime = 200;

    if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance && deltaTime < maxTapTime) {
      if (hapticEnabled) hapticFeedback.medium();
      setGameState(prev => rotatePiece(prev));
    } else {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        if (deltaX > minSwipeDistance) {
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'right'));
        } else if (deltaX < -minSwipeDistance) {
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'left'));
        }
      } else {
        if (deltaY > minSwipeDistance) {
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'down'));
        }
      }
    }

    touchStartRef.current = null;
  }, [isPaused, gameState.gameOver, showRulesModal, hapticEnabled]);

  const handleControlButtonPress = useCallback((action: 'left' | 'right' | 'down' | 'rotate') => {
    if (isPaused || gameState.gameOver || showRulesModal) return;

    if (hapticEnabled) {
      if (action === 'rotate') {
        hapticFeedback.medium();
      } else {
        hapticFeedback.light();
      }
    }

    setGameState(prev => {
      switch (action) {
        case 'left': return movePiece(prev, 'left');
        case 'right': return movePiece(prev, 'right');
        case 'down': return movePiece(prev, 'down');
        case 'rotate': return rotatePiece(prev);
        default: return prev;
      }
    });
  }, [isPaused, gameState.gameOver, showRulesModal, hapticEnabled]);

  // --- Effects ---

  useEffect(() => {
    return () => {
      clearTimers();
      onPauseChange(false);
    };
  }, [clearTimers, onPauseChange]);

  useEffect(() => {
    if (!gameState.gameOver && !isPaused && !showRulesModal) {
      resumeGameLoop();
    } else {
      clearTimers();
    }
    return clearTimers;
  }, [gameState.gameOver, isPaused, showRulesModal, resumeGameLoop, clearTimers]);


  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isPaused || gameStateRef.current.gameOver || showRulesModal) return;
      if (['ArrowLeft', 'ArrowRight', 'ArrowDown', 'ArrowUp', ' '].includes(event.key)) {
        event.preventDefault();
      }

      setGameState(prev => {
        let newState = prev;
        switch (event.key) {
          case 'ArrowLeft':
            newState = movePiece(prev, 'left');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowRight':
            newState = movePiece(prev, 'right');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowDown':
            newState = movePiece(prev, 'down');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowUp':
          case ' ':
            newState = rotatePiece(prev);
            if (isHapticSupported()) hapticFeedback.medium();
            break;
          case 'Escape':
            pauseGame();
            break;
        }
        return newState;
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPaused, showRulesModal, pauseGame]);


  const getRenderGrid = useCallback(() => {
    if (!gameState.currentPiece) return gameState.grid;
    const renderGrid = gameState.grid.map(row => [...row]);
    gameState.currentPiece.shape.forEach((row, y) => {
      row.forEach((cell, x) => {
        if (cell !== 0) {
          const gridY = gameState.currentPiece!.y + y;
          const gridX = gameState.currentPiece!.x + x;
          if (gridY >= 0 && gridY < GRID_HEIGHT && gridX >= 0 && gridX < GRID_WIDTH) {
            renderGrid[gridY][gridX] = gameState.currentPiece!.color;
          }
        }
      });
    });
    return renderGrid;
  }, [gameState]);

  const renderGrid = getRenderGrid();

  const handleStartFromRules = () => {
    setShowRulesModal(false);
    startGame();
  };

  return (
    <GameContainer>
      <OrientationHint />

      {!gameState.gameOver && !showRulesModal && (
        <PauseButton onClick={pauseGame} title={t('game.pauseButton', 'Mettre en pause')}>
          <FiMenu />
        </PauseButton>
      )}

      <GameArea>
        <SidePanel>
          <ScoreDisplay score={gameState.score} />
          <GameTimer timeInSeconds={currentTimerSeconds} />
          <InfoDisplay>
            <h4>{t('game.info', 'Infos')}</h4>
            <p><FiHash /> {t('game.level', 'Niveau')}: {gameState.level}</p>
            <p><FiAward /> {t('game.lines', 'Lignes')}: {gameState.linesCleared}</p>
          </InfoDisplay>
          {gameState.nextPiece && (
            <NextPieceDisplay>
              <h4>{t('game.nextPiece', 'Prochaine Pièce')}</h4>
              <div className="preview-grid">
                {Array(4).fill(0).map((_, rIdx) => (
                  Array(4).fill(0).map((_2, cIdx) => {
                    const isPieceCell = gameState.nextPiece &&
                      rIdx < gameState.nextPiece.shape.length &&
                      cIdx < gameState.nextPiece.shape[rIdx].length &&
                      gameState.nextPiece.shape[rIdx][cIdx] !== 0;
                    return (
                      <Cell
                        key={`next-${rIdx}-${cIdx}`}
                        $colorIndex={isPieceCell ? gameState.nextPiece!.color : 0}
                      />
                    );
                  })
                ))}
              </div>
            </NextPieceDisplay>
          )}
        </SidePanel>

        <TetrisBoard
          ref={boardRef}
          rows={GRID_HEIGHT}
          cols={GRID_WIDTH}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {renderGrid.map((row, rIdx) =>
            row.map((cell, cIdx) => (
              <Cell key={`${rIdx}-${cIdx}`} $colorIndex={cell || 0} />
            ))
          )}
        </TetrisBoard>
      </GameArea>

      <MainControls className="desktop-only">
        <button onClick={() => handleControlButtonPress('left')} title={t('game.moveLeft', 'Déplacer à gauche')}>
          <FiArrowLeft />
        </button>
        <button onClick={() => handleControlButtonPress('down')} title={t('game.softDrop', 'Chute douce')}>
          <FiArrowDown />
        </button>
        <button onClick={() => handleControlButtonPress('right')} title={t('game.moveRight', 'Déplacer à droite')}>
          <FiArrowRight />
        </button>
        <button onClick={() => handleControlButtonPress('rotate')} title={t('game.rotate', 'Rotation')}>
          <FiRotateCcw />
        </button>
      </MainControls>

      <MobileControls className="mobile-only">
        <div className="control-row">
          <ControlButton onClick={() => handleControlButtonPress('left')} title={t('game.moveLeft', 'Déplacer à gauche')} className="move-button">
            <FiArrowLeft />
          </ControlButton>
          <ControlButton onClick={() => handleControlButtonPress('down')} title={t('game.softDrop', 'Chute douce')} className="drop-button">
            <FiArrowDown />
          </ControlButton>
          <ControlButton onClick={() => handleControlButtonPress('right')} title={t('game.moveRight', 'Déplacer à droite')} className="move-button">
            <FiArrowRight />
          </ControlButton>
        </div>
        <div className="control-row">
          <ControlButton onClick={() => handleControlButtonPress('rotate')} title={t('game.rotate', 'Rotation')} className="rotate-button">
            <FiRotateCw />
          </ControlButton>
        </div>
      </MobileControls>

      <GameModal
        isOpen={showRulesModal || isPaused || gameState.gameOver}
        title={showRulesModal ? t('game.modal.rulesTitle', 'Règles du Jeu') :
               isPaused ? t('game.modal.pausedTitle', 'Jeu en Pause') :
               t('game.modal.gameOverTitle', 'Partie Terminée !')}
        showStartButton={showRulesModal}
        showResumeButton={isPaused && !showRulesModal && !gameState.gameOver}
        showReturnButton={true}
        showRestartButton={isPaused || gameState.gameOver}
        onStart={handleStartFromRules}
        onRestart={restartGame}
        onResume={resumeGame}
        onReturn={quitGame}
      >
        {showRulesModal && (
          <div>
            <p>{t('game.zenTetris.rules1', 'Empilez les blocs pour former des lignes complètes et marquez des points. La vitesse augmente avec les niveaux !')}</p>
            <p>{t('game.zenTetris.rules2', 'Contrôles :')}</p>
            <div style={{ marginBottom: '1rem' }}>
              <h4 style={{ margin: '0.5rem 0', color: theme.accent }}>{t('game.controls.keyboard')} :</h4>
              <ul style={{ margin: '0.5rem 0', paddingLeft: '20px' }}>
                <li><FiArrowLeft /> : {t('game.zenTetris.ruleMoveLeft', 'Déplacer à gauche')}</li>
                <li><FiArrowRight /> : {t('game.zenTetris.ruleMoveRight', 'Déplacer à droite')}</li>
                <li><FiArrowDown /> : {t('game.zenTetris.ruleSoftDrop', 'Chute douce (soft drop)')}</li>
                <li><FiArrowUp /> (ou <FiRotateCcw />) : {t('game.zenTetris.ruleRotate', 'Rotation')}</li>
                <li>`Esc` : {t('game.zenTetris.rulePause', 'Pause')}</li>
              </ul>
            </div>
            <div>
              <h4 style={{ margin: '0.5rem 0', color: theme.accent }}>{t('games.zenTetris.controls', 'Contrôles tactiles')} :</h4>
              <ul style={{ margin: '0.5rem 0', paddingLeft: '20px' }}>
                <li>👆 {t('games.zenTetris.touchTap', 'Tap rapide : Rotation')}</li>
                <li>⬅️ {t('games.zenTetris.touchLeft', 'Glisser gauche : Déplacer à gauche')}</li>
                <li>➡️ {t('games.zenTetris.touchRight', 'Glisser droite : Déplacer à droite')}</li>
                <li>⬇️ {t('games.zenTetris.touchDown', 'Glisser bas : Chute douce')}</li>
                <li>🔘 {t('games.zenTetris.touchButtons', 'Boutons de contrôle en bas')}</li>
              </ul>
            </div>
          </div>
        )}
        {isPaused && !showRulesModal && !gameState.gameOver && (
          <p>{t('game.modal.pausedMessage', 'Votre partie est en pause. Reprenez quand vous êtes prêt.')}</p>
        )}
        {gameState.gameOver && (
          <div>
            <p>{t('game.modal.gameOverMessage', 'Bien joué ! Votre score final est de {{score}} et vous avez atteint le niveau {{level}}.', { score: gameState.score, level: gameState.level })}</p>
          </div>
        )}
      </GameModal>
    </GameContainer>
  );
};

export default ZenTetrisGame;