import { db } from '../firebase';
import { User as FirebaseUser } from "firebase/auth";
import { collection, addDoc, getDoc, updateDoc, doc, query, onSnapshot, QuerySnapshot, type DocumentData, setDoc, serverTimestamp, Timestamp } from "firebase/firestore";
import type { User } from '../models/user.model';
import { IUser } from '../models/user';

// Create a new user
export const addUser = async (user: Omit<User, 'id'>): Promise<string> => {
  try {
    console.log('add user', db);
    const docRef = await addDoc(collection(db, "users"), user);
    return docRef.id;
  } catch (error) {
    console.error("Error adding user:", error);
    throw error;
  }
};

export const ensureUserDocument = async (firebaseUser: FirebaseUser): Promise<User | null> => {
  const userRef = doc(db, "users", firebaseUser.uid);
  const userSnap = await getDoc(userRef);

  if (!userSnap.exists()) {
   const newUser: IUser = {
      uid: firebaseUser.uid,
      email: firebaseUser.email ?? "",
      name: firebaseUser.displayName ?? "",
      createdAt: Timestamp.now(), 
    };

    await setDoc(userRef, newUser);
    return newUser as User;
  }
  else{
    return getUser(firebaseUser.uid);
  }
};

// Read a single user
export const getUser = async (userId: string): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId));
    if (userDoc.exists()) {
      return { uid: userDoc.id, ...userDoc.data() } as User;
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error getting user:", error);
    throw error;
  }
};

// Update a user
export const updateUser = async (userId: string, updates: Partial<User>): Promise<void> => {
  try {
    await updateDoc(doc(db, "users", userId), updates);
  } catch (error) {
    console.error("Error updating user:", error);
    throw error;
  }
}

export const getUsers = (callback: (users: User[]) => void): () => void => {
  const q = query(collection(db, "users"));

  const unsubscribe = onSnapshot(
    q,
    (querySnapshot: QuerySnapshot<DocumentData>) => {
      const users = querySnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data(),
      }) as User);
      callback(users);
    },
    (error) => {
      console.error("🔥 Firestore realtime error in getUsers:", error);
    }
  );

  return unsubscribe;
};
