import React, { useEffect, useState } from 'react';
import Toast from '../components/Toast';
import { useNetworkStatus } from '../services/useNetworkStatus';

const NetworkStatusNotifier: React.FC = () => {
  const online = useNetworkStatus();
  const [show, setShow] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (online) {
      setMessage('Connexion rétablie');
      setShow(true);
      setTimeout(() => setShow(false), 2000);
    } else {
      setMessage('Vous êtes hors ligne');
      setShow(true);
    }
  }, [online]);

  return show ? <Toast message={message} /> : null;
};

export default NetworkStatusNotifier;
