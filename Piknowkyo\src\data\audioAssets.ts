// src/data/audioAssets.ts
import { AudioAsset, AudioManifest } from '../models';

// src/data/audioAssets.ts (ou où votre fonction est définie)
// import { storage, db } from '../services/firebase'; // Vos instances Firebase
// import { ref, deleteObject } from "firebase/storage";
// import { doc, deleteDoc } from "firebase/firestore";

export interface DefaultAudioAsset {
    id: string;
    name: string;
    url: string; // Chemin relatif dans /public/assets/
    type: 'music' | 'ambient';
  }
  
  // Fonction pour charger les assets par défaut depuis le manifest JSON
  export const fetchAudioAssets = async (): Promise<AudioManifest> => {
    try {
      const response = await fetch('/assets/audio_manifests/audio_manifest.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      // Transformer les URL relatives en URL absolues si nécessaire pour la lecture
      const transformPaths = (assets: DefaultAudioAsset[]): DefaultAudioAsset[] => {
          return assets.map(asset => ({
              ...asset,
              url: asset.url.startsWith('/') ? asset.url : `/assets/audio/${asset.type === 'music' ? 'musics' : 'ambiants'}/${asset.url}`
          }));
      };
      return {
          musics: transformPaths(data.musics || []),
          ambiants: transformPaths(data.ambiants || []),
      };
    } catch (error) {
      console.error("Could not fetch audio manifest:", error);
      return { musics: [], ambiants: [] }; // Retourner des listes vides en cas d'erreur
    }
  };
  
  
  // Placeholder - à implémenter avec Firebase
  export const uploadAudioAsset = async (file: File, type: 'musics' | 'ambiants', userId: string): Promise<AudioAsset> => {
    console.log(`Simulating upload of ${file.name} for user ${userId} as ${type}`);
    // Dans une vraie app:
    // 1. Créer une référence dans Firebase Storage (ex: `users/${userId}/${type}/${file.name}`)
    // const storageRef = ref(storage, `users/${userId}/${type}/${Date.now()}_${file.name}`);
    // 2. Uploader le fichier
    // await uploadBytes(storageRef, file);
    // 3. Obtenir l'URL de téléchargement
    // const downloadURL = await getDownloadURL(storageRef);
    // 4. Enregistrer les métadonnées dans Firestore (users/{userId}/audioAssets/{assetId})
    // const assetId = doc(collection(db, `users/${userId}/audioAssets`)).id;
    // await setDoc(doc(db, `users/${userId}/audioAssets`, assetId), {
    //   id: assetId,
    //   name: file.name,
    //   url: downloadURL,
    //   type: type === 'musics' ? 'music' : 'ambient',
    //   isUserUploaded: true,
    //   storagePath: storageRef.fullPath, // IMPORTANT
    //   userId: userId,
    //   createdAt: serverTimestamp()
    // });
  
    // Simulation:
    const assetId = `user_${type}_${Date.now()}`;
    return new Promise(resolve => setTimeout(() => resolve({
      id: assetId,
      name: file.name,
      url: URL.createObjectURL(file), // URL locale pour la démo
      type: type === 'musics' ? 'music' : 'ambient',
      isUserUploaded: true,
      storagePath: `users/${userId}/${type}/${file.name}`, // Chemin simulé
    }), 1000));
  };
  
  // CORRECTION DE LA SIGNATURE ICI :
  export const deleteUserAudioAsset = async (
    storagePath: string, // Le chemin complet dans Firebase Storage
    assetId: string,     // L'ID de l'asset dans Firestore (pour supprimer le document)
    assetType: 'musics' | 'ambiants', // Ajouté pour savoir quelle collection cibler si nécessaire (ou utiliser directement assetId)
    userId: string       // L'UID de l'utilisateur propriétaire
  ): Promise<void> => {
    console.log(`Simulating delete of asset with storagePath: ${storagePath}, id: ${assetId}, type: ${assetType} for user ${userId}`);
    // Dans une vraie app:
    // 1. Supprimer le fichier de Firebase Storage
    // const fileRef = ref(storage, storagePath);
    // await deleteObject(fileRef);
    // 2. Supprimer le document de Firestore
    // await deleteDoc(doc(db, `users/${userId}/audioAssets`, assetId));
    
    return new Promise(resolve => setTimeout(resolve, 500));
  };
  
  // Vous pourriez avoir besoin d'une fonction pour fetch les assets d'un utilisateur depuis Firestore
  // export const fetchUserAudioAssets = async (userId: string, type: 'musics' | 'ambiants'): Promise<AudioAsset[]> => {
  //   const assets: AudioAsset[] = [];
  //   const q = query(collection(db, `users/${userId}/audioAssets`), where("type", "==", type === 'musics' ? 'music' : 'ambient'));
  //   const querySnapshot = await getDocs(q);
  //   querySnapshot.forEach((doc) => {
  //     assets.push(doc.data() as AudioAsset);
  //   });
  //   return assets;
  // };
  