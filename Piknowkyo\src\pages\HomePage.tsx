// src/pages/HomePage.tsx

import React, { useState, useContext, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';

import AppIcon from '../components/AppIcon';
import QuickAccessSettingsModal from '../components/QuickAccessSettingsModal';
import { useTheme } from '../ThemeProvider';
import { useAppStore, QuickAccessLink, SessionScript } from '../store/useAppStore';
import { GAMES_LIST } from '../games/gameData'; // Import GAMES_LIST

import heroBgLight from '../assets/images/hero-background_light.webp';
import heroBgDark from '../assets/images/hero-background_dark.webp';

// --- STYLED COMPONENTS ---
const PageContainer = styled.div`
  padding: 0 1rem 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

interface HeroSectionProps {
  $bgimage?: string;
}

const HeroSection = styled.section<HeroSectionProps>`
  text-align: center;
  padding: 3rem 1rem;
  min-height: 40vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-image: ${({ $bgimage, theme }: { $bgimage?: string; theme: DefaultTheme }) =>
    $bgimage
      ? `linear-gradient(rgba(0,0,0,0.35), rgba(0,0,0,0.35)), url(${$bgimage})`
      : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`
  };
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  color: ${({ theme }) => theme.textLight || '#fff'};
  box-shadow: ${({ theme }) => theme.headerShadow || '0 4px 12px rgba(0,0,0,0.1)'};
  position: relative;
  margin: 0 -1rem 2.5rem -1rem;

  h1, p, a { position: relative; z-index: 2; }
  h1 { font-size: 2.2rem; margin-bottom: 0.5rem; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.4); }
  p { font-size: 1.05rem; max-width: 600px; margin: 0 auto 1.5rem auto; line-height: 1.6; text-shadow: 0 1px 3px rgba(0,0,0,0.3); }
`;

const PrimaryButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  background: ${({ theme }) => theme.surface || '#fff'};
  color: ${({ theme }) => theme.primary};
  padding: 0.7rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  position: relative;
  z-index: 2;
  &:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.25); }
  svg { margin-left: 0.5rem; }
`;

const Section = styled.section`
  margin-bottom: 2.5rem;
  padding: 0 0.5rem;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.2rem;
  position: relative;

  h2 {
    font-size: 1.6rem;
    color: ${({ theme }) => theme.primary};
    margin: 0;
  }
`;

const SettingsButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.textMuted};
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s, background-color 0.2s;
  
  &:hover {
    color: ${({ theme }) => theme.primary};
    background-color: ${({ theme }) => theme.surfaceAlt};
  }
`;

const QuickAccessGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
`;

const QuickAccessItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem 1rem;
  color: ${({ theme }) => theme.text};
  text-decoration: none;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  min-height: 120px;
  cursor: pointer;
  border: none;
  width: 100%;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 5px 15px rgba(0,0,0,0.08)'};
  }
  
  svg {
    font-size: 2rem;
    margin-bottom: 0.8rem;
    color: ${({ theme }) => theme.primary};
  }
  
  span {
    font-weight: 500;
    font-size: 0.95rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
`;

const WelcomeText = styled.p`
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
`;

type LinkItem = { key: string; iconName: string; label: string; to: string; };
type ButtonItem = { key: string; iconName: string; label: string; onClick: () => void; };
type RenderableQuickAccessItem = LinkItem | ButtonItem;

// --- COMPONENT IMPLEMENTATION ---

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const currentTheme = useContext(ThemeContext) as DefaultTheme;

  const profile = useAppStore(state => state.profile);
  const preferences = useAppStore(state => state.preferences);
  const allScripts = useAppStore(state => state.sessions.scripts);

  const [isSettingsModalOpen, setSettingsModalOpen] = useState(false);

  const heroImageUrl = darkMode ? heroBgDark : heroBgLight;

  const quickAccessItems = useMemo((): RenderableQuickAccessItem[] => {
    const links = preferences?.quickAccessLinks;
    if (!links) return [];

    const allSessions: SessionScript[] = Object.values(allScripts).flat();

    const mappedItems = links.map((link: QuickAccessLink): RenderableQuickAccessItem | null => {
      const key = `${link.type}-${link.id}`;
      switch (link.type) {
        case 'page':
          return {
            key,
            to: link.id === 'audio-assets' ? '/settings/audio-assets' : `/${link.id}`,
            iconName: link.id,
            label: t(`navigation.${link.id}`),
          };
        case 'session_category':
          return {
            key,
            to: `/sessions?category=${link.id}`,
            iconName: link.id,
            label: t(`sessionTypes.${link.id}`),
          };
        case 'session':
          const session = allSessions.find(s => s.id === link.id);
          if (!session) return null;
          return {
            key,
            to: `/sessions/${link.id}`,
            iconName: session.type,
            label: session.title,
          };
        case 'tool':
          if (link.id === 'recommendation') {
            return {
              key,
              onClick: () => navigate('/sessions?action=recommend'),
              iconName: 'recommendation',
              label: t('navigation.recommendation'),
            };
          }
          if (link.id === 'favorites') {
            return {
              key,
              to: '/sessions?filter=favorites',
              iconName: 'favorites',
              label: t('navigation.favorites'),
            };
          }
          return null;
        case 'game_category': // Handle the main Games category
          return {
            key,
            to: '/games', // Link to the main games page
            iconName: 'games', // General icon for games
            label: t('navigation.games'), // Label for the games section
          };
        case 'game': // Handle individual game links
          const game = GAMES_LIST.find(g => g.id === link.id);
          if (!game) return null;
          return {
            key,
            to: `/games?gameId=${game.id}`, // Link directly to the game with its ID
            iconName: game.icon,
            label: t(game.titleKey),
          };
        default:
          return null;
      }
    });
    
    return mappedItems.filter((item): item is RenderableQuickAccessItem => item !== null);
  }, [preferences, allScripts, t, navigate]);

  return (
    <PageContainer>
      <QuickAccessSettingsModal isOpen={isSettingsModalOpen} onClose={() => setSettingsModalOpen(false)} />
      
      <HeroSection $bgimage={heroImageUrl}>
        <h1>{t('home.title', 'Unlock Your Inner Potential')}</h1>
        <p>{t('home.subtitle', 'Explore guided meditations, hypnosis, and tools for personal growth.')}</p>
        <PrimaryButton to="/sessions">
          {t('home.exploreButton', 'Explore Sessions')}
          <AppIcon name="arrow-right" size={16} />
        </PrimaryButton>
      </HeroSection>

      <Section>
        <WelcomeText>
          {profile?.anonymousPseudo 
            ? t('home.welcomeUser', { name: profile.anonymousPseudo })
            : t('home.welcomeText')}
        </WelcomeText>
      </Section>

      <Section>
        <SectionHeader>
          <h2>{t('home.quickAccess', 'Quick Access')}</h2>
          <SettingsButton onClick={() => setSettingsModalOpen(true)} title={t('home.customizeQuickAccess', 'Customize Quick Access')}>
            <AppIcon name="settings" size={20} />
          </SettingsButton>
        </SectionHeader>
        <QuickAccessGrid>
          {quickAccessItems.map(item => {
            const cardContent = (
              <>
                <AppIcon name={item.iconName} size={32} />
                <span>{item.label}</span>
              </>
            );

            if ('onClick' in item) {
              return <QuickAccessItem as="button" key={item.key} onClick={item.onClick}>{cardContent}</QuickAccessItem>;
            }
            return <QuickAccessItem as={Link} key={item.key} to={item.to}>{cardContent}</QuickAccessItem>;
          })}
        </QuickAccessGrid>
      </Section>

      <Section style={{ textAlign: 'center', marginTop: '3rem' }}>
        <Link to="/about" style={{ color: currentTheme.primary, textDecoration: 'underline' }}>
          {t('home.learnMore', 'Learn more about our approach')}
        </Link>
      </Section>
    </PageContainer>
  );
};

export default HomePage;