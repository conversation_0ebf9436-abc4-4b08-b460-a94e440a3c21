// src/pages/MonetizationPage.tsx

import React, { useState, useMemo, useEffect } from 'react';
import styled, { css } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppStore } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import { useShallow } from 'zustand/react/shallow';
import AppIcon from '../components/AppIcon';
import Button from '../components/ui/Button';
// NOTE: useLang is no longer needed on this page as we don't call Cloud Functions from here anymore.

// --- STRIPE URLS ---
// These are the direct links from your Stripe Dashboard.
const STRIPE_PAYMENT_LINK_URL = 'https://buy.stripe.com/fZu7sMaeG0ks1AIdYq7Vm01';
const STRIPE_CUSTOMER_PORTAL_URL = 'billing.stripe.com/p/login/6oU28seuWebi5QYaMe7Vm00';


// --- Products Configuration (for display purposes only) ---
const products = [
  {
    id: 'free',
    nameKey: 'monetization.plans.free.name',
    price: '0',
    currency: '$',
    billingCycleKey: 'monetization.plans.billing.month',
    features: [
      'monetization.features.free.meditations',
      'monetization.features.free.musicAndTTS',
      'monetization.features.free.stats',
      'monetization.features.free.journal',
      'monetization.features.free.adUnlock',
      'monetization.features.free.support',
    ],
  },
  {
    id: 'premium_monthly',
    nameKey: 'monetization.plans.premium.name',
    price: '9', 
    currency: '$',
    billingCycleKey: 'monetization.plans.billing.month',
    features: [
      'monetization.features.premium.allSessions',
      'monetization.features.premium.cloudTTS',
      'monetization.features.premium.ambientSounds',
      'monetization.features.premium.stats',
      'monetization.features.premium.games',
      'monetization.features.premium.journal',
      'monetization.features.premium.noAds',
    ],
  },
];


// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  h1 {
    font-size: 2.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  p {
    font-size: 1.05rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const SubscriptionStatusCard = styled.div<{ $isTrial?: boolean }>`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1.5rem 2rem;
  border-radius: 16px;
  margin-bottom: 3rem;
  border: 1px solid ${({ theme }) => theme.border};
  box-shadow: ${({ theme }) => theme.cardShadow};
  
  ${({ $isTrial, theme }) => $isTrial && css`
    border: 2px solid ${theme.accent};
    background: linear-gradient(135deg, ${theme.surfaceAlt}, ${theme.accent}15);
  `}
`;

const StatusHeader = styled.h4`
  font-size: 1.4rem;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: ${({ theme }) => theme.primary};
`;

const StatusText = styled.p`
  font-size: 1rem;
  color: ${({ theme }) => theme.textSecondary};
  margin: 0 0 1.2rem 0;
  strong {
    color: ${({ theme }) => theme.text};
    font-weight: 600;
  }
`;

const TrialInfo = styled.div`
  margin-top: 1rem;
  .days-remaining {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
  }
`;

const ProgressBarContainer = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.border};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressBar = styled.div<{ $progress: number }>`
  width: ${({ $progress }) => $progress}%;
  height: 100%;
  background-color: ${({ theme }) => theme.accent};
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
`;


const PlansContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;

  @media (min-width: 800px) {
    grid-template-columns: repeat(2, 1fr);
    align-items: stretch;
  }
`;

const PlanCard = styled.div<{ $isCurrentPlan?: boolean }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 2rem;
  display: flex;
  flex-direction: column;
  border: 2px solid ${({ $isCurrentPlan, theme }) => ($isCurrentPlan ? theme.primary : 'transparent')};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const PlanHeader = styled.div`
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;

  h3 {
    font-size: 1.8rem;
    margin: 0 0 0.5rem 0;
  }
  .price {
    font-size: 2.5rem;
    font-weight: 700;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.25rem;
    span {
      font-size: 1rem;
      font-weight: 400;
      color: ${({ theme }) => theme.textSecondary};
    }
  }
  .billing-cycle {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textMuted};
  }
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
  flex-grow: 1;

  li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    color: ${({ theme }) => theme.textSecondary};
    svg {
      font-size: 1.2rem;
      color: ${({ theme }) => theme.primary};
      flex-shrink: 0;
    }
  }
`;


// --- Component ---

const MonetizationPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const { subscription } = useAppStore(useShallow(state => ({
    subscription: state.subscription,
  })));

  const isPremium = subscription?.isActive ?? false;
  const isTrial = subscription?.isTrialActive ?? false;

  const trialDaysRemaining = useMemo(() => {
    if (!isTrial || !subscription?.trialEnds) return 0;
    const now = new Date();
    const endDate = new Date(subscription.trialEnds);
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }, [isTrial, subscription?.trialEnds]);

  const trialProgress = useMemo(() => {
    if (!isTrial || !subscription?.trialStarts || !subscription?.trialEnds) return 0;
    const start = new Date(subscription.trialStarts).getTime();
    const end = new Date(subscription.trialEnds).getTime();
    const now = new Date().getTime();
    const totalDuration = end - start;
    const elapsed = now - start;
    if (totalDuration <= 0) return 100;
    return Math.min(100, (elapsed / totalDuration) * 100);
  }, [isTrial, subscription?.trialStarts, subscription?.trialEnds]);


  useEffect(() => {
    const query = new URLSearchParams(location.search);
    if (query.get("payment") === "success") {
      console.log("Redirected from a successful payment.");
      navigate(location.pathname, { replace: true });
    }
    if (query.get("payment") === "canceled") {
      console.log("Redirected from a canceled payment.");
      navigate(location.pathname, { replace: true });
    }
  }, [location.search, navigate]);

  const handleSubscribe = () => {
    if (!user || !user.email) {
      alert(t('monetization.errors.authRequired', 'You must be logged in to subscribe.'));
      navigate('/profile');
      return;
    }
    const lang = i18n.language.split('-')[0];
    const url = `${STRIPE_PAYMENT_LINK_URL}?prefilled_email=${encodeURIComponent(user.email)}&locale=${lang}`;
    window.location.assign(url);
  };

  const handleManageSubscription = () => {
    const lang = i18n.language.split('-')[0];
    const url = `${STRIPE_CUSTOMER_PORTAL_URL}?locale=${lang}`;
    window.open(url, '_blank');
  };
  
  const getStatusText = () => {
    if (isTrial) {
        return t('monetization.status.trialActive', 'You are currently on a Premium trial.');
    }
    if (isPremium) {
        if (subscription?.renewsAt) {
            const renewalDate = new Date(subscription.renewsAt);
            const dateOptions: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
            const timeOptions: Intl.DateTimeFormatOptions = { hour: '2-digit', minute: '2-digit' };
            const formattedDate = renewalDate.toLocaleDateString(i18n.language, dateOptions);
            const formattedTime = renewalDate.toLocaleTimeString(i18n.language, timeOptions);
            
            if (subscription?.willBeCanceled) {
                // Pass both 'heure' and 'time' for i18n flexibility
                return t('monetization.status.cancelsOnDateTime', { date: formattedDate, time: formattedTime, heure: formattedTime });
            }
            return t('monetization.status.renewsOnDateTime', { date: formattedDate, time: formattedTime, heure: formattedTime });
        }
        // Fallback if the date is not available for some reason
        return t('monetization.status.renewsOn', { date: t('common.unknown', 'an unknown date') });
    }
    return t('monetization.status.freePlan', 'You are currently on the Free plan.');
  };


  return (
    <PageContainer>
      <PageHeader>
        <h1><AppIcon name="award" /> {t('monetization.title', 'Upgrade Your Experience')}</h1>
        <p>{t('monetization.description', 'Unlock all features to maximize your well-being journey, or continue with our generous free plan.')}</p>
      </PageHeader>
      
      {user && subscription && (
        <SubscriptionStatusCard $isTrial={isTrial}>
          <StatusHeader>
             <AppIcon name="star" /> {t('monetization.status.title')}
          </StatusHeader>
          <StatusText dangerouslySetInnerHTML={{ __html: getStatusText() }} />
          
          {isTrial && (
            <TrialInfo>
              <div className="days-remaining">
                {trialDaysRemaining > 0
                  ? t('monetization.trial.endsIn', { count: trialDaysRemaining })
                  : t('monetization.trial.ended')
                }
              </div>
              <ProgressBarContainer><ProgressBar $progress={trialProgress} /></ProgressBarContainer>
            </TrialInfo>
          )}

          {isPremium ? (
             <Button onClick={handleManageSubscription} variant="secondary">
                <AppIcon name="settings" /> {t('monetization.actions.manage')}
              </Button>
          ) : isTrial ? (
             <Button onClick={handleSubscribe} variant="primary">
                <AppIcon name="award" /> {t('monetization.actions.upgradeNow')}
              </Button>
          ) : null}

        </SubscriptionStatusCard>
      )}

      <PlansContainer>
        {products.map(plan => {
          const isCurrentPlan = (plan.id === 'free' && !isPremium && !isTrial) || (plan.id !== 'free' && isPremium);
          
          return (
            <PlanCard key={plan.id} $isCurrentPlan={isCurrentPlan}>
              <PlanHeader>
                <h3>{t(plan.nameKey, plan.id)}</h3>
                <div className="price">
                  {plan.price}{plan.currency}
                  {plan.id !== 'free' && <span>/{t(plan.billingCycleKey, 'month')}</span>}
                </div>
              </PlanHeader>
              <FeatureList>
                {plan.features.map(featureKey => (
                  <li key={featureKey}>
                    <AppIcon name="check" /> {t(featureKey)}
                  </li>
                ))}
              </FeatureList>

              {plan.id === 'free' ? (
                <Button variant="secondary" disabled>
                  <AppIcon name="check-circle" /> {t('monetization.actions.currentPlan')}
                </Button>
              ) : (
                isPremium ? (
                  <Button onClick={handleManageSubscription} variant="primary">
                    <AppIcon name="settings" /> {t('monetization.actions.manage')}
                  </Button>
                ) : (
                   <Button onClick={handleSubscribe} variant="primary">
                    <AppIcon name="award" /> {t('monetization.actions.subscribe')}
                  </Button>
                )
              )}
            </PlanCard>
          )
        })}
      </PlansContainer>
    </PageContainer>
  );
};

export default MonetizationPage;