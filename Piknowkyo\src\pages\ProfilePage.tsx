// src/pages/ProfilePage.tsx

import React, { useState, useEffect, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Language, useLang } from '../LangProvider';
import { useAuth } from '../hooks/useAuth';
import { useAppStore } from '../store/useAppStore';
import AppIcon from '../components/AppIcon';

// --- Styled Components ---
const PageGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 1100px;
  margin: 0 auto;
  padding: 1.5rem;

  @media (min-width: 992px) {
    grid-template-columns: 1fr 320px;
  }
`;

const MainContent = styled.main`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const Sidebar = styled.aside`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const ProfileHeaderCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 2.5rem 2rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const AvatarPlaceholder = styled.div`
  width: 110px;
  height: 110px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${({ theme }) => theme.primary}, ${({ theme }) => theme.accent});
  color: ${({ theme }) => theme.textLight};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 4px solid ${({ theme }) => theme.surface};
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
`;

const PseudoText = styled.h2`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  font-weight: 600;
  margin: 0;
`;

const UserEmailDisplay = styled.div`
  font-size: 1rem;
  color: ${({ theme }) => theme.textMuted};
  margin: 0.25rem 0 1rem 0;
`;

const MemberSince = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.875rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
`;

const Section = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid ${({ theme }) => theme.border};
`;

const SectionTitle = styled.h3`
  font-size: 1.3rem;
  color: ${({ theme }) => theme.primary};
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.borderSlight};
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  border: 1px solid ${({ theme }) => theme.borderSlight};

  .stat-value {
    font-size: 1.75rem;
    font-weight: bold;
    color: ${({ theme }) => theme.primary};
    display: block;
    line-height: 1.2;
  }
  .stat-label {
    font-size: 0.8rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-top: 0.25rem;
  }
`;

const SubscriptionCard = styled(Section)<{ $isTrial: boolean }>`
  border-left: 5px solid ${({ theme, $isTrial }) => $isTrial ? theme.accent : theme.primary};
`;

const SubscriptionStatus = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  background: ${({ theme }) => theme.primary};
  margin-bottom: 1rem;

  &.trial {
    background: ${({ theme }) => theme.accent};
  }
  &.free {
    background: ${({ theme }) => theme.secondary};
  }
`;

const TrialInfo = styled.div`
  margin: 1rem 0;
  .days-remaining {
    color: ${({ theme }) => theme.text};
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
`;

const ProgressBarContainer = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${({ theme }) => theme.borderSlight};
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressBar = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background: linear-gradient(90deg, ${({ theme }) => theme.primary}, ${({ theme }) => theme.accent});
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
`;

const UpgradePrompt = styled.p`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 1rem 0;
`;

const FormGroup = styled.div`
  margin-bottom: 1.2rem;
  &:last-child { margin-bottom: 0; }
  label {
    display: block;
    font-weight: 500;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }
  select {
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
    font-size: 1rem;
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' | 'ghost' }>`
  background: ${({ theme, $variant }) =>
    $variant === 'danger' ? (theme.errorColor || '#d9534f') :
    $variant === 'secondary' ? theme.secondary :
    $variant === 'ghost' ? 'transparent' :
    theme.primary
  };
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:not(:last-child) { margin-bottom: 0.75rem; }
  &:hover { opacity: 0.85; }
  &:active { transform: scale(0.97); }
  &:disabled { background: ${({ theme }) => theme.disabledBackground}; color: ${({ theme }) => theme.disabledText}; cursor: not-allowed; }
`;

const LoadingContainer = styled.div`
  display: flex; align-items: center; justify-content: center;
  padding: 3rem; font-size: 1.2rem; color: ${({ theme }) => theme.textSecondary};
`;

const ModalOverlay = styled.div`
  position: fixed; top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: flex; align-items: center; justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.surface}; padding: 2rem;
  border-radius: 12px; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: 450px; width: 90%; text-align: center;
  h3 { color: ${({ theme }) => theme.primary}; margin: 0 0 1rem 0; }
  p { color: ${({ theme }) => theme.textSecondary}; margin-bottom: 1.5rem; line-height: 1.6; }
  .actions { display: flex; justify-content: space-around; gap: 1rem; }
`;

const generateUniqueAnonymousPseudo = async (uid: string, t: any): Promise<string> => {
    const adjectives = Object.values(t('pseudoGenerator.adjectives', { returnObjects: true })) as string[];
    const nouns = Object.values(t('pseudoGenerator.nouns', { returnObjects: true })) as string[];
    const generationSeed = `${uid}-${Date.now()}`;
    const wordHash = generationSeed.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const adjectiveIndex = wordHash % adjectives.length;
    const nounIndex = (wordHash + 1) % nouns.length;
    const basePseudo = `${adjectives[adjectiveIndex]} ${nouns[nounIndex]}`;
    const encoder = new TextEncoder();
    const data = encoder.encode(generationSeed);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hexHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return `${basePseudo}#${hexHash.substring(0, 6)}`;
};


const ProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const { lang, setLang } = useLang();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const navigate = useNavigate();

  const { user } = useAuth();
  const subscription = useAppStore(state => state.subscription);
  const profile = useAppStore(state => state.profile);
  const preferences = useAppStore(state => state.preferences);
  const activity = useAppStore(state => state.activity);
  const pricingConfig = useAppStore(state => state.pricingConfig);
  const { updateProfile, updatePreferences } = useAppStore();
  
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const initializeProfile = async () => {
        if (user && profile && !profile.anonymousPseudo) {
            console.log("User profile is missing an anonymous pseudo, creating one...");
            const newPseudo = await generateUniqueAnonymousPseudo(user.uid, t);
            updateProfile(user.uid, { anonymousPseudo: newPseudo });
        }
    };
    initializeProfile();
  }, [user, profile, updateProfile, t]);
  
  const handleGrammaticalGenderChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (!user) return;
    updatePreferences(user.uid, { grammaticalGender: e.target.value as any });
  };
  
  const handleManageSubscription = () => navigate('/monetization');
  const handleLogout = () => navigate('/logout');
  const handleDeleteAccount = async () => {
    setShowDeleteConfirmModal(false);
    setIsSaving(true);
    console.log("Account deletion process initiated...");
    // Simulate API call for deletion
    setTimeout(() => { alert('Account deleted.'); navigate('/'); }, 1500);
  };

  const isLoading = !subscription || !profile || !preferences || !activity;

  if (isLoading) {
    return <LoadingContainer><AppIcon name="loader" /> {t('loading.profile', 'Loading profile...')}</LoadingContainer>;
  }
  
  if (!user) {
    return <LoadingContainer>{t('profile.pleaseLogin', 'Please log in to access your profile.')}</LoadingContainer>;
  }

  // FIX: Use 'trialEnds' instead of 'trialEndDate'
  const { isTrialActive, trialEnds } = subscription;
  const trialDaysTotal = pricingConfig?.trialDays ?? 14;
  // FIX: Use 'trialEnds' instead of 'trialEndDate'
  const trialDaysRemaining = isTrialActive && trialEnds ? Math.max(0, Math.ceil((new Date(trialEnds).getTime() - Date.now()) / (1000 * 60 * 60 * 24))) : 0;
  const trialProgress = isTrialActive ? ((trialDaysTotal - trialDaysRemaining) / trialDaysTotal) * 100 : 0;
  
  const memberSinceDate = user.metadata.creationTime 
    ? new Date(user.metadata.creationTime).toLocaleDateString(lang, { year: 'numeric', month: 'long', day: 'numeric'}) 
    : 'N/A';

  const getStatusText = () => {
    if (subscription.isActive) return t('subscription.status.premium', 'Premium Active');
    if (isTrialActive) return t('subscription.status.trial', 'Free Trial');
    return t('subscription.status.free', 'Free Plan');
  };

  const getStatusClass = () => {
    if (subscription.isActive) return 'active';
    if (isTrialActive) return 'trial';
    return 'free';
  };

  return (
    <PageGrid>
      <MainContent>
        <ProfileHeaderCard>
          <AvatarPlaceholder>{profile.anonymousPseudo ? profile.anonymousPseudo.charAt(0).toUpperCase() : <AppIcon name="user" size={56} />}</AvatarPlaceholder>
          <PseudoText>{profile.anonymousPseudo || '...'}</PseudoText>
          {user.email && <UserEmailDisplay>{user.email}</UserEmailDisplay>}
          <MemberSince>
            <AppIcon name="calendar" size={14} />
            {t('profile.memberSince', { date: memberSinceDate })}
          </MemberSince>
        </ProfileHeaderCard>

        <Section>
            <SectionTitle><AppIcon name="award" /> {t('profile.statsTitle', 'Your Activity')}</SectionTitle>
            <StatsGrid>
              <StatCard>
                <span className="stat-value">{activity?.completedSessions.length ?? 0}</span>
                <div className="stat-label">{t('profile.stats.sessionsCompleted', 'Sessions completed')}</div>
              </StatCard>
              <StatCard><span className="stat-value">{activity?.streak ?? 0}</span><div className="stat-label">{t('profile.stats.daysStreak', 'Consecutive days')}</div></StatCard>
              <StatCard><span className="stat-value">{activity?.totalMinutes ?? 0}</span><div className="stat-label">{t('profile.stats.totalMinutes', 'Total minutes')}</div></StatCard>
            </StatsGrid>
        </Section>
      </MainContent>

      <Sidebar>
        <SubscriptionCard $isTrial={isTrialActive}>
          <SectionTitle><AppIcon name="star" /> {t('subscription.title', 'Subscription Status')}</SectionTitle>
          <SubscriptionStatus className={getStatusClass()}>{getStatusText()}</SubscriptionStatus>

          {isTrialActive && trialDaysRemaining > 0 && (
            <TrialInfo>
              <div className="days-remaining">
                {trialDaysRemaining > 1 
                  ? t('subscription.trial.endsIn', { count: trialDaysRemaining })
                  : t('subscription.trial.endsToday')
                }
              </div>
              <ProgressBarContainer><ProgressBar progress={trialProgress} /></ProgressBarContainer>
            </TrialInfo>
          )}

          {isTrialActive && trialDaysRemaining <= 0 && (
             <UpgradePrompt>{t('subscription.trial.ended')}</UpgradePrompt>
          )}

          {!subscription.isActive && (
            <>
              <UpgradePrompt>{t('subscription.upgradePrompt')}</UpgradePrompt>
              <Button onClick={handleManageSubscription} $variant="primary">
                <AppIcon name="credit-card" /> {t('subscription.upgradeButton', 'Upgrade to Premium')}
              </Button>
            </>
          )}
          
          {subscription.isActive && (
             <Button onClick={handleManageSubscription} $variant="secondary">
                <AppIcon name="credit-card" /> {t('subscription.manage', 'Manage Subscription')}
              </Button>
          )}
        </SubscriptionCard>

        <Section>
          <SectionTitle><AppIcon name="settings" /> {t('profile.preferencesTitle', 'Preferences')}</SectionTitle>
          <FormGroup>
            <label htmlFor="app-language">{t('profile.appLanguage', 'Application Language:')}</label>
            <select id="app-language" value={lang} onChange={e => setLang(e.target.value as Language)}>
              <option value="fr">{t('languages.french')}</option><option value="en">{t('languages.english')}</option><option value="es">{t('languages.spanish')}</option>
            </select>
          </FormGroup>
          <FormGroup>
            <label htmlFor="grammatical-gender">{t('profile.grammaticalGenderLabel', 'How should we address you in scripts?')}</label>
            <select id="grammatical-gender" value={preferences?.grammaticalGender || 'neutral'} onChange={handleGrammaticalGenderChange}>
              <option value="neutral">{t('gender.neutral', 'Neutral')}</option><option value="masculine">{t('gender.masculine', 'Masculine')}</option><option value="feminine">{t('gender.feminine', 'Feminine')}</option>
            </select>
          </FormGroup>
        </Section>

        <Section>
          <SectionTitle><AppIcon name="lock" /> {t('profile.accountActionsTitle', 'Account Management')}</SectionTitle>
          <Button onClick={handleLogout} $variant="secondary"><AppIcon name="logout" /> {t('profile.logout', 'Logout')}</Button>
        </Section>
      </Sidebar>

    </PageGrid>
  );
};

export default ProfilePage;