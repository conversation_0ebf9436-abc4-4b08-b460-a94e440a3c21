import { serverTimestamp, Timestamp } from "firebase/firestore";
import { IUser } from "./user";

export class User implements IUser {
    constructor(props: any) {
        if(props) {
            this.assign(props)
        }
    }

    public uid = '';
    public email = '';
    public name= '';
    public createdAt= Timestamp.now();

    protected assign(props: any): void {
        Object.assign(this, props);
    }
}