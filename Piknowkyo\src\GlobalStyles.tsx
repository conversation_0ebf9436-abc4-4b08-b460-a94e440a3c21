// /src/GlobalStyles.tsx
import { createGlobalStyle } from 'styled-components';

// Fonts (Montserrat, Roboto, Poppins) should be loaded via <link> tag
// in your public/index.html as previously explained.
// Make sure the font names here match the ones being loaded.

const GlobalStyles = createGlobalStyle`
  /* Reset and base */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-size: 16px; // Base for rem units
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif; // Poppins first, then system fallbacks
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: ${({ theme }) => theme.background};
    color: ${({ theme }) => theme.text};
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease; // Transition for theme switching
  }

  /* Basic typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif; // Montserrat for titles
    font-weight: 600; // A common weight for headings
    color: ${({ theme }) => theme.primary}; // Primary color for titles
    margin-top: 1.5rem; // Space before titles
    margin-bottom: 1rem; // Space after titles
    line-height: 1.3; // Line height for better readability
  }

  h1 {
    font-size: 2.2rem; // Adjusted for consistency
  }

  h2 {
    font-size: 1.8rem; // Adjusted
  }

  h3 {
    font-size: 1.5rem;
  }

  p {
    margin-bottom: 1rem;
    font-family: 'Roboto', sans-serif; // Roboto for body text if Poppins is not available
    /* If Poppins is the main body font, you can remove this line
       or place it as a fallback after Poppins in the body.font-family declaration. */
  }

  a {
    color: ${({ theme }) => theme.accent || theme.primary}; // Use accent, otherwise primary for links
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: ${({ theme }) => theme.primary};
      text-decoration: underline; // Underline on hover for better affordance
    }
  }

  button {
    cursor: pointer;
    font-family: 'Poppins', sans-serif; // Poppins for buttons
    border: none;
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight || 'white'}; // Ensure a fallback for textLight
    padding: 0.6rem 1.2rem;
    border-radius: 8px; // Softer rounding
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex; // For better alignment with icons if used
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) { // Do not apply hover effect if disabled
      opacity: 0.85;
      transform: translateY(-1px); // Subtle hover effect
    }

    &:active:not(:disabled) {
      transform: translateY(0px); // Click effect
      opacity: 1;
    }

    &:disabled {
      background: ${({ theme }) => theme.disabledBackground || '#cccccc'};
      color: ${({ theme }) => theme.disabledText || '#666666'};
      cursor: not-allowed;
      transform: none;
      opacity: 0.7;
    }
  }

  /* Enhancements for existing UI elements (if you use them globally) */
  /* These styles are very specific and might be better placed in their respective components
     if .app-container, .app-header etc. are not global classes but styled-components.
     For now, I'm keeping them here assuming you use them as global classes. */

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: ${({ theme }) => theme.surface};
    border-bottom: 1px solid ${({ theme }) => theme.border};
    box-shadow: ${({ theme }) => theme.headerShadow || '0 2px 4px rgba(0,0,0,0.03)'};
    position: sticky;
    top: 0;
    z-index: 100; // Ensure a high enough z-index

    @media (max-width: 768px) {
      padding: 0.75rem 1rem;
    }
  }

  .logo {
    display: flex;
    align-items: center;
    font-weight: 700; // Bolder for the logo
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    font-family: 'Montserrat', sans-serif; // Montserrat for the logo

    img { // Styles for the image inside the logo
      height: 40px; // Fixed image height
      margin-right: 0.75rem;
      transition: transform 0.3s ease;
    }
    
    &:hover img {
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      font-size: 1.25rem;
      img {
        height: 32px;
        margin-right: 0.5rem;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem; // A bit more space
  }

  .theme-toggle, .menu-toggle { // Styles for icon buttons
    background: transparent; // Transparent background
    border: none;
    color: ${({ theme }) => theme.text};
    font-size: 1.3rem; // Icon size
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, color 0.2s ease;

    &:hover {
      background: ${({ theme }) => theme.surfaceAlt || `${theme.primary}1A`}; // Subtle hover color
      color: ${({ theme }) => theme.primary};
      transform: none; // Remove transform from general buttons here
    }
  }

  .app-main {
    flex: 1; /* Allows main content to take up remaining space */
    padding: 1.5rem; // Standard padding
    max-width: 1200px; // Max container for content
    margin: 0 auto; // Center the container
    width: 100%;
    padding-bottom: 4rem; // Space for the footer or end of page

    @media (max-width: 768px) {
      padding: 1rem; // Less padding on mobile
    }
  }

  /* Responsive styles for typography */
  @media (max-width: 768px) {
    h1 {
      font-size: 1.8rem; // Adjusted size for mobile
    }
    h2 {
      font-size: 1.6rem;
    }
    h3 {
      font-size: 1.35rem;
    }
    p {
      font-size: 0.95rem;
    }
  }

  /* Generic cards and containers */
  .card, div[class*="Card"] { // Targets .card and classes containing "Card"
    background: ${({ theme }) => theme.surface};
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem; // Standard spacing for cards
    box-shadow: ${({ theme }) => theme.cardShadow || '0 4px 12px rgba(0,0,0,0.07)'};
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 16px rgba(0,0,0,0.1)'};
    }
  }

  /* Form styles */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    width: 100%;
    /* REMOVED padding: 0.75rem 1rem; to prevent conflicts with component-specific styles */
    margin-bottom: 0rem; // Standard spacing below inputs
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 8px; // Softer rounding
    font-family: 'Poppins', sans-serif;
    font-size: 1rem; // Standard font size for inputs
    background-color: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.primary};
      box-shadow: 0 0 0 3px ${({ theme }) => `${theme.primary}33`}; // More visible focus shadow
    }

    &::placeholder {
      color: ${({ theme }) => theme.textMuted};
      opacity: 0.7;
    }
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); } // Added a slight translation effect
    to { opacity: 1; transform: translateY(0); }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in-out forwards; // 'forwards' to maintain the final state
  }

  /* Custom scrollbar (optional, but often desired) */
  ::-webkit-scrollbar {
    width: 10px; // A bit wider
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.surfaceAlt || theme.background}; // Track background
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.primary};
    border-radius: 10px;
    border: 2px solid ${({ theme }) => theme.surfaceAlt || theme.background}; // Creates a border effect

    &:hover {
      background: ${({ theme }) => theme.accent || `darken(${theme.primary}, 10%)`}; // Darken on hover
    }
  }
`;

export default GlobalStyles;