// src/components/JournalEntryItem.tsx

import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { JournalEntry } from '../models';
import { useAppStore } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import AppIcon from './AppIcon';

// --- Styled Components ---

const EntryCard = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid ${({ theme }) => theme.border};
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  }
`;

const EntryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const EntryDate = styled.p`
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  font-weight: 500;
  margin: 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.primary};
  }
`;

const EntryContent = styled.p`
  font-size: 1rem;
  color: ${({ theme }) => theme.text};
  line-height: 1.7;
  white-space: pre-wrap; /* This respects line breaks and spacing */
  margin: 0;
`;

const EditContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  padding: 0.8rem;
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}30;
  }
`;

const Button = styled.button`
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: 1px solid transparent;

  &.primary {
    background-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border-color: ${({ theme }) => theme.primary};
  }

  &.secondary {
    background-color: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.text};
    border-color: ${({ theme }) => theme.border};
    &:hover {
      background-color: ${({ theme }) => theme.surfaceAlt};
    }
  }
`;

// --- Component ---

interface JournalEntryItemProps {
  entry: JournalEntry;
}

const JournalEntryItem: React.FC<JournalEntryItemProps> = ({ entry }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const updateJournalEntry = useAppStore(state => state.updateJournalEntry);
  const deleteJournalEntry = useAppStore(state => state.deleteJournalEntry);
  
  const [isEditing, setIsEditing] = useState(false);
  const [editedNote, setEditedNote] = useState(entry.note);

  const handleSave = useCallback(async () => {
    if (!user || !editedNote.trim()) return;
    await updateJournalEntry(user.uid, entry.id, { note: editedNote });
    setIsEditing(false);
  }, [user, entry.id, editedNote, updateJournalEntry]);

  const handleDelete = useCallback(async () => {
    if (!user) return;
    const confirmDelete = window.confirm(t('journal.deleteConfirmText'));
    if (confirmDelete) {
      await deleteJournalEntry(user.uid, entry.id);
    }
  }, [user, entry.id, deleteJournalEntry, t]);

  const handleCancel = useCallback(() => {
    setEditedNote(entry.note);
    setIsEditing(false);
  }, [entry.note]);

  return (
    <EntryCard>
      <EntryHeader>
        <EntryDate>
          {new Date(entry.date).toLocaleDateString(undefined, {
            year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
          })}
        </EntryDate>
        {!isEditing && (
          <ActionButtons>
            <IconButton onClick={() => setIsEditing(true)} title={t('journal.edit')}>
              <AppIcon name="edit-2" size={18} />
            </IconButton>
            <IconButton onClick={handleDelete} title={t('journal.delete')}>
              <AppIcon name="trash-2" size={18} />
            </IconButton>
          </ActionButtons>
        )}
      </EntryHeader>

      {isEditing ? (
        <EditContainer>
          <TextArea 
            value={editedNote}
            onChange={(e) => setEditedNote(e.target.value)}
            autoFocus
          />
          <ActionButtons style={{ justifyContent: 'flex-end' }}>
            <Button className="secondary" onClick={handleCancel}>
              {t('journal.cancel')}
            </Button>
            <Button className="primary" onClick={handleSave} disabled={!editedNote.trim()}>
              {t('journal.save')}
            </Button>
          </ActionButtons>
        </EditContainer>
      ) : (
        <EntryContent>{entry.note}</EntryContent>
      )}
    </EntryCard>
  );
};

export default JournalEntryItem;