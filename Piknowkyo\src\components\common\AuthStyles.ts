// /src/components/common/AuthStyles.ts
import styled from 'styled-components';
import { lighten, darken } from 'polished';

// Container for the entire authentication page
export const AuthPageContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.background};
  transition: background-color 0.3s ease;
  padding: 20px;
  box-sizing: border-box;
`;

// Card containing the auth forms
export const AuthCard = styled.div`
  background-color: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 40px;
  width: 100%;
  max-width: 420px;
  text-align: center;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
`;

// Header actions (for theme toggle button)
export const HeaderActions = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
`;

// Theme toggle button
export const ThemeToggleButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.text};
  cursor: pointer;
  font-size: 1.2rem;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease, color 0.3s ease;

  &:hover {
    background-color: ${({ theme }) => theme.name === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)'};
  }
`;

// App logo
export const AppLogo = styled.img`
  width: 150px;
  height: auto;
  margin-bottom: 25px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
`;

// Main page title
export const Title = styled.h1`
  color: ${({ theme }) => theme.text};
  margin-bottom: 10px;
  font-size: 2.5em;
  font-weight: bold;
  letter-spacing: -0.8px;
`;

// Subtitle
export const Subtitle = styled.p`
  color: ${({ theme }) => theme.text};
  margin-bottom: 30px;
  font-size: 1.2em;
  opacity: 0.8;
`;

// Generic form
export const Form = styled.form`
  width: 100%;
`;

// Input field
export const Input = styled.input`
  width: calc(100% - 24px);
  padding: 12px;
  margin-bottom: 20px;
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 8px;
  font-size: 1em;
  color: ${({ theme }) => theme.text};
  background-color: ${({ theme }) => theme.inputBackground || theme.surface};
  transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;

  &::placeholder {
    color: ${({ theme }) => theme.textSecondary};
    opacity: 0.7;
  }

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.primary}40;
  }

  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
  }
`;

// Main button
export const Button = styled.button`
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
  margin-top: 10px;

  &:hover {
    background-color: ${({ theme }) => theme.primaryHover || darken(0.1, theme.primary)};
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
    transform: none;
  }
`;

// "OR" separator
export const Separator = styled.div`
  display: flex;
  align-items: center;
  text-align: center;
  margin: 30px 0;
  color: ${({ theme }) => theme.textMuted || theme.textSecondary};
  opacity: 0.6;
  font-size: 0.9em;

  &::before,
  &::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  }

  &:not(:empty)::before {
    margin-right: .75em;
  }

  &:not(:empty)::after {
    margin-left: .75em;
  }
`;

// Google button
export const GoogleButton = styled.button`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.googleButtonBorder || theme.border};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.googleButtonBackground || theme.surface};
  color: ${({ theme }) => theme.googleButtonText || theme.text};
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;

  &:hover {
    background-color: ${({ theme }) => theme.googleButtonHoverBackground || darken(0.05, theme.googleButtonBackground || theme.surface)};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
  }
`;

// Status messages (error, success, loading)
export const Message = styled.p<{ type: 'error' | 'success' | 'loading' }>`
  margin-top: 15px;
  font-size: 0.95em;
  border-radius: 8px;
  padding: 10px;
  border: 1px solid;

  ${({ type, theme }) => {
    switch (type) {
      case 'error': return `color: ${theme.errorColor}; background-color: ${lighten(0.4, theme.errorColor || '#d9534f')}; border-color: ${theme.errorColor};`;
      case 'success': return `color: ${theme.successColor}; background-color: ${lighten(0.4, theme.successColor || '#28a745')}; border-color: ${theme.successColor};`;
      case 'loading': return `color: ${theme.primary}; font-weight: bold; border: none; background: none;`;
      default: return '';
    }
  }}
`;

// Links to toggle between login/signup
export const ToggleLink = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.primary};
  font-size: 1em;
  margin-top: 20px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;

  &:hover {
    color: ${({ theme }) => theme.primaryHover || darken(0.1, theme.primary)};
  }
`;

// Styles for Language Selector
export const LangSelectorContainer = styled.div`
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid ${({ theme }) => theme.border};
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
`;

export const FlagContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 20px;
`;

// --- MODIFIED: Use a transient prop '$isActive' ---
// The dollar sign prefix tells styled-components to not pass this prop to the DOM element.
export const FlagButton = styled.button<{ $isActive: boolean }>`
  background: none;
  border: none;
  cursor: pointer;
  font-size: 2rem;
  padding: 0;
  opacity: ${({ $isActive }) => ($isActive ? 1 : 0.6)};
  transform: ${({ $isActive }) => ($isActive ? 'scale(1.15)' : 'scale(1)')};
  transition: opacity 0.2s ease, transform 0.2s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.15);
  }
`;

export const WebsiteLink = styled.a`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.9em;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    text-decoration: underline;
    color: ${({ theme }) => theme.primary};
  }
`;