import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';

const Card = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  margin: 1rem 0;
  padding: 1.2rem;
`;

const Button = styled.button`
  background: ${({ theme }) => theme.primary};
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  margin-top: 1rem;
  cursor: pointer;
`;

const getQuestions = (t: any) => [
  {
    id: 'lang',
    label: t('preferences.language.question'),
    options: [t('languages.french'), t('languages.english'), t('languages.spanish')],
  },
  {
    id: 'notif',
    label: t('preferences.notifications.question'),
    options: [t('preferences.yes'), t('preferences.no')],
  },
  {
    id: 'premium',
    label: t('preferences.premium.question'),
    options: [t('preferences.yes'), t('preferences.no')],
  },
];

const Preferences: React.FC<{ onSubmit: (answers: Record<string, string>) => void }> = ({ onSubmit }) => {
  const { t } = useTranslation();
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [step, setStep] = useState(0);
  const questions = getQuestions(t);

  const handleSelect = (value: string) => {
    setAnswers((a) => ({ ...a, [questions[step].id]: value }));
    setStep((s) => s + 1);
  };

  if (step >= questions.length) {
    return (
      <Card>
        <div>{t('preferences.thanks')}</div>
        <Button onClick={() => onSubmit(answers)}>{t('preferences.validate')}</Button>
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>{questions[step].label}</div>
      {questions[step].options.map((opt) => (
        <Button key={opt} onClick={() => handleSelect(opt)} style={{ marginRight: 8, marginBottom: 8 }}>{opt}</Button>
      ))}
    </Card>
  );
};

export default Preferences;
