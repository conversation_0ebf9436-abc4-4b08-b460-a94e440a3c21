// src/i18n.ts

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// --- Statically import a minimal set of translations for the login page ---
import enAuth from './locales/en/auth.json';
import frAuth from './locales/fr/auth.json';
import esAuth from './locales/es/auth.json';

// --- START: Language Detection Logic ---
// This logic now runs immediately to determine the correct language *before* i18next initializes.

const SUPPORTED_LANGUAGES = ['en', 'fr', 'es'];
const DEFAULT_LANG = 'en';
const LANG_STORAGE_KEY = 'app_lang';

const getInitialLanguage = (): string => {
  try {
    // 1. Check localStorage for a saved language preference
    const savedLang = localStorage.getItem(LANG_STORAGE_KEY);
    if (savedLang && SUPPORTED_LANGUAGES.includes(savedLang)) {
      return savedLang;
    }

    // 2. Fallback to browser's language
    const browserLang = navigator.language.split('-')[0];
    if (SUPPORTED_LANGUAGES.includes(browserLang)) {
      return browserLang;
    }
  } catch (e) {
    // localStorage might be unavailable in some environments (e.g., private browsing)
    console.error("Could not access localStorage, falling back to browser language.", e);
    const browserLang = navigator.language.split('-')[0];
    if (SUPPORTED_LANGUAGES.includes(browserLang)) {
      return browserLang;
    }
  }
  
  // 3. Default to English
  return DEFAULT_LANG;
};

const initialLang = getInitialLanguage();
console.log(`[i18n] Initializing with language: ${initialLang}`);

// --- END: Language Detection Logic ---

i18n
  .use(initReactI18next)
  .init({
    // Pre-load minimal resources for instant rendering of AuthPage.
    resources: {
      en: { translation: enAuth },
      fr: { translation: frAuth },
      es: { translation: esAuth },
    },
    
    // Set the correctly detected language from the start.
    lng: initialLang,
    fallbackLng: 'en',

    // --- The rest of the config remains the same ---
    ns: ['translation'],
    defaultNS: 'translation',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;