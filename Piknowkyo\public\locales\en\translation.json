{"navigation": {"home": "Home", "sessions": "Sessions", "games": "Games", "journal": "Journal", "stats": "Stats", "blog": "Blog", "profile": "Profile", "monetization": "Premium", "settings": "Settings", "about": "About", "audio-assets": "My Audios", "recommendation": "Recommendation", "favorites": "My Favorites", "lexicon": "Lexicon"}, "lexicon": {"relatedConcepts": "Related Concepts", "title": "Lexicon", "subtitle": "Explore the concepts, emotions, and techniques of our approach.", "searchPlaceholder": "Search for a term...", "allCategories": "All", "noResults": "No definitions found. Try adjusting your search or filter.", "categories": {"basic_emotions": "Basic Emotions", "sentiments": "Sentiments", "cognitive_patterns": "Cognitive Patterns", "somatic_sensations": "Somatic Sensations", "desired_outcomes": "Desired Outcomes", "sensory_channels": "Sensory Channels", "modalities": "Modalities", "durations": "Durations", "intensities": "Intensities", "techniques": "Techniques", "energetic_systems": "Energetic Systems", "spiritual_concepts": "Spiritual Concepts"}}, "trial": {"banner": {"message": "Your trial ends on {{date}} ({{days}} days remaining)"}}, "common": {"welcome": "Welcome to PiKnowKyo", "ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "restart": "<PERSON><PERSON>", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success", "days": "days", "unknown": "an unknown date"}, "home": {"title": "Welcome to PiKnowKyo", "subtitle": "Your space to cultivate inner peace and growth.", "exploreButton": "Explore Sessions", "welcomeText": "Start your journey to well-being. Choose a practice or explore your personalized tools.", "quickAccess": "Quick Access", "learnMore": "Learn more about PiKnowKyo", "welcomeUser": "Welcome back, {{name}}! Ready for your next step?", "customizeQuickAccess": "Customize Quick Access", "modal": {"title": "Customize Quick Access", "toolsSection": "Tools & Actions", "pagesSection": "Main Pages", "categoriesSection": "Session Categories", "gamesSection": "Games", "favoritesSection": "Your Favorite Sessions", "searchPlaceholder": "Search sessions...", "searchGamesPlaceholder": "Search games..."}}, "sessions": {"title": "Explore Sessions", "meditation": "Meditation", "hypnosis": "Hypnosis", "affirmations": "Affirmations", "custom": "Your Sessions", "description": "Discover our collection of guided sessions for your personal growth.", "searchPlaceholder": "Search for a session...", "allTypes": "All types", "allDurations": "All durations", "durationLabel": "Duration", "type": "Type", "category": "Category", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noSessionsFound": "No sessions found matching your criteria.", "noCategoriesAvailable": "No session categories are available at the moment.", "noResultsInGroup": "No sessions in this category match your criteria.", "clearFilters": "Clear filters", "filterBy": "Filter by", "sessionType": "Session Type", "gridView": "Grid View", "listView": "List View", "noResultsMatchCriteria": "No sessions match your criteria.", "noSessionsAvailable": "No sessions available at the moment.", "story": "Story", "favoritesFilter": "Favorites only", "noResults": "No sessions found for this category or filter.", "noResults.filtered": "No sessions match your current filters.", "recommendation.button": "Get a Recommendation", "filter": {"searchPlaceholder": "Search all sessions...", "showAll": "All", "showFavorites": "Favorites", "filteringByTag": "Filtering by tag:", "duration": {"all": "Any duration"}, "allCategories": "All Categories", "toggleView": "Toggle view", "clear": "Clear filters"}, "filters": {"title": "<PERSON><PERSON>", "showFavorites": "Show favorites only", "reset": "Reset Filters", "apply": "Apply"}, "card": {"new": "New", "toggleFavorite": "Add/Remove from favorites"}, "viewModes": {"grid": "Grid", "list": "List"}, "duration": {"label": "Duration", "under15": "Under 15 min", "15to30": "15 - 30 min", "over30": "Over 30 min"}, "mood": {"label": "<PERSON><PERSON>", "all": "All moods", "calm": "Calm", "energized": "Energized", "focused": "Focused", "stressed": "Stressed"}, "toggleView": "Toggle view"}, "games": {"title": "Personal Development Minigames", "intro": "Test and improve your skills with our fun and challenging minigames.", "zenTetris": {"title": "Zen Tetris", "description": "A relaxing version of the famous block game. Improve your focus and stress management.", "rules": "Place the falling pieces to complete horizontal lines. The more lines you clear at once, the more points you get. The game speeds up gradually.", "controls": "Touch Controls", "tips": "Stay calm, plan your moves, and try to create combos to maximize your score.", "touchTap": "Quick Tap: <PERSON><PERSON><PERSON>", "touchLeft": "Swipe Left: Move Left", "touchRight": "Swipe Right: Move Right", "touchDown": "Swipe Down: Soft Drop", "touchButtons": "Control buttons at the bottom"}, "cardiacCoherence": {"title": "Cardiac Coherence", "description": "A guided breathing exercise to synchronize your heart and mind. Improve your focus and reduce stress.", "setup": {"title": "Configure Session", "mode": "Mode", "adult": "Adult", "child": "Child", "duration": "Duration", "useTTS": "Vocal Guide (TTS)", "useSoundEffects": "Sound Effects"}, "getReady": "Get ready...", "inhale": "Inhale...", "exhale": "Exhale...", "hold": "Hold...", "finished": "Session Complete", "finishedTitle": "Session Complete", "finishedMessage": "Great job! You have completed your breathing session."}, "estimatedDuration": "Estimated duration", "personalBest": "Personal best", "savedGameProgress": "Saved game progress", "maxLevels": "This game has {{maxLevels}} difficulty levels.", "yourBestScore": "Your best score on this game is {{score}} points.", "moveLeft": "Move Left", "moveRight": "Move Right", "softDrop": "Soft Drop", "rotate": "Rotate", "level": "Level", "lines": "Lines", "nextPiece": "Next Piece", "info": "Info", "keywords": "Keywords", "continueGame": "Continue Game", "newGame": "New Game", "gameInfo": "Game Information", "gameRules": "Game Rules", "gameOver": "Game Over", "finalScore": "Final Score", "newRecord": "New Record!", "playAgain": "Play Again", "backToGames": "Back to Games", "pause": "Pause", "resume": "Resume", "quit": "Quit", "gameOverSummary": "Congratulations! Your final score is {{score}} points and you reached level {{level}} in {{time}} seconds."}, "game": {"start": "Start", "resume": "Resume", "pauseButton": "Pause", "orientationHint": "Rotate for a better experience", "modal": {"rulesTitle": "Game Rules", "pausedTitle": "Game Paused", "gameOverTitle": "Game Over!", "return": "Return", "restart": "<PERSON><PERSON>", "resume": "Resume", "start": "Start", "pausedMessage": "Your game is paused. Resume when you're ready.", "gameOverMessage": "Well done! Your final score is {{score}} and you reached level {{level}}.", "gameOverSummary": "Congratulations! Your final score is {{score}} points and you reached level {{level}} in {{time}} seconds."}, "zenTetris": {"ruleMoveLeft": "Move left", "ruleMoveRight": "Move right", "ruleSoftDrop": "Soft drop", "ruleRotate": "Rotate", "rulePause": "Pause", "rules1": "Stack blocks to form complete lines and score points. The speed increases with levels!", "rules2": "Controls:"}, "controls": {"keyboard": "Keyboard", "touch": "Touch"}, "info": "Info", "level": "Level", "lines": "Lines", "score": "Score", "time": "Time", "nextPiece": "Next Piece", "moveLeft": "Move Left", "moveRight": "Move Right", "rotate": "Rotate", "softDrop": "Soft Drop"}, "journal": {"title": "Tracking Journal", "trackingJournal": "Tracking Journal", "yourNotes": "Your Journal for this Session", "noEntries": "No journal entries yet.", "addEntry": "Add your reflection on this session...", "description": "Find all your personal notes here, organized by session. Reflect on your experiences and track your progress.", "noNotesYet": "Your journal is still empty.", "startSessionPrompt": "Start a session and take notes to see your reflections here.", "unknownSession": "Session (ID: {{id}})", "seeAllNotes": "See all {{count}} notes...", "loginToUse": "Log in to use the journal feature.", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "deleteConfirmText": "Are you sure you want to permanently delete this journal entry?", "previousEntriesTitle": "Previous Entries", "noteSingular": "{{count}} note", "notesPlural": "{{count}} notes"}, "rating": {"loginToRate": "Log in to rate this session."}, "and": " and ", "stats": {"title": "Your Well-being Statistics", "sessionsFollowed": "Sessions Practiced", "daysDaysStreakDesc": "Days you logged in and completed an activity.", "duration": {"hours": "h", "min": "min", "h": "h"}, "timesExploredSingular": "time explored", "timesExploredPlural": "times explored", "and": " and ", "affinityAnalysis.noConcepts": "nothing specific", "affinityAnalysis.intro": "Based on your completed sessions, here's an overview of your journey and affinities:", "affinityAnalysis.summary": "You've most often explored themes related to: ", "affinityAnalysis.emotions": "Emotions and Sentiments like {{list}}.", "affinityAnalysis.outcomes": "Desired Outcomes such as {{list}}.", "affinityAnalysis.techniques": "Techniques used including {{list}}.", "affinityAnalysis.somatic": "Somatic Sensations explored including {{list}}.", "affinityAnalysis.cognitive": "Cognitive Patterns addressed such as {{list}}.", "affinityAnalysis.modalities": "Well-being Modalities like {{list}}.", "affinityAnalysis.energetic": "Energetic Concepts including {{list}}.", "affinityAnalysis.duration": "Typical Session Durations like {{list}}.", "affinityAnalysis.intensity": "Session Intensities such as {{list}}.", "affinityAnalysis.noDetails": "No specific details for this category yet. Keep exploring!", "affinityAnalysis.overallTopConcepts": "Your most explored concepts (all categories) are: {{list}}.", "affinityAnalysis.moreDetailsLink": "View all explored concepts for more details.", "journalEntries": "Your Journal Entries", "showAllJournalEntries": "View all {{count}} entries", "showAllActivityHistory": "View all activity history", "noJournalEntries": "You don't have any journal entries yet. Complete sessions and take notes to see your reflections here!", "viewEntry": "View entry", "forSession": "for session", "sessionsCompleted": "Sessions completed", "daysStreak": "Consecutive days", "totalMinutes": "Total minutes", "noData": "You haven't completed any sessions yet. Start a session to see your stats here!", "sessionsCompletedDesc": "Total number of sessions you have completed.", "completionsPlural": "completed", "completionsSingular": "completed", "activityHistory": "Activity History", "noActivityHistory": "No activity history available. Start a session or game to see it here!", "completedOn": "Completed on", "sleepPatterns": "Sleep Patterns", "heartRateVariability": "Heart Rate Variability", "exerciseTracking": "Exercise Tracking", "nutritionHydration": "Nutrition & Hydration", "futureFeature": "Future Feature", "comingSoon": "Coming Soon", "description": "Track your journey, celebrate your progress, and discover your trends.", "sessionsFollowedDesc": "Number of unique sessions with notes.", "totalTime": "Total Time in Session", "totalTimeDesc": "Estimated cumulative time.", "favoriteSession": "Favorite Session", "favoriteSessionDesc": "The most noted.", "notesWritten": "Total Notes Written", "notesWrittenDesc": "Number of recorded reflections.", "typesFollowed": "Breakdown by Session Type", "timePerSession": "Detail per Session (Estimated)", "noTypesYet": "No specific session types tracked yet.", "noTimePerSession": "No time per session data available.", "timesPlural": "times", "timesSingular": "time", "notesPlural": "notes", "noteSingular": "note", "gameHighScores": "Game High Scores", "noGameData": "You have not set any high scores in the games yet. Go play to see your records here!", "levelLabel": "Level", "yourAffinityProfile": "Your Affinity Profile", "affinityDescription": "The concepts and emotions you've most explored through your sessions.", "noAffinityData": "No affinity data available. Complete sessions to see your profile here!", "timesExplored": "times explored", "healthMetrics": "Health & Well-being Metrics", "futureDevMessage": "This section will soon integrate data from connected devices to provide insights into your sleep patterns, heart rate variability, exercise, nutrition, hydration, and more. Stay tuned for a complete holistic view of your well-being!"}, "blog": {"title": "Community Journal", "description": "Share your experiences, discoveries, and inspirations with the PiKnowKyo community. All posts are anonymous.", "searchPlaceholder": "Search posts...", "allCategories": "All categories", "writeNewPost": "Write a new post", "postPlaceholder": "Your post (will be published anonymously)...", "category": "Category", "publishing": "Publishing...", "publish": "Publish", "loginToPost": "You must be logged in to post.", "noPostsYet": "No posts yet in this category or matching your search.", "noPostsFound": "No posts found for this category or search term.", "unsyncedPostTooltip": "This post is saved locally and will be synced when you are online.", "unsyncedCommentTooltip": "This comment is saved locally and will be synced when you are online.", "like": "Like", "comments": "Comments", "addComment": "Add a comment", "commentPlaceholder": "Your comment (anonymous)...", "postComment": "Post Comment", "noCommentsYet": "No comments yet. Be the first to comment!", "backToBlog": "Back to blog", "postNotFound": "Post not found", "commentsSectionTitle": "Comments", "yourCommentPlaceholder": "Your comment...", "sending": "Sending...", "sendComment": "Send", "loginToComment": "Log in to add a comment.", "sampleAuthor": "Anonymous Author", "samplePostContent": "Detailed content of the post. This post talks about the importance of mindfulness in our stressful daily lives and how simple exercises can bring great inner peace.", "sampleCommenter1": "Commenter1", "sampleCommenter2": "<PERSON><PERSON><PERSON>", "sampleComment1": "Great post!", "sampleComment2": "Very interesting, thanks for sharing.", "anonymousUser": "Anonymous User", "unknownDate": "Unknown date", "categories": {"général": "General", "gratitude": "Gratitude", "défis": "Challenges", "inspirations": "Inspirations", "questions": "Questions"}}, "about": {"title": "About", "description": "Discover <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, your companion for personal growth and well-being.", "features": {"customizable": "Fully customizable sessions to meet your needs.", "journal": "Personal journal to track your progress and reflections.", "stats": "Detailed statistics to visualize your evolution.", "games": "Minigames to develop your cognitive skills."}, "philosophy": {"title": "Our Philosophy: The Journey from Pi to Kyo", "pi": {"title": "Pi (π)", "description": "The infinite, the sacred mystery of the universe, and the fundamental harmony that unites us all. It is the starting point, the openness to the unknown."}, "know": {"title": "Know", "description": "Exploration, structured learning, and mental clarity. It is the acquisition of tools and understandings to navigate the path."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "Teaching, embodied wisdom, enlightenment, and the altruistic sharing of the discovered light. It is the culmination and the radiance."}, "conclusion": "PiKnowKyo is more than an app; it's a compass for your inner growth, inspired by"}, "tools": {"title": "Our Tools for Your Growth", "description": "We offer a diverse range of sessions and tools designed to support you on your path of personal growth:", "hypnosis": "Evolutionary Hypnosis to explore your subconscious and initiate profound changes.", "meditation": "Guided Meditations to cultivate mindfulness, inner peace, and emotional resilience.", "affirmations": "Positive Affirmations to reprogram your thoughts and strengthen your self-confidence.", "nlp": "NLP Training (Neuro-Linguistic Programming) to improve your communication and achieve your goals.", "stories": "Metaphorical Stories to stimulate your imagination and facilitate the integration of new perspectives."}, "experience": {"title": "A Holistic Experience Designed for You", "audio": "100% configurable audio (music, ambiance, voice, binaural).", "guidedPaths": "Guided paths and custom session creation.", "blog": "Internal blog with articles, tips, and inspiring resources.", "multilang": "Multi-language support and customizable light/dark themes.", "notifications": "Gentle motivational notifications to accompany you."}, "monetization": {"title": "Ethical Monetization:", "description": "We offer a free trial, an optional subscription for full access, minimal and non-intrusive ads (bypassable with the subscription), and the possibility of donations to support our mission."}, "community": {"title": "Join Our Community", "description": "PiKnowKyo is designed for those who value self-knowledge, organizing their thoughts, and personal productivity with a focus on well-being. Whether you are a student, professional, researcher, or simply a curious mind in search of harmony, our app is your ally.", "contact": "For any questions, suggestions, or if you need assistance, do not hesitate to email us at:", "website": "You can also visit our website", "moreInfo": "for more information"}}, "monetization": {"title": "Upgrade Your Experience", "description": "Unlock all features to maximize your well-being journey, or continue with our generous free plan.", "plans": {"free": {"name": "Free Plan"}, "premium": {"name": "Piknowkyo Premium"}, "billing": {"month": "month"}}, "features": {"free": {"meditations": "Unlimited access to meditations and stories sessions", "musicAndTTS": "Background music and basic TTS voices", "stats": "Progress statistics", "journal": "Tracking journal", "adUnlock": "Watch an ad to unlock a session for 1 hour", "support": "Support via social media"}, "premium": {"allSessions": "Unlimited access to ALL sessions (hypnosis, NLP, etc.)", "cloudTTS": "Cloud TTS voices", "ambientSounds": "Advanced ambient and binaural sounds", "stats": "Progress statistics", "games": "Access to mini-games", "journal": "Tracking journal", "noAds": "Ad-free experience"}}, "status": {"title": "Subscription Status", "trialActive": "You are currently on a Premium trial.", "renewsOn": "Your Premium plan renews on <strong>{{date}}</strong>.", "cancelsOn": "Your Premium plan will expire on <strong>{{date}}</strong>.", "freePlan": "You are currently on the Free plan.", "cancelsOnDateTime": "Your access will end on <strong>{{date}} at {{time}}</strong>.", "renewsOnDateTime": "Your subscription renews on <strong>{{date}} at {{time}}</strong>."}, "trial": {"endsIn_one": "Your trial ends in {{count}} day.", "endsIn_other": "Your trial ends in {{count}} days.", "ended": "Your trial has ended."}, "toast": {"checkoutSuccess": "Subscription successful! Your plan should update shortly.", "checkoutCanceled": "Subscription process canceled."}, "actions": {"manage": "Manage Subscription", "upgradeNow": "Upgrade Now", "currentPlan": "Current Plan", "subscribe": "Subscribe", "subscribeError": "Failed to create Stripe checkout URL. Please try again.", "manageError": "Failed to get Stripe customer portal URL. Please try again."}}, "sessionDetails": {"yourNotes": "Your Journal for this Session", "averageRating": "Average Rating", "noRatingsYet": "No ratings yet. Be the first to rate it!", "viewInLexicon": "View '{{term}}' in Lexicon", "description": "Description", "expectedBenefits": "Expected Benefits", "keywords": "Keywords", "startSession": "Start Session", "backToSessions": "Back to Sessions", "audioConfigGlobal": "Session Audio Configuration", "userReviews": "User Reviews", "previousNotes": "Previous Notes", "voiceConfigTitle": "Voice Configuration", "yourRating": "Your Rating", "tagsTitle": "Tags", "lastUpdated": "Last updated on {{date}}", "tagTooltip": "View definition in Lexicon", "benefitsTitle": "Benefits", "dnd": {"label": "Do Not Disturb (App)", "permissionNeededInfo": "<PERSON><PERSON><PERSON> will request notification permission to optimize this mode.", "permissionDeniedWarning": "Notification permission denied. The app's DND mode is active, but system notifications are not affected."}}, "units": {"minutes": "min", "points": "pts", "notAvailable": "N/A", "seconds": "sec", "min": "min", "ratings_one": "rating", "ratings_other": "ratings"}, "menu": {"navigation": "Navigation", "account": "Account"}, "notFound": {"message": "Sorry, the page you are looking for does not exist.", "backHome": "Back to Home"}, "quiz": {"title": "Quiz", "description": "Select a quiz to start testing your knowledge!", "comingSoon": "Quizzes are coming soon! Stay tuned."}, "history": {"title": "History", "description": "Check your results and progress.", "comingSoon": "History will be available soon."}, "categories": {"title": "Categories", "description": "Choose a category to explore related quizzes.", "comingSoon": "Categories are coming soon!"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "This browser does not support notifications."}, "pseudoGenerator": {"adjectives": {"light": "Light", "wind": "Wind", "ocean": "Ocean", "mountain": "Mountain", "star": "Star", "forest": "Forest", "river": "River", "sun": "Sun", "moon": "Moon", "aurora": "Aurora"}, "nouns": {"serene": "<PERSON><PERSON>", "calm": "Calm", "wise": "<PERSON>", "peaceful": "Peaceful", "clairvoyant": "Clairvoyant", "harmonious": "Harmonious", "awakened": "Awakened", "free": "Free", "creative": "Creative", "intuitive": "Intuitive"}}, "app": {"name": "Piknowkyo", "theme_light": "Switch to light theme", "theme_dark": "Switch to dark theme", "logo_alt": "Piknowkyo Logo"}, "auth": {"common": {"email_placeholder": "Email Address", "password_placeholder": "Password", "or_separator": "OR", "please_wait_loading": "Please wait...", "success_redirect": "Login or signup successful! Redirecting..."}, "login": {"subtitle": "Welcome back!", "button": "Log In", "button_loading": "Logging in...", "google_button": "Log in with Google", "google_button_loading": "Logging in with Google...", "error_invalid_credentials": "Incorrect email or password.", "error_google_popup_closed": "The Google sign-in window was closed. Please try again.", "error_google_popup_cancelled": "A Google popup request is already pending or was cancelled. Please try again.", "error_general": "<PERSON><PERSON> failed. Please try again.", "toggle_signup": "Don't have an account yet? Sign up"}, "signup": {"subtitle": "Create your account!", "confirm_password_placeholder": "Confirm Password", "button": "Sign Up", "button_loading": "Signing up...", "google_button": "Sign up with Google", "google_button_loading": "Signing up with Google...", "error_password_mismatch": "Passwords do not match.", "error_email_in_use": "This email is already in use. Please log in.", "error_weak_password": "Password is too weak (minimum 6 characters).", "error_general": "Signup failed. Please try again.", "toggle_login": "Already have an account? Log in"}, "logout": {"button": "Log out", "button_aria_label": "Log out of your account"}}, "preferences": {"language": {"question": "Choose your preferred language"}, "notifications": {"question": "Would you like to receive motivational notifications?"}, "premium": {"question": "Do you want to try premium features for free (with non-intrusive ads)?"}, "yes": "Yes", "no": "No", "thanks": "Thank you!", "validate": "Validate my preferences"}, "questionnaire": {"goal": {"question": "What is your main goal?", "relaxation": "Relaxation", "confidence": "Self-confidence", "stress": "Stress management", "spirituality": "Spirituality", "other": "Other"}, "experience": {"question": "Have you ever practiced hypnosis or meditation?", "never": "Never", "sometimes": "Sometimes", "regularly": "Regularly"}, "audio": {"question": "Do you prefer a session with music, natural sounds, or silence?", "music": "Music", "nature": "Natural sounds", "silence": "Silence"}, "thanks": "Thank you!", "viewSuggestions": "View my suggestions"}, "notificationTest": {"heading": "Notification Test", "platform": "Current platform:", "status": "Web notification status:", "title": "Notification Test", "body": "This is a test notification from PiKnowKyo", "sendButton": "Send a test notification"}, "actions": {"back": "Back", "backToBlog": "Back to Blog", "backToHome": "Back to Home", "backToSessionDetails": "Back to Details", "backToSessions": "Back to Sessions", "backToSettings": "Back to Settings", "cancel": "Cancel", "delete": "Delete", "deleteConfirm": "Confirm Deletion", "deleting": "Deleting...", "enterFullscreen": "Fullscreen Mode", "exitFullscreen": "Exit Fullscreen", "ok": "OK", "pause": "Pause", "play": "Play", "preview": "Preview", "restart": "<PERSON><PERSON>", "startSession": "Start Session", "stopPreview": "Stop", "stopTest": "Stop Test", "testSound": "Test Sound", "testVoice": "Test Voice", "upload": "Upload", "saving": "Saving...", "previewSound": "Preview Sound"}, "audioAssets": {"title": "Manage Audio Files", "musicTitle": "Music", "ambientTitle": "Ambient Sounds", "noMusics": "No music available", "noAmbiants": "No ambient sounds available", "selectFile": "Select a file", "changeFile": "Change file", "uploadMusicPrompt": "Upload new music", "uploadAmbientPrompt": "Upload new ambient sound", "uploading": "Uploading...", "uploadSuccess": "File {{fileName}} uploaded successfully!", "uploadError": "Error during upload", "previewError": "Could not play audio preview", "cannotDeleteDefault": "Default files cannot be deleted", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete this file?", "deleteSuccess": "File deleted successfully", "deleteError": "Error during deletion"}, "audioConfig": {"musicTrack": "Music track", "musicTitle": "Background Music", "musicTooltip": "Choose background music to accompany your session. You can adjust the volume.", "ambientSound": "Ambient sound:", "ambientTitle": "Ambient Sound", "ambientTooltip": "Add natural or ambient sounds to create the perfect atmosphere.", "beatFrequency": "Beat (Hz)", "brainwavePresets": "Brainwaves (Beat):", "testVoiceText": "This is a test of the selected voice.", "testVoiceError": "The test could not be completed.", "binauralBeats": "Binaural/Isochronic Beats", "binauralTooltip": "Generate sounds to influence brainwaves. Headphones are required for an optimal binaural effect.", "binauralBeatsSetup": {"volume": "Binaural Volume:"}, "baseFrequency": "Base Frequency (Hz)", "baseFrequencyPreset": "Base Frequency Preset:", "beatFrequencyPreset": "Beat Frequency Preset (Brainwave):", "customOption": "Custom", "customFrequencyDescPlaceholder": "Custom frequency selected or no preset active.", "selectPresetToSeeDescription": "Select a preset to see its description.", "headphonesRequired": "Headphones required for binaural effect", "baseFreqPresets": {"solfeggio": {"category": "Solfeggio Frequencies", "174": {"label": "174 Hz - Foundation", "desc": "Associated with grounding, security, and pain relief. Helps create a stable foundation."}, "285": {"label": "285 Hz - Tissue Regeneration", "desc": "Linked to tissue healing and regeneration at a cellular level. Promotes rejuvenation."}, "396": {"label": "396 Hz (Ut) - Liberating Guilt & Fear", "desc": "Helps release guilt, fear, and subconscious blockages. Supports grounding and empowerment."}, "417": {"label": "417 Hz (Re) - Facilitating Change", "desc": "Aids in clearing traumatic experiences and facilitating positive change. Cleanses negative energy."}, "528": {"label": "528 Hz (Mi) - Transformation & Miracles (DNA Repair)", "desc": "Known as the 'love frequency'. Associated with DNA repair, increased energy, and clarity."}, "639": {"label": "639 Hz (Fa) - Connecting & Relationships", "desc": "Promotes harmony in relationships, understanding, tolerance, and love. Enhances communication."}, "741": {"label": "741 Hz (Sol) - Awakening Intuition & Expression", "desc": "Linked to cleansing toxins, awakening intuition, and promoting self-expression. Solves problems."}, "852": {"label": "852 Hz (La) - Returning to Spiritual Order", "desc": "Helps awaken inner strength and self-realization. Connects to a higher spiritual order."}, "963": {"label": "963 Hz (Si) - Divine Consciousness & Oneness", "desc": "Associated with awakening to perfect state, oneness, and connection with divine consciousness."}}, "chakras": {"category": "Chakra Frequencies", "root": {"label": "Root Chakra (~256 Hz)", "desc": "Relates to grounding, security, survival instincts, and physical vitality."}, "sacral": {"label": "Sacral Chakra (~288 Hz)", "desc": "Governs creativity, emotions, sexuality, and pleasure."}, "solarPlexus": {"label": "Solar Plexus Chakra (~320 Hz)", "desc": "Center of personal power, self-esteem, and willpower."}, "heart": {"label": "Heart Chakra (~341.3 Hz)", "desc": "Relates to love, compassion, forgiveness, and emotional balance."}, "throat": {"label": "Throat Chakra (~384 Hz)", "desc": "Center for communication, self-expression, and truth."}, "thirdEye": {"label": "Third Eye Chakra (~426.7 Hz)", "desc": "Governs intuition, insight, wisdom, and psychic abilities."}, "crown": {"label": "Crown Chakra (~480 Hz)", "desc": "Connects to spirituality, divine consciousness, and enlightenment."}}, "planetary": {"category": "Planetary Frequencies", "om": {"label": "OM / Earth Year (136.10 Hz) - Alignment", "desc": "The OM frequency, corresponding to the Earth's orbital period. Used for deep grounding, centering, and spiritual alignment."}, "sun": {"label": "Sun (126.22 Hz) - Vitality", "desc": "Promotes vitality, joy, and a sense of self. Associated with intuition and life force."}, "earth": {"label": "Earth Day (194.18 Hz) - Grounding", "desc": "For grounding, stability, and connection with the Earth's energy. Promotes physical balance."}, "moon": {"label": "Moon (210.42 Hz) - Emotions", "desc": "Relates to emotions, sensitivity, and the feminine cycle. Supports emotional flow."}}, "organsDetailed": {"category": "Organ & Gland Frequencies (Detailed)", "pineal": {"label": "Pineal Gland (662 Hz)", "desc": "A frequency specifically cited for pineal gland resonance, distinct from broader spiritual frequencies."}, "pituitary": {"label": "Pituitary Gland (636 Hz)", "desc": "Supports pituitary function, hormonal balance, and higher intuition."}, "brain_general": {"label": "Brain (General Resonance - 330 Hz)", "desc": "A general resonance frequency sometimes associated with overall brain health and activity."}}, "otherNotable": {"category": "Other Notable Frequencies", "432hz": {"label": "432 Hz - Natural Tuning", "desc": "Considered by some to be a more natural, harmonious tuning frequency aligned with the universe. Promotes calm."}}}, "beatPresets": {"delta": {"category": "Delta Waves (0.5-4 Hz) - Deep Sleep", "2_5hz": {"label": "2.5 Hz - Pain Relief & Relaxation", "desc": "Associated with endorphin release, may aid in pain relief and induce profound relaxation."}}, "theta": {"category": "Theta Waves (4-8 Hz) - Deep Meditation", "5hz": {"label": "5 Hz - Intuition & Dream Recall", "desc": "Stimulates intuition, creativity; ideal for deep meditation and accessing dream memories."}, "7_83hz": {"label": "7.83 Hz - <PERSON><PERSON><PERSON> (Earth)", "desc": "Main Schumann Resonance. Promotes grounding, stress reduction, and a sense of connection."}}, "alpha": {"category": "Alpha Waves (8-12 Hz) - Relaxed Focus", "10hz": {"label": "10 Hz - Peak Alpha (Learning & Serenity)", "desc": "Peak Alpha. Enhances learning, memory, serenity, and reduces anxiety. Mood elevator."}}, "beta": {"category": "Beta Waves (12-38 Hz) - Active Thinking", "14hz": {"label": "14 Hz - Active Focus (SMR)", "desc": "Increases concentration, alertness; ideal for problem-solving and analytical thought."}}, "gamma": {"category": "Gamma Waves (38Hz+) - Peak Performance", "40hz": {"label": "40 Hz - Optimal Performance & Insight", "desc": "High-level information processing, heightened perception, complex problem solving, 'aha!' moments."}}, "spiritualStates": {"category": "Spiritual States & Consciousness", "pinealActivationTheta": {"label": "Pineal Attunement (Theta - 7.5Hz)", "desc": "Theta waves for deep meditation aimed at harmonizing and gently stimulating the pineal gland."}, "chakraCleansing": {"label": "Chakra Cleansing (Theta - 6Hz)", "desc": "Theta waves to support deep meditation for energetic cleansing and alignment of the chakra system."}, "astralProjection": {"label": "Astral Projection Aid (Theta - 6.5Hz)", "desc": "Theta waves often associated with inducing states conducive to out-of-body experiences."}, "kundaliniSupport": {"label": "Kundalini Support (Alpha/Theta - 8Hz)", "desc": "Alpha-Theta border frequency to support meditative practices aimed at safely awakening Kundalini energy."}, "merkabaMeditation": {"label": "Merkaba Meditation (Alpha - 10.5Hz)", "desc": "Alpha waves to aid in Merkaba (light body) activation meditations for spiritual awareness."}}, "cognitiveEnhancement": {"category": "Cognitive Enhancement & Well-being", "creativityBoostTheta": {"label": "Creativity Boost (Theta - 5.5Hz)", "desc": "Theta waves to enhance insight, inspiration, and creative problem-solving."}, "anxietyReductionAlpha": {"label": "Anxiety Reduction (Alpha - 10Hz)", "desc": "Peak Alpha waves for promoting calmness, reducing stress, and alleviating anxiety."}, "sleepImprovementDelta": {"label": "Sleep Improvement (Delta - 2Hz)", "desc": "Delta waves to entrain the brain towards deep, restorative sleep patterns."}}}, "music": {"none": "None"}, "ambient": {"none": "None"}, "noDescription": "No description available", "presets": {"chakras": "Chakras", "organs": "Organs", "otherNotable": "Other notable frequencies"}, "webAudioNotSupported": "Web Audio API not supported by your browser.", "ttsTitle": "Text-to-Speech", "ttsTooltip": "Adjust the guide's voice volume. The voice type and language are managed in the app's general settings.", "musicSound": "Music:", "volume": "Volume", "selectPreset": "-- Select a preset --", "selectState": "-- Select a state --", "targetFrequencyInfo": "Left Ear: {{leftEar}} Hz, Right Ear: {{rightEar}} Hz"}, "errors": {"missingPostId": "Missing post ID.", "paymentError": "An error occurred while processing your payment.", "manageSubscriptionError": "Could not access your subscription management.", "postNotFound": "Post not found.", "cantLoadPost": "Could not load post.", "cantLoadComments": "Could not load comments.", "cantAddComment": "Error adding comment.", "cantAddPost": "Error publishing post.", "cantLoadSessions": "Could not load session data.", "encryptionFailed": "Encryption failed", "cantLoadJournal": "Could not load journal entries.", "cantLoadUserData": "Could not load your personal data.", "blogLoadLocalFailed": "Failed to load blog data from local storage.", "blogSyncFailed": "Failed to sync with the server.", "postSaveFailed": "Your post could not be saved locally.", "commentSaveFailed": "Your comment could not be saved locally.", "commentLoadFailed": "Failed to load comments.", "userNotAuthenticated": "You must be logged in to upload files.", "cantLoadAssets": "Could not load audio assets.", "cloudVoicesPremium": "Cloud voices are a premium feature. Please select Browser provider or upgrade.", "cantLoadDictionary": "Could not load dictionary data."}, "warnings": {"decryptionFailed": "Could not decrypt data. It might be corrupted or from a previous session.", "missingEncryptionKey": "Security Alert: Encryption key is not set. Data will be stored in plaintext."}, "player": {"sessionEnded": "Session ended.", "readyToStart": "Ready to start...", "audioSettings": "Volumes", "volumeControls": "Volume Settings", "music": "Music", "ambient": "Ambiance", "voice": "Voice", "binaural": "Binaural Beats"}, "settings": {"title": "Settings", "appLanguage": "App Language", "appLanguageInfo": "This changes the language of the entire application.", "audio": "Audio", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "language": "Language", "voice": "Voice", "autoVoice": "Auto voice", "testVoice": "Test voice", "saveConfig": "Save Configuration", "ttsSectionTitle": "Text-to-Speech (TTS)", "ttsProvider": "TTS Provider", "ttsTestText": "This is a text-to-speech test.", "ttsTestError": "Error testing voice", "downloadingVoice": "Downloading voice...", "voiceDownloaded": "Voice downloaded", "noVoiceForSelection": "No voice available for this selection", "noSpecificVoiceForLang": "No specific voice for this language. Here are all available voices:", "explanationsTitle": "Explanations", "audioAssetsManagementTitle": "Audio Assets Management", "audioAssetsInfo": "Manage your custom music and ambient sounds", "goToAudioAssets": "Manage Audio Files", "ttsProviderBrowser": "Browser (Offline & Fast)", "ttsProviderCloud": "Google Cloud (High Quality)", "premiumOnly": "Premium Only", "providerLabels": {"browser": "Browser", "cloud": "<PERSON> (AI)"}, "ttsProviderInfo": {"browser": "Uses the browser's built-in voices", "cloud": "High-quality AI voices (Coming soon...)"}, "modal": {"saveSuccessTitle": "Configuration Saved", "saveSuccessMessage": "Your settings have been saved successfully", "testErrorTitle": "Test Error"}}, "test": {"newSongTitle": "New song title", "addNewSong": "Add new song"}, "sync": {"offline": "Offline", "syncing": "Syncing...", "error": "Sync error ({{count}})", "pending": "{{count}} pending", "synchronized": "Synchronized", "syncedMinutesAgo": "Synced {{minutes}}min ago", "syncedHoursAgo": "Synced {{hours}}h ago", "online": "Online", "clickToSync": "Click to sync"}, "loading": {"user": "Loading user information...", "profile": "Loading profile...", "blog": "Loading blog...", "comments": "Loading comments...", "post": "Loading post...", "content": "Loading content...", "stats": "Loading statistics...", "sessions": "Loading sessions...", "session": "Loading session...", "journal": "Loading your journal...", "default": "Loading...", "authenticating": "Authenticating...", "language": "Setting language...", "initializing": "Initializing data...", "category": "Loading...", "pseudo": "Generating...", "audioAssets": "Loading audio assets...", "categories": "Loading categories...", "voices": "Loading voices..."}, "plans": {"free": {"title": "Free Plan", "price": "$0", "currentPlan": "Your Current Plan", "switchToFree": "Switch to Free Plan"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Billed monthly, cancel anytime.", "manageSub": "Manage Subscription", "subscribe": "Upgrade to Premium"}, "billing": {"month": "month"}}, "features": {"free": {"baseMeditations": "Access to basic meditations and stories", "backgroundMusic": "Basic background music and TTS voices", "stats": "Progress statistics", "blog": "Access to community blog"}, "premium": {"allSessions": "Unlimited access to ALL sessions (hypnosis, NLP, etc.)", "ambientSounds": "Advanced ambient sounds and binaural beats", "customSessions": "Creation of custom sessions", "games": "Access to mindfulness mini-games", "journal": "Detailed tracking journal", "motivationNotifs": "Personalized motivational notifications", "calendar": "Calendar and custom programs (coming soon)", "customAudio": "Ability to use your own sounds and music", "noAds": "Ad-free experience", "prioritySupport": "Priority support"}}, "legal": {"privacy": "Privacy Policy", "terms": "Terms and Conditions"}, "profile": {"title": "My Profile", "notConnectedTitle": "User Profile", "pleaseLogin": "Please log in to access your profile.", "publicPseudo": "Public Pseudo", "regeneratePseudo": "Generate new pseudo", "preferencesTitle": "Preferences", "appLanguage": "App Language", "grammaticalGenderLabel": "How do you prefer to be addressed in the scripts?", "grammaticalGenderInfo": "This will help us tailor some texts for a more personalized experience.", "accountActionsTitle": "Account Management", "logout": "Log Out", "deleteAccount": "Delete my account", "deleteConfirmTitle": "Confirm Deletion", "deleteConfirmMessage": "Are you sure you want to delete your account? All your data, including your progress and journal entries, will be permanently erased. This action is irreversible.", "accountDeletedSuccess": "Your account and all your data have been deleted.", "memberSince": "Member since {{date}}", "statsTitle": "Your Activity", "stats": {"sessionsCompleted": "Sessions completed", "daysStreak": "Consecutive days", "totalMinutes": "Total minutes", "noData": "You haven't completed any sessions yet. Start a session to see your stats here!", "sessionsCompletedDesc": "Total number of sessions you have completed.", "completionsPlural": "completed", "completionsSingular": "completed"}}, "gender": {"masculine": "Ma<PERSON><PERSON><PERSON>", "feminine": "Feminine", "neutral": "Neutral"}, "sessionTypes": {"hypnosis": "Hypnosis", "meditation": "Meditation", "training": "Training", "story": "Story", "journaling": "Journaling", "visualization": "Visualization", "relaxation": "Relaxation", "coaching": "Coaching", "sleep induction": "Sleep Induction", "sleep-induction": "Sleep Induction", "roleplay": "Roleplay", "affirmation": "Affirmation", "gratitude practice": "Gratitude Practice", "gratitude-practice": "Gratitude Practice", "breathwork": "Breathwork", "motivational speech": "Motivational Speech", "motivational-speech": "Motivational Speech", "guided imagery": "Guided Imagery", "guided-imagery": "Guided Imagery", "problem solving": "Problem Solving", "problem-solving": "Problem Solving", "creative writing": "Creative Writing", "creative-writing": "Creative Writing", "mindful movement": "Mindful Movement", "mindful-movement": "Mindful Movement", "self-compassion": "Self-Compassion", "focus enhancement": "Focus Enhancement", "focus-enhancement": "Focus Enhancement", "silence": "Silence", "beginner": "<PERSON><PERSON><PERSON>", "work_break": "Work Break"}, "sessionTypesDescriptions": {"hypnosis": "A guided session to induce a deep state of relaxation and suggestibility, using a slow, repetitive tone to reduce stress or target subconscious goals like confidence or habit change.", "meditation": "A guided mindfulness or relaxation session focusing on breathing, body awareness, or mental calmness, with a soothing tone and frequent pauses to enhance presence.", "training": "An energetic session to motivate and guide a physical or mental workout, delivering clear instructions and encouragement for activities like workout routines or productivity tasks.", "story": "An immersive narrative session, such as a fantasy or adventure story, with vivid descriptions and a captivating tone to engage the listener's imagination.", "journaling": "An introspective session guiding the listener through reflective questions or prompts, with a calm, encouraging tone and pauses to allow for written responses.", "visualization": "A guided session to create vivid mental imagery, such as visualizing goals or calming scenes, using a descriptive and immersive tone to enhance focus.", "relaxation": "A session focused on physical and mental relaxation, using a gentle tone and slow pacing to release tension and promote deep rest.", "coaching": "A motivational session offering guidance and strategies for personal or professional growth, with an upbeat and empowering tone to inspire action.", "sleep induction": "A calming session designed to help the listener fall asleep, using a slow, soothing tone with gentle imagery and a progressive cadence to encourage rest.", "roleplay": "An interactive narrative session placing the listener in a role (e.g., an explorer, a detective), with a dynamic, engaging tone and pauses for imagined responses.", "affirmation": "A session delivering positive, empowering statements to build confidence or mindset, using a clear, uplifting tone with pauses to let affirmations sink in.", "gratitude practice": "A session guiding the listener to reflect on things they are thankful for, with a warm, thoughtful tone and pauses to encourage deep emotional connection.", "breathwork": "A session guiding specific breathing techniques to reduce stress or increase energy, with a steady, rhythmic tone and clear instructions for breathing pace.", "motivational speech": "An inspiring session delivering a powerful speech to boost determination and focus, using a passionate, uplifting tone to energize the listener.", "guided imagery": "A session leading the listener through detailed mental scenes (e.g., a peaceful beach), with a descriptive, soothing tone to enhance relaxation or creativity.", "problem solving": "A session guiding the listener through structured steps to address a personal or professional challenge, with a clear, supportive tone and pauses for reflection.", "creative writing": "A session providing prompts or scenarios to inspire creative writing, with an imaginative, encouraging tone and pauses for the listener to write.", "mindful movement": "A session guiding gentle physical movements (e.g., yoga or stretching) with a calm, instructional tone, synchronized with breathing cues.", "self-compassion": "A session fostering self-kindness through guided reflections and affirmations, with a warm, nurturing tone to promote emotional healing.", "silence": "A session of complete silence, allowing the listener to focus inwardly, meditate, or simply enjoy a ambient sounds, binaural beats, music, or a combination of these over a set duration.", "focus enhancement": "A session designed to improve concentration and mental clarity, using a steady, motivating tone with techniques like anchoring or timed focus intervals."}, "premium": {"actions": {"subscribe": "Subscribe to Premium", "watchAd": "Watch ad to unlock", "watch": "Watch Ad", "loadingAd": "Loading ad...", "stopPreview": "Stop", "preview": "Preview", "delete": "Delete", "upload": "Upload", "subscribeShort": "Subscribe", "manageShort": "Manage", "watchShort": "Watch Ad"}, "ads": {"comingSoon": "This feature is coming soon! Thank you for your patience."}, "features": {"advancedSessions": {"title": "Advanced Sessions", "description": "Unlock exclusive access to advanced sessions like hypnosis and specialized training."}, "ambient": {"title": "Unlock Ambient Sounds", "description": "Enhance your sessions with a library of calming ambient sounds like rain, forests, or oceans."}, "binaural": {"title": "Unlock Binaural Beats", "description": "Access a wide range of frequencies to influence your brainwaves for deep meditation, focus, or relaxation."}, "custom": {"titleInList": "Upload Custom Audio"}}}, "main_menu": {"open_menu_aria_label": "Open menu"}, "adReward": {"unlockItemTitle": "Unlock Feature", "unlockItemDescription": "Watch a short ad to unlock this feature for a limited time.", "unlockNow": "Unlock with Ad", "watching": "Watching ad..."}, "subscription": {"title": "Subscription Status", "upgradePrompt": "Unlock all features by upgrading to a premium plan.", "upgradeButton": "Upgrade to Premium", "manage": "Manage Subscription", "status": {"free": "Free Plan", "premium": "Premium Active", "trial": "Free Trial"}, "trial": {"endsIn": "Your trial ends in {{count}} days.", "endsToday": "Your trial ends today!", "ended": "Your trial period has ended."}}, "recommendationKeywords": {"calm": "calm|serenity|peace|soothe|relax|anxiety|stress", "focus": "focus|attention|alert|clarity|performance|goal", "energy": "energy|motivation|vitality|dynamic|strength|power", "comfort": "comfort|soothe|sadness|compassion|gentleness|emotion", "creativity": "creative|inspiration|idea|imagine|solution|problem", "sleep": "sleep|dormant|night|insomnia|fall asleep", "approach": {"guided": "guided|story|voice|hypnosis|meditation", "explore": "explore|thoughts|journal|write|question", "visualize": "visualize|imagine|dream|goal|place", "body": "body|breath|movement|sensation|physical"}}, "recommendationAssistant": {"title": "Holistic Assistant", "results_title": "Based on our conversation, here is a suggestion for you:", "default_reason": "This session is designed to help you recenter and find calm.", "q_initial": ["Welcome. To help you find what you need, tell me, how are you feeling right now?", "Hello. Let's find the right session for you. What's the main emotion present for you today?"], "affirm_propose_somatic": ["Thank you for sharing. Let's connect with the body. Often, emotions manifest as physical sensations.", "I hear you. Let's explore how this feeling shows up in your body."], "q_somatic_location": ["Where does this feeling seem to be centered in your body?", "If you scan your body, where is this sensation most present?"], "q_verification": ["So, a feeling of {{context}}. Does that sound right?", "Okay, so we're noticing {{context}}. Is that an accurate description?"], "affirm_propose_cognitive": ["Okay, let's explore the mind. Our thoughts and feelings are deeply connected.", "Understood. Let's look at the thoughts that might be accompanying this feeling."], "q_cognitive_pattern": ["Which of these thought patterns resonates most with what you're experiencing?", "What does the voice in your head sound like right now?"], "affirm_propose_behavioral": ["That's helpful. Let's look at how this feeling might be influencing your actions.", "Okay. Sometimes, our feelings translate into specific behaviors. Let's see."], "q_behavioral_pattern": ["How has this feeling been showing up in your actions recently?", "Do any of these recent behaviors seem familiar?"], "affirm_propose_contextual": ["Understood. Where we are can influence how we feel. Let's explore the context.", "Thank you. Now, let's consider the situations where this feeling tends to appear."], "q_contextual_trigger": ["In which area of your life does this feeling seem to be most triggered?", "When does this feeling usually surface?"], "affirm_propose_metaphorical": ["Okay, let's try a different approach. Sometimes images speak louder than words.", "Let's step back for a moment and use our imagination."], "q_metaphorical_image": ["If this feeling were a landscape or an object, which of these would it be?", "Which of these images best represents your current inner state?"], "affirm_propose_energetic": ["Thank you. Let's tune into a more subtle level. Let's talk about your energy.", "Okay. Beyond thoughts and feelings, there is your vital energy."], "q_energetic_sensation": ["How would you describe your personal energy right now?", "Which of these best describes your current energetic state?"], "q_outcome": ["This has been very insightful. Thank you. To finish, what would be the ideal outcome for you after a session?", "Okay, with all this in mind, what state are you hoping to cultivate?"], "opt_idk": "I'm not sure", "opt_idk_or_other": "None of these / Not sure", "opt_surprise_me": "Surprise me", "opt_yes": "Yes, that's right", "opt_no": "No, not quite", "opt_pivot_cognitive_from_somatic": "It's not really in my body, more in my thoughts...", "opt_pivot_metaphorical_from_cognitive": "It's hard to describe with words, it's more of an image...", "opt_behavior_procrastination": "Putting things off", "opt_behavior_isolation": "Withdrawing from others", "opt_behavior_irritability": "Being easily annoyed", "opt_behavior_avoidance": "Avoiding difficult situations", "opt_context_work": "At work or related to it", "opt_context_relationships": "In my relationships", "opt_context_home": "At home", "opt_context_internal": "It comes from within, regardless of context", "opt_metaphor_storm": "A storm inside", "opt_metaphor_fog": "A thick fog", "opt_metaphor_weight": "A heavy weight", "opt_metaphor_knot": "A tight knot", "opt_energy_stuck": "Stuck or stagnant", "opt_energy_overactive": "Buzzing or overactive", "opt_energy_drained": "Drained or depleted", "opt_energy_scattered": "Scattered or unfocused"}, "dictionary": {"basic_emotions": {"anger": {"name": "Anger"}, "sadness": {"name": "Sadness"}, "joy": {"name": "<PERSON>"}, "fear": {"name": "Fear"}, "calm": {"name": "Calm"}}, "somatic_sensations": {"chest_oppression": {"name": "Chest oppression"}, "knotted_stomach": {"name": "Knotted stomach"}, "shoulder_neck_tension": {"name": "Shoulder and neck tension"}, "heaviness_limbs": {"name": "Heaviness in limbs"}, "racing_heart": {"name": "Racing heart"}}, "cognitive_patterns": {"mental_rumination": {"name": "Mental rumination"}, "future_anxiety": {"name": "Anxiety about the future"}, "critical_inner_dialogue": {"name": "Critical inner dialogue"}, "mental_fog": {"name": "Mental fog"}, "comparison": {"name": "Comparison to others"}}, "desired_outcomes": {"deep_calm": {"name": "Deep calm"}, "letting_go": {"name": "Letting go"}, "grounding": {"name": "Grounding"}, "stress_reduction": {"name": "Stress reduction"}, "increased_self_esteem": {"name": "Increased self-esteem"}, "joyful_living": {"name": "Joyful living"}}}, "voiceSelector": {"groupTitle": {"en-AU": "English (Australia)", "en-GB": "English (United Kingdom)", "en-IN": "English (India)", "en-US": "English (United States)", "es-ES": "Spanish (Spain)", "es-US": "Spanish (United States)", "fr-CA": "French (Canada)", "fr-FR": "French (France)"}}}