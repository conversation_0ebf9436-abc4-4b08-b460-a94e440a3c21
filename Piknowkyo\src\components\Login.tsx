// src/components/Login.tsx
import React, { useState } from 'react';
import { signInWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { auth } from '../firebase';
import { FcGoogle } from 'react-icons/fc';
import { useTranslation } from 'react-i18next'; // Importez useTranslation
import {
  Form,
  Input,
  Button,
  Separator,
  GoogleButton,
  Message,
  ToggleLink
} from './common/AuthStyles';

interface LoginProps {
  onToggleForm: (formType: 'login' | 'signup') => void;
}

const Login: React.FC<LoginProps> = ({ onToggleForm }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation(); // Initialisez useTranslation

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      await signInWithEmailAndPassword(auth, email, password);
      console.log("User logged in successfully with email/password");
      setSuccess(t('auth.common.success_redirect')); // Traduction ici
    } catch (err: any) {
      console.error("Error logging in with email/password:", err);
      if (err.code === 'auth/user-not-found' || err.code === 'auth/wrong-password') {
        setError(t('auth.login.error_invalid_credentials')); // Traduction ici
      } else {
        setError(t('auth.login.error_general')); // Traduction ici
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    const provider = new GoogleAuthProvider();
    try {
      await signInWithPopup(auth, provider);
      console.log("User logged in successfully with Google");
      setSuccess(t('auth.common.success_redirect')); // Traduction ici
    } catch (err: any) {
      console.error("Error logging in with Google:", err);
      if (err.code === 'auth/popup-closed-by-user') {
        setError(t('auth.login.error_google_popup_closed')); // Traduction ici
      } else if (err.code === 'auth/cancelled-popup-request') {
        setError(t('auth.login.error_google_popup_cancelled')); // Traduction ici
      } else {
        setError(t('auth.login.error_general')); // Traduction ici
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Form onSubmit={handleLogin}>
        <Input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder={t('auth.common.email_placeholder')} // Traduction ici
          required
          disabled={loading}
        />
        <Input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder={t('auth.common.password_placeholder')} // Traduction ici
          required
          disabled={loading}
        />
        <Button type="submit" disabled={loading}>
          {loading ? t('auth.login.button_loading') : t('auth.login.button')} {/* Traduction ici */}
        </Button>
      </Form>

      <Separator>{t('auth.common.or_separator')}</Separator> {/* Traduction ici */}

      <GoogleButton
        onClick={handleGoogleLogin}
        disabled={loading}
      >
        <FcGoogle size={24} />
        {loading ? t('auth.login.google_button_loading') : t('auth.login.google_button')} {/* Traduction ici */}
      </GoogleButton>

      {error && <Message type="error">{error}</Message>}
      {success && <Message type="success">{success}</Message>}
      {loading && !error && !success && <Message type="loading">{t('auth.common.please_wait_loading')}</Message>} {/* Traduction ici */}

      <ToggleLink onClick={() => onToggleForm('signup')} disabled={loading}>
        {t('auth.login.toggle_signup')} {/* Traduction ici */}
      </ToggleLink>
    </>
  );
};

export default Login;