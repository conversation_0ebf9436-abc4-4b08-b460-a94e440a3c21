// src/components/PaymentHistory.tsx
import React from 'react';
import { useSubscription } from '../hooks/useSubscription';
import { Card } from './ui';
import Table from './ui/Table'; // Correct import path for Table

type Payment = {
  date: string;
  description: string;
  amount: string;
  status: string;
};

const PaymentHistory: React.FC = () => {
  const { isLoading } = useSubscription();
  const paymentHistory: Payment[] = [];

  if (isLoading) return <div>Loading payment history...</div>;

  return (
    <Card title="Billing History">
      <Table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Description</th>
            <th>Amount</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          {paymentHistory.map((payment, index) => (
            <tr key={index}>
              <td>{new Date(payment.date).toLocaleDateString()}</td>
              <td>{payment.description}</td>
              <td>{payment.amount}</td>
              <td>{payment.status}</td>
            </tr>
          ))}
        </tbody>
      </Table>
    </Card>
  );
};

export default PaymentHistory;