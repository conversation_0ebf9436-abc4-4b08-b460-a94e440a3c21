// src/App.tsx

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import { useLang } from './LangProvider';
import { useTranslation } from 'react-i18next';
import { FiMenu } from 'react-icons/fi';
import { useTheme } from './ThemeProvider';
import { useAppStore } from './store/useAppStore';

// Pages
import HomePage from './pages/HomePage';
import SessionsPage from './pages/SessionsPage';
import SessionDetailPage from './pages/SessionDetailPage';
import PlayerPage from './pages/PlayerPage';
import JournalPage from './pages/JournalPage';
import StatsPage from './pages/StatsPage';
import ProfilePage from './pages/ProfilePage';
import MonetizationPage from './pages/MonetizationPage';
import GamesPage from './pages/GamesPage';
import BlogPage from './pages/BlogPage';
import AboutPage from './pages/AboutPage';
import LexiconPage from './pages/LexiconPage'; // Import the new LexiconPage
import AudioAssetsConfigPage from './pages/AudioAssetsConfigPage';

// Components
import SplashScreen from './components/SplashScreen';
import AuthPage from './components/AuthPage';
import SyncStatusIndicator from './components/SyncStatusIndicator';
import NetworkStatusNotifier from './components/NetworkStatusNotifier';
import BottomBar from './components/BottomBar';
import MainMenu from './components/MainMenu';
import Logout from './components/Logout';
import DefinitionModal from './components/DefinitionModal'; // Import the global definition modal

const AuthenticatedApp = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const { darkMode, toggleTheme } = useTheme();
  const { t } = useTranslation();

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);

  return (
    <Router>
      <div className={`app-container ${darkMode ? 'dark-theme' : 'light-theme'}`}>
        <header className="app-header">
          <div className="logo">
            <img src="/logo192.png" alt={t('app.logo_alt', 'PiKnowKyo')} style={{ height: 40, marginRight: 12 }} />
            <span>{t('app.name', 'PiKnowKyo')}</span>
          </div>
          <div className="header-actions">
            <SyncStatusIndicator />
            <Logout />
            <button
              className="theme-toggle"
              onClick={toggleTheme}
              aria-label={darkMode ? t('app.theme_light', 'Light theme') : t('app.theme_dark', 'Dark theme')}
            >
              {darkMode ? '☀️' : '🌙'}
            </button>
            <button
              className="menu-toggle"
              onClick={toggleMenu}
              aria-label={t('main_menu.open_menu_aria_label', 'Open menu')}
            >
              <FiMenu size={24} />
            </button>
          </div>
        </header>

        <MainMenu isOpen={menuOpen} onClose={closeMenu} />

        <main className="app-main">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/sessions" element={<SessionsPage />} />
            <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
            <Route path="/player/:sessionId" element={<PlayerPage />} />
            <Route path="/journal" element={<JournalPage />} />
            <Route path="/lexicon" element={<LexiconPage />} /> {/* Add the route for the LexiconPage */}
            <Route path="/stats" element={<StatsPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/monetization" element={<MonetizationPage />} />
            <Route path="/games" element={<GamesPage />} />
            <Route path="/blog" element={<BlogPage />} />
            <Route path="/settings/audio-assets" element={<AudioAssetsConfigPage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="*" element={<HomePage />} />
          </Routes>
        </main>

        {/* Global components that can be triggered from anywhere */}
        <DefinitionModal /> 

        <NetworkStatusNotifier />
        <BottomBar />
      </div>
    </Router>
  );
};

const App: React.FC = () => {
  const { t } = useTranslation();
  const { user, loading: authLoading } = useAuth();
  const { lang, isLangLoading } = useLang();
  
  const initStore = useAppStore((state) => state.init);
  const cleanupStore = useAppStore((state) => state.cleanup);
  const isStoreInitialized = useAppStore((state) => state.isInitialized);

  // --- FIX: Split into two separate, specialized useEffects ---

  // Effect 1: Handles store INITIALIZATION
  useEffect(() => {
    // Condition to initialize: user logged in, all loading finished, and store not yet initialized.
    if (user && !authLoading && !isLangLoading && !isStoreInitialized) {
      console.log(`[App] All clear. User authenticated ('${user.uid}') and language ('${lang}') loaded. Initializing data store...`);
      initStore(user.uid, lang);
    }
  }, [user, authLoading, lang, isLangLoading, isStoreInitialized, initStore]);

  // Effect 2: Handles store CLEANUP
  useEffect(() => {
    // Condition to clean up: auth is resolved, user is logged out, and the store was previously initialized.
    // This effect does NOT depend on isLangLoading, so it won't be re-triggered by language changes.
    if (!user && !authLoading && isStoreInitialized) {
      console.log("[App] User is logged out. Cleaning up data store.");
      cleanupStore();
    }
  }, [user, authLoading, isStoreInitialized, cleanupStore]);

  const isAppLoading = authLoading || isLangLoading || (user && !isStoreInitialized);

  if (isAppLoading) {
    let message = t('loading.default', 'Loading...');
    if (authLoading) {
      message = t('loading.authenticating', 'Authenticating...');
    } else if (isLangLoading) {
      message = t('loading.language', 'Setting language...');
    } else if (user && !isStoreInitialized) {
      message = t('loading.initializing', 'Initializing data...');
    }
    
    return <SplashScreen message={message} />;
  }

  if (!user) {
    return <AuthPage />;
  }
  
  return <AuthenticatedApp />;
};

export default App;