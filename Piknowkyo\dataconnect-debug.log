I0602 12:16:46.755799   11492 load.go:37] Reloading schema and connectors...
I0602 12:16:46.771509   11492 control.go:73] [/emulator/piknowkyo ba0c] UpdateResources(): done
Schema: sources: schema\schema.gql [2088B] 
Connector "default": sources: connector\mutations.gql [1233B] connector\queries.gql [1802B] 
I0602 12:16:46.785681   11492 collector.go:107] schema extensions wrote into "C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo\\Piknowkyo\\dataconnect\\.dataconnect\\schema"
Generated sources: prelude.gql [71531B] 
I0602 12:16:46.785681   11492 load.go:115] Finished reloading
I0602 12:16:46.785681   11492 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:9399
I0602 12:16:46.785681   11492 dev.go:95] Listening on address (HTTP + gRPC): [::1]:9399
I0602 12:16:49.231690   11492 control.go:73] [/emulator/piknowkyo d680] UpdateResources(): done
Schema: sources: schema\schema.gql [2088B] 
Connector "default": sources: connector\mutations.gql [1233B] connector\queries.gql [1802B] 
