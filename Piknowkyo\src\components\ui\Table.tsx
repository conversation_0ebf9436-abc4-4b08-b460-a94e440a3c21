import React from 'react';
import styled from 'styled-components';

interface TableProps {
  children: React.ReactNode;
}

const TableContainer = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
`;

const TableHead = styled.thead`
  background-color: ${({ theme }) => theme.tableHeaderBackground};
`;

const TableBody = styled.tbody`
  & > tr:nth-child(even) {
    background-color: ${({ theme }) => theme.tableRowEvenBackground};
  }
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${({ theme }) => theme.tableRowHoverBackground} !important;
  }
`;

const TableCell = styled.td`
  padding: 8px;
  border: 1px solid ${({ theme }) => theme.tableBorder};
`;

const TableHeaderCell = styled.th`
  padding: 8px;
  border: 1px solid ${({ theme }) => theme.tableBorder};
  text-align: left;
`;

const Table: React.FC<TableProps> & {
  Head: React.FC<TableProps>;
  Body: React.FC<TableProps>;
  Row: React.FC<TableProps>;
  Cell: React.FC<TableProps>;
  HeaderCell: React.FC<TableProps>;
} = ({ children }) => {
  return <TableContainer>{children}</TableContainer>;
};

Table.Head = ({ children }) => <TableHead>{children}</TableHead>;
Table.Body = ({ children }) => <TableBody>{children}</TableBody>;
Table.Row = ({ children }) => <TableRow>{children}</TableRow>;
Table.Cell = ({ children }) => <TableCell>{children}</TableCell>;
Table.HeaderCell = ({ children }) => <TableHeaderCell>{children}</TableHeaderCell>;

export default Table;