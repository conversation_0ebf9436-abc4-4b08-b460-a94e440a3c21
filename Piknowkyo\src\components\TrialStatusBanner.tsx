// src/components/TrialStatusBanner.tsx

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSubscription } from '../hooks/useSubscription';
import styled from 'styled-components';

const BannerContainer = styled.div`
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
  padding: 12px 16px;
  text-align: center;
  font-weight: 600;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MessageContainer = styled.div`
  flex: 1;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #000;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.7;
  &:hover {
    opacity: 1;
  }
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TrialStatusBanner: React.FC = () => {
  const { t } = useTranslation();
  const {
    subscription,
    trialDaysRemaining,
    isLoading,
    isUpdating,
    error
  } = useSubscription();
  const isTrialActive = subscription?.isTrialActive;
  const [visible, setVisible] = React.useState(true);
  
  // FIX: Use 'trialEnds' instead of 'trialEndDate' to match the updated SubscriptionState interface.
  const trialEnds = subscription?.trialEnds;

  // Hide banner if trial is not active or user dismissed it
  if (!isTrialActive || !visible) {
    return null;
  }

  return (
    <BannerContainer>
      <MessageContainer>
        {isLoading ? (
          <div>Loading trial status...</div>
        ) : error ? (
          <div>Error: {error.message}</div>
        ) : (
          t('trial.banner.message', 'Your trial ends on {{date}} ({{days}} days remaining)', {
            days: trialDaysRemaining,
            date: trialEnds ? new Date(trialEnds).toLocaleString(undefined, { timeZoneName: 'short' }) : 'N/A'
          })
        )}
      </MessageContainer>
      <CloseButton
        onClick={() => setVisible(false)}
        disabled={isUpdating}
        aria-label={t('common.close', 'Close')}
      >
        ×
      </CloseButton>
    </BannerContainer>
  );
};

export default TrialStatusBanner;