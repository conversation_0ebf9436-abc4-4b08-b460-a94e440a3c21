// src/components/Logout.tsx
import React from 'react';
import { signOut } from "firebase/auth";
import { auth } from '../firebase';
import styled from 'styled-components';
import AppIcon from './AppIcon'; // Import AppIcon

// Styled component for the logout button
const LogoutButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.text};
  cursor: pointer;
  padding: 8px;
  border-radius: 50%; // Make it circular to fit the icon nicely
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.surfaceAlt};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}40;
  }
`;

const Logout: React.FC = () => {
  const handleLogout = async () => {
    try {
      await signOut(auth);
      console.log("User logged out successfully");
      // You could add a user confirmation message here if needed.
    } catch (error) {
      console.error("Error logging out:", error);
      // You could display an error message to the user here.
    }
  };

  return (
    <LogoutButton onClick={handleLogout} title="Logout">
      <AppIcon name="logout" size={22} />
    </LogoutButton>
  );
};

export default Logout;