// src/components/SplashScreen.tsx

import React from 'react';
import styled, { keyframes } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { usePWA } from '../hooks/usePWA'; // Import the usePWA hook

// --- DEFINE PROPS INTERFACE ---
// We tell TypeScript that this component can receive an optional 'message' prop.
interface SplashScreenProps {
  message?: string;
}

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const Splash = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: ${({ theme }) => theme.background};
  color: ${({ theme }) => theme.text}; // Set a base text color
  animation: ${fadeIn} 0.7s;
  padding: 1rem;
  text-align: center;
`;

const Logo = styled.img`
  width: 150px; // Slightly smaller for better balance with text
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.primary};
  font-size: 2rem;
  margin-bottom: 1rem; // Add margin bottom
`;

// --- NEW STYLED COMPONENT FOR THE MESSAGE ---
const LoadingMessage = styled.p`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 1rem;
  margin-top: 1rem;
  height: 24px; // Reserve space to prevent layout shift
`;

const InstallButton = styled.button`
  margin-top: 1.5rem;
  padding: 0.75rem 1.5rem;
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: ${({ theme }) => theme.primaryHover};
  }
`;

// --- UPDATE COMPONENT SIGNATURE TO ACCEPT PROPS ---
const SplashScreen: React.FC<SplashScreenProps> = ({ message }) => {
  const { t } = useTranslation();
  const { isInstallable, promptInstall } = usePWA();

  return (
    <Splash>
      <Logo src="/logo192.png" alt="Piknowkyo logo" />
      <Title>{t('app.name')}</Title>
      
      {/* --- RENDER THE MESSAGE IF IT EXISTS --- */}
      <LoadingMessage>
        {message ? message : (isInstallable ? t('pwa.install_prompt', 'Install the app for the best experience!') : '')}
      </LoadingMessage>

      {isInstallable && (
        <InstallButton onClick={promptInstall}>
          {t('pwa.install_button', 'Install App')}
        </InstallButton>
      )}
    </Splash>
  );
};

export default SplashScreen;