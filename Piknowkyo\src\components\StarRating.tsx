// src/components/StarRating.tsx

import React, { useState } from 'react';
import styled from 'styled-components';
import { FiStar } from 'react-icons/fi';

// --- Styled Components ---

const StarContainer = styled.div<{ $interactive: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 0.2rem;
  cursor: ${({ $interactive }) => ($interactive ? 'pointer' : 'default')};
`;

const StarIcon = styled(FiStar)<{ $filled: boolean; $isHovering: boolean }>`
  color: ${({ theme, $filled, $isHovering }) =>
    $filled || $isHovering ? theme.accent || '#FFC107' : theme.borderSlight};
  transition: color 0.2s, transform 0.1s;
  transform: ${({ $isHovering }) => ($isHovering ? 'scale(1.15)' : 'scale(1)')};
  
  /* Use fill for a more solid star appearance */
  fill: ${({ theme, $filled, $isHovering }) =>
    $filled || $isHovering ? theme.accent || '#FFC107' : 'transparent'};
`;

// --- Component Props ---

interface StarRatingProps {
  rating: number; // The current rating value (e.g., 3.5)
  setRating?: (rating: number) => void; // If provided, the component is interactive
  size?: number; // Optional size for the icons
  totalStars?: number; // Total number of stars, defaults to 5
}

// --- Component Implementation ---

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  setRating,
  size = 24,
  totalStars = 5,
}) => {
  // State to handle hover effect for interactive ratings
  const [hoverRating, setHoverRating] = useState(0);

  const isInteractive = !!setRating;

  const handleMouseEnter = (index: number) => {
    if (!isInteractive) return;
    setHoverRating(index + 1);
  };

  const handleMouseLeave = () => {
    if (!isInteractive) return;
    setHoverRating(0);
  };

  const handleClick = (index: number) => {
    if (!isInteractive) return;
    // This allows un-setting the rating by clicking the same star again, which is a nice UX touch.
    const newRating = index + 1 === rating ? 0 : index + 1;
    setRating(newRating);
  };

  return (
    <StarContainer
      $interactive={isInteractive}
      onMouseLeave={handleMouseLeave}
      aria-label={`Rating: ${rating} out of ${totalStars} stars.`}
    >
      {[...Array(totalStars)].map((_, index) => {
        const starValue = index + 1;
        // A star is filled if its value is less than or equal to the current hover/rating
        const isFilled = starValue <= (hoverRating || rating);

        return (
          <StarIcon
            key={index}
            size={size}
            $filled={isFilled}
            $isHovering={isInteractive && starValue <= hoverRating}
            onMouseEnter={() => handleMouseEnter(index)}
            onClick={() => handleClick(index)}
            aria-hidden="true" // Hide individual stars from screen readers; the container has the full label.
          />
        );
      })}
    </StarContainer>
  );
};

export default StarRating;