// src/games/common/components/ScoreDisplay.tsx
import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
// CORRECTION : Remplacement de FiTrophy par FiAward
import { FiAward } from 'react-icons/fi';

const ScoreContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: ${({ theme }) => theme.textLight || 'white'};
`;

interface ScoreDisplayProps {
  score: number;
}

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ score }) => {
  const { t } = useTranslation();

  return (
    <ScoreContainer>
      <FiAward /> {t('game.score', 'Score')}: {score}
    </ScoreContainer>
  );
};

export default ScoreDisplay;