// pmng/src/pages/UserManagementPage.tsx
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { db } from '../firebase';
import { collection, getDocs } from 'firebase/firestore';
import { FiEdit, FiSearch } from 'react-icons/fi';
import UserEditModal from './UserEditModal'; // Import the new modal

// --- Styled Components (no changes) ---
const PageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
  
  svg {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 15px 10px 45px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.surface};
  font-size: 1rem;
`;

const UserTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: ${({ theme }) => theme.surface};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.cardShadow};
`;

const TableHead = styled.thead`
  background-color: ${({ theme }) => theme.tableHeaderBackground};
  th {
    padding: 12px 15px;
    text-align: left;
    font-size: 0.85rem;
    text-transform: uppercase;
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.tableBorder};
  &:last-child {
    border-bottom: none;
  }
  &:hover {
    background-color: ${({ theme }) => theme.tableRowHoverBackground};
  }
`;

const TableCell = styled.td`
  padding: 12px 15px;
  vertical-align: middle;

  &.uid-cell {
    font-family: monospace;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const ActionButton = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 1px solid ${({ theme }) => theme.border};
  background-color: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.primary};
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border-color: ${({ theme }) => theme.primary};
  }
`;
// --- Component ---
interface User {
  id: string;
}

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // --- NEW: State for managing the modal ---
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const usersCollectionRef = collection(db, 'users');
      const querySnapshot = await getDocs(usersCollectionRef);
      
      // CORRECTED: Replaced .map() with a for...of loop for better type inference
      const fetchedUsers: User[] = [];
      for (const doc of querySnapshot.docs) {
        fetchedUsers.push({
          id: doc.id,
        });
      }
      setUsers(fetchedUsers);

    } catch (err) {
      console.error("Firebase fetch error:", err); // Log the full error for debugging
      setError("Failed to fetch users. Check Firestore rules and network connection.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const filteredUsers = useMemo(() => {
    if (!searchTerm) return users;
    return users.filter(user => user.id.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [searchTerm, users]);

  // --- NEW: Handlers for the modal ---
  const handleEditUser = (userId: string) => {
    setEditingUserId(userId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setEditingUserId(null);
    setIsModalOpen(false);
  };

  const handleSaveAndRefresh = () => {
      // After saving, we re-fetch the user list in case data changed
      // For now, just closing the modal is fine. A full refresh can be added later.
      // fetchUsers(); 
      handleCloseModal();
  }

  if (isLoading) return <div>Loading users...</div>;
  if (error) return <div style={{ color: 'red' }}>{error}</div>;

  return (
    <PageWrapper>
      <Header>
        <h1>User Management ({filteredUsers.length})</h1>
        <SearchContainer>
          <FiSearch />
          <SearchInput
            type="text"
            placeholder="Search by UID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>
      </Header>

      <UserTable>
        <TableHead>
          <tr>
            <th>User UID</th>
            <th style={{ textAlign: 'right' }}>Actions</th>
          </tr>
        </TableHead>
        <tbody>
          {filteredUsers.map(user => (
            <TableRow key={user.id}>
              <TableCell className="uid-cell">{user.id}</TableCell>
              <TableCell style={{ textAlign: 'right' }}>
                <ActionButton onClick={() => handleEditUser(user.id)}>
                  <FiEdit size={14} />
                  <span>Edit</span>
                </ActionButton>
              </TableCell>
            </TableRow>
          ))}
        </tbody>
      </UserTable>
      
      {filteredUsers.length === 0 && !isLoading && <p>No users found.</p>}

      {/* --- NEW: Render the modal conditionally --- */}
      {isModalOpen && editingUserId && (
        <UserEditModal 
          userId={editingUserId} 
          onClose={handleCloseModal}
          onSave={handleSaveAndRefresh}
        />
      )}
    </PageWrapper>
  );
};

export default UserManagementPage;