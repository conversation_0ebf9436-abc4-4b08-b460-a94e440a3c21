// pmng/src/pages/UserManagementPage.tsx
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { db, auth } from '../firebase';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { FiEdit, FiSearch, FiUser, FiClock, FiCalendar, FiRefreshCw, FiDatabase, FiUserX } from 'react-icons/fi';
import CryptoJS from 'crypto-js';
import UserEditModal from './UserEditModal'; // Import the new modal

// --- Styled Components (no changes) ---
const PageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
  
  svg {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 15px 10px 45px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.surface};
  font-size: 1rem;
`;

const UserTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: ${({ theme }) => theme.surface};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.cardShadow};
`;

const TableHead = styled.thead`
  background-color: ${({ theme }) => theme.tableHeaderBackground};
  th {
    padding: 12px 15px;
    text-align: left;
    font-size: 0.85rem;
    text-transform: uppercase;
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.tableBorder};
  &:last-child {
    border-bottom: none;
  }
  &:hover {
    background-color: ${({ theme }) => theme.tableRowHoverBackground};
  }
`;

const TableCell = styled.td`
  padding: 12px 15px;
  vertical-align: middle;

  &.uid-cell {
    font-family: monospace;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
  }
`;

const ActionButton = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 1px solid ${({ theme }) => theme.border};
  background-color: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.primary};
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border-color: ${({ theme }) => theme.primary};
  }
`;

const StatusBadge = styled.span<{ $status: 'premium' | 'trial' | 'expired' | 'free' }>`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  position: relative;

  ${({ $status, theme }) => {
    switch ($status) {
      case 'premium':
        return `
          background-color: ${theme.success}20;
          color: ${theme.success};
          border: 1px solid ${theme.success}40;
          &::before {
            content: '✓';
            margin-right: 2px;
          }
        `;
      case 'trial':
        return `
          background-color: ${theme.warning || '#f59e0b'}20;
          color: ${theme.warning || '#f59e0b'};
          border: 1px solid ${theme.warning || '#f59e0b'}40;
          &::before {
            content: '⏱';
            margin-right: 2px;
          }
        `;
      case 'expired':
        return `
          background-color: ${theme.danger}20;
          color: ${theme.danger};
          border: 1px solid ${theme.danger}40;
          &::before {
            content: '⚠';
            margin-right: 2px;
          }
        `;
      default:
        return `
          background-color: ${theme.textSecondary}20;
          color: ${theme.textSecondary};
          border: 1px solid ${theme.textSecondary}40;
          &::before {
            content: '○';
            margin-right: 2px;
          }
        `;
    }
  }}
`;

const ProgressBar = styled.div<{ $progress: number }>`
  width: 100%;
  height: 4px;
  background-color: ${({ theme }) => theme.border};
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;

  &::after {
    content: '';
    display: block;
    height: 100%;
    width: ${({ $progress }) => $progress}%;
    background-color: ${({ theme, $progress }) =>
      $progress > 80 ? theme.danger :
      $progress > 50 ? theme.warning || '#f59e0b' :
      theme.success};
    transition: width 0.3s ease;
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const UserMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.8rem;
  color: ${({ theme }) => theme.textSecondary};

  .meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;

// --- Component ---
interface UserSubscription {
  isActive: boolean;
  isTrialActive: boolean;
  tier: 'free' | 'premium';
  trialEnds: string | null;
  renewsAt: string | null;
}

interface UserActivity {
  totalMinutes: number;
  streak: number;
}

interface UserProfile {
  anonymousPseudo: string;
  email?: string;
  displayName?: string;
  name?: string;
}

interface UserAuthInfo {
  email: string | null;
  displayName: string | null;
  createdAt: string | null;
  emailVerified: boolean;
  disabled: boolean;
  lastSignInTime: string | null;
}

interface FirestoreDataStatus {
  hasFirestoreData: boolean;
  hasSubscription: boolean;
  hasProfile: boolean;
  hasActivity: boolean;
  hasPreferences: boolean;
  mainDocument?: any;
}

interface EnrichedUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  emailVerified: boolean;
  disabled: boolean;
  creationTime: string;
  lastSignInTime: string | null;
  hasFirestoreData: boolean;
  firestoreData: FirestoreDataStatus | null;
  // Legacy fields for compatibility
  id?: string;
  subscription?: UserSubscription;
  activity?: UserActivity;
  profile?: UserProfile;
  authInfo?: UserAuthInfo;
}

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<EnrichedUser[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);

  // --- NEW: State for managing the modal ---
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);

  const decryptUserData = useCallback(async (userId: string, dataType: string) => {
    try {
      const docRef = doc(db, 'users', userId, dataType, 'main');
      const docSnap = await getDoc(docRef);
      if (docSnap.exists() && docSnap.data()?.encrypted) {
        const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, userId).toString(CryptoJS.enc.Utf8);
        return JSON.parse(decrypted);
      }
    } catch (error) {
      console.warn(`Failed to decrypt ${dataType} for user ${userId}:`, error);
    }
    return null;
  }, []);

  const callListAllUsersFunction = useCallback(async (): Promise<EnrichedUser[]> => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const idToken = await currentUser.getIdToken();
      const functionUrl = 'https://us-central1-piknowkyo-777.cloudfunctions.net/listAllUsers';

      const response = await fetch(functionUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Unknown error from Cloud Function');
      }

      console.log(`Retrieved ${data.totalUsers} users from Cloud Function`);
      return data.users;

    } catch (error) {
      console.error('Error calling listAllUsers function:', error);
      throw error;
    }
  }, []);

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setLoadingProgress(0);

    try {
      setLoadingProgress(20);

      // Get all users from Cloud Function
      const allUsers = await callListAllUsersFunction();
      setLoadingProgress(60);

      // Enrich users with decrypted Firestore data for those who have it
      const enrichedUsers: EnrichedUser[] = [];
      const batchSize = 10;

      for (let i = 0; i < allUsers.length; i += batchSize) {
        const batch = allUsers.slice(i, i + batchSize);

        const batchPromises = batch.map(async (user) => {
          let subscription = null;
          let activity = null;
          let profile = null;

          // Only try to decrypt data if user has Firestore data
          if (user.hasFirestoreData && user.firestoreData) {
            const promises = [];

            if (user.firestoreData.hasSubscription) {
              promises.push(decryptUserData(user.uid, 'subscription'));
            } else {
              promises.push(Promise.resolve(null));
            }

            if (user.firestoreData.hasActivity) {
              promises.push(decryptUserData(user.uid, 'activity'));
            } else {
              promises.push(Promise.resolve(null));
            }

            if (user.firestoreData.hasProfile) {
              promises.push(decryptUserData(user.uid, 'profile'));
            } else {
              promises.push(Promise.resolve(null));
            }

            [subscription, activity, profile] = await Promise.all(promises);
          }

          return {
            ...user,
            id: user.uid, // For compatibility
            subscription,
            activity,
            profile,
            authInfo: {
              email: user.email,
              displayName: user.displayName,
              createdAt: user.creationTime,
              emailVerified: user.emailVerified,
              disabled: user.disabled,
              lastSignInTime: user.lastSignInTime
            }
          };
        });

        const batchResults = await Promise.all(batchPromises);
        enrichedUsers.push(...batchResults);

        // Update progress
        const progress = 60 + ((i + batchSize) / allUsers.length) * 30;
        setLoadingProgress(Math.min(progress, 90));

        // Update UI with partial results for better UX
        setUsers([...enrichedUsers]);
      }

      setUsers(enrichedUsers);
      setLoadingProgress(100);
      console.log(`Loaded ${enrichedUsers.length} users with enriched data`);

    } catch (err: any) {
      console.error("Error fetching users:", err);
      setError(`Failed to fetch users: ${err.message}`);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [callListAllUsersFunction, decryptUserData]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const getUserStatus = useCallback((user: EnrichedUser): 'premium' | 'trial' | 'expired' | 'free' => {
    if (!user.subscription) return 'free';

    const { isActive, isTrialActive, trialEnds } = user.subscription;

    if (isActive) return 'premium';

    if (isTrialActive && trialEnds) {
      const now = new Date();
      const trialEndDate = new Date(trialEnds);
      return trialEndDate > now ? 'trial' : 'expired';
    }

    return 'free';
  }, []);

  const getTrialDaysRemaining = useCallback((user: EnrichedUser): number => {
    if (!user.subscription?.isTrialActive || !user.subscription.trialEnds) return 0;

    const now = new Date();
    const trialEnd = new Date(user.subscription.trialEnds);
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }, []);

  const getTrialProgress = useCallback((user: EnrichedUser): number => {
    if (!user.subscription?.isTrialActive || !user.subscription.trialStarts || !user.subscription.trialEnds) return 0;

    const start = new Date(user.subscription.trialStarts);
    const end = new Date(user.subscription.trialEnds);
    const now = new Date();

    const totalDuration = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();

    if (totalDuration <= 0) return 100;
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  }, []);

  const filteredUsers = useMemo(() => {
    if (!searchTerm) return users;
    const term = searchTerm.toLowerCase();
    return users.filter(user =>
      user.uid.toLowerCase().includes(term) ||
      (user.id && user.id.toLowerCase().includes(term)) ||
      user.profile?.anonymousPseudo?.toLowerCase().includes(term) ||
      user.email?.toLowerCase().includes(term) ||
      user.displayName?.toLowerCase().includes(term) ||
      user.authInfo?.email?.toLowerCase().includes(term) ||
      user.authInfo?.displayName?.toLowerCase().includes(term)
    );
  }, [searchTerm, users]);

  // --- NEW: Handlers for the modal ---
  const handleEditUser = (userId: string) => {
    setEditingUserId(userId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setEditingUserId(null);
    setIsModalOpen(false);
  };

  const handleSaveAndRefresh = () => {
      // After saving, we re-fetch the user list in case data changed
      // For now, just closing the modal is fine. A full refresh can be added later.
      // fetchUsers(); 
      handleCloseModal();
  }

  if (isLoading) return (
    <div>
      <div>Loading users...</div>
      {loadingProgress > 0 && (
        <div style={{ marginTop: '10px' }}>
          <div style={{ fontSize: '0.9rem', marginBottom: '5px' }}>
            Progress: {Math.round(loadingProgress)}%
          </div>
          <ProgressBar $progress={loadingProgress} />
        </div>
      )}
    </div>
  );
  if (error) return <div style={{ color: 'red' }}>{error}</div>;

  return (
    <PageWrapper>
      <Header>
        <div>
          <h1>User Management</h1>
          <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '4px' }}>
            Showing {filteredUsers.length} of {users.length} users
            {searchTerm && ` (filtered by "${searchTerm}")`}
          </div>
        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', width: '100%' }}>
          <SearchContainer style={{ flex: 1 }}>
            <FiSearch />
            <SearchInput
              type="text"
              placeholder="Search by UID, email, name, or pseudo..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
          <ActionButton
            onClick={fetchUsers}
            disabled={isLoading}
            style={{
              minWidth: 'auto',
              padding: '12px 20px',
              height: '44px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              flexShrink: 0
            }}
          >
            <FiRefreshCw
              size={16}
              style={{
                transform: isLoading ? 'rotate(360deg)' : 'none',
                transition: 'transform 1s linear'
              }}
            />
            <span>Refresh</span>
          </ActionButton>
        </div>
      </Header>

      <UserTable>
        <TableHead>
          <tr>
            <th>User Information</th>
            <th>Status</th>
            <th>Activity</th>
            <th style={{ textAlign: 'right' }}>Actions</th>
          </tr>
        </TableHead>
        <tbody>
          {filteredUsers.map(user => {
            const status = getUserStatus(user);
            const trialDays = getTrialDaysRemaining(user);
            const trialProgress = getTrialProgress(user);

            return (
              <TableRow key={user.uid}>
                <TableCell>
                  <UserInfo>
                    <div className="uid-cell">{user.uid}</div>
                    {user.email ? (
                      <div style={{ fontSize: '0.9rem', fontWeight: '500', color: '#0066cc' }}>
                        📧 {user.email}
                        {user.emailVerified ? (
                          <span style={{ color: '#28a745', marginLeft: '5px' }}>✓</span>
                        ) : (
                          <span style={{ color: '#dc3545', marginLeft: '5px' }}>⚠</span>
                        )}
                      </div>
                    ) : (
                      <div style={{ fontSize: '0.85rem', color: '#999', fontStyle: 'italic' }}>
                        📧 No email found
                      </div>
                    )}
                    {user.displayName && (
                      <div style={{ fontSize: '0.9rem', fontWeight: '500' }}>
                        👤 {user.displayName}
                      </div>
                    )}
                    {user.profile?.anonymousPseudo && (
                      <div style={{ fontSize: '0.85rem', color: '#666' }}>
                        🎭 {user.profile.anonymousPseudo}
                      </div>
                    )}
                    <UserMeta>
                      <div className="meta-item">
                        <FiCalendar size={12} />
                        Joined: {new Date(user.creationTime).toLocaleDateString()}
                      </div>
                      {user.lastSignInTime && (
                        <div className="meta-item">
                          <FiClock size={12} />
                          Last: {new Date(user.lastSignInTime).toLocaleDateString()}
                        </div>
                      )}
                      <div className="meta-item">
                        <FiDatabase size={12} />
                        {user.hasFirestoreData ? (
                          <span style={{ color: '#28a745' }}>Firestore ✓</span>
                        ) : (
                          <span style={{ color: '#dc3545' }}>No Firestore data</span>
                        )}
                      </div>
                      {user.disabled && (
                        <div className="meta-item">
                          <FiUserX size={12} />
                          <span style={{ color: '#dc3545' }}>Disabled</span>
                        </div>
                      )}
                    </UserMeta>
                  </UserInfo>
                </TableCell>
                <TableCell>
                  <StatusBadge $status={status}>
                    {status === 'trial' ? `Trial (${trialDays}d)` : status}
                  </StatusBadge>
                  {status === 'trial' && trialProgress > 0 && (
                    <ProgressBar $progress={trialProgress} />
                  )}
                  {user.subscription && (
                    <UserMeta>
                      {user.subscription.trialEnds && status === 'trial' && (
                        <div className="meta-item">
                          <FiCalendar size={12} />
                          Expires: {new Date(user.subscription.trialEnds).toLocaleDateString()}
                        </div>
                      )}
                      {user.subscription.renewsAt && status === 'premium' && (
                        <div className="meta-item">
                          <FiCalendar size={12} />
                          Renews: {new Date(user.subscription.renewsAt).toLocaleDateString()}
                        </div>
                      )}
                    </UserMeta>
                  )}
                </TableCell>
                <TableCell>
                  {user.activity ? (
                    <UserMeta>
                      <div className="meta-item">
                        <FiClock size={12} />
                        {Math.round(user.activity.totalMinutes)} min
                      </div>
                      <div className="meta-item">
                        🔥 {user.activity.streak} streak
                      </div>
                    </UserMeta>
                  ) : (
                    <span style={{ color: '#999', fontSize: '0.8rem' }}>No data</span>
                  )}
                </TableCell>
                <TableCell style={{ textAlign: 'right' }}>
                  <ActionButton onClick={() => handleEditUser(user.uid)}>
                    <FiEdit size={14} />
                    <span>Edit</span>
                  </ActionButton>
                </TableCell>
              </TableRow>
            );
          })}
        </tbody>
      </UserTable>
      
      {filteredUsers.length === 0 && !isLoading && <p>No users found.</p>}

      {/* --- NEW: Render the modal conditionally --- */}
      {isModalOpen && editingUserId && (
        <UserEditModal 
          userId={editingUserId} 
          onClose={handleCloseModal}
          onSave={handleSaveAndRefresh}
        />
      )}
    </PageWrapper>
  );
};

export default UserManagementPage;