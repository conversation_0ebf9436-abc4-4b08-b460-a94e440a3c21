import { Dictionary, DictionaryElement } from '../../types/definitions';

// The complete set of domains we can explore. This is our map.
export type ExplorationDomain = 
  | 'initial'
  | 'emotional'
  | 'somatic'
  | 'cognitive'
  | 'behavioral'
  | 'metaphorical'
  | 'energetic'
  | 'contextual'
  | 'outcome'; // The final goal-setting domain

// The distinct phases of our therapeutic conversation loop.
export type StateNodeType =
  | 'proposing_exploration'   // "Let's explore..."
  | 'exploratory_question'    // "What do you feel/think/see...?"
  | 'verification_check'      // "So, a tightness in the chest. Is that right?"
  | 'recommendation_end';     // The final state.

// The user's profile, built dynamically throughout the conversation.
export interface UserProfile {
  emotions: string[];
  somatic_markers: string[];
  cognitive_patterns: string[];
  behavioral_patterns: string[];
  energetic_states: string[];
  contextual_triggers: string[];
  metaphorical_images: string[];
  desired_outcomes: string[];
}

// The complete state of an ongoing conversation.
export interface ConversationState {
  currentStepId: string;
  userProfile: UserProfile;
  // This is the memory of the assistant: which domains has it successfully explored?
  completedDomains: ExplorationDomain[];
  // Tracks the history of states visited to enable a "back" button later.
  pathHistory: string[];
}

export type TFunction = (key: string, options?: any) => string;
export type Language = 'en' | 'fr' | 'es';

// The structure for each state node in our state machine.
export interface StateNode {
  type: StateNodeType;
  domain: ExplorationDomain;
  
  // Gets the text to display.
  getText: (t: TFunction, profile: UserProfile, dictionary: Dictionary, lang: Language) => string;
  
  // Gets the possible answers for question types.
  getOptions?: (dictionary: Dictionary, profile: UserProfile, lang: Language, t: TFunction) => (DictionaryElement & { label: string })[];
  
  // The brain: determines the next step and updates the user profile and conversation state.
  determineNextState: (answerKey: string | null, state: ConversationState) => {
    nextStateId: string;
    updatedProfile: UserProfile;
    updatedCompletedDomains: ExplorationDomain[];
  };
}

// Re-export Dictionary and DictionaryElement as types
export type { Dictionary, DictionaryElement };