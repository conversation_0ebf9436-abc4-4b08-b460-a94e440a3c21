// src/pages/SessionDetailPage.tsx
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
import { useAppStore, UserPreferences } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import { useShallow } from 'zustand/react/shallow';
import { JournalEntry } from '../models';

// --- Services & Data ---
import { ALL_STANDARD_VOICES as cloudVoicesData, TTSVoice } from '../services/ttsVoices';
import { getBrowserVoices, TTSProvider, ttsPlay, TTSConfig, ttsStop } from '../services/tts';

// --- Components ---
import JournalEntryForm from '../components/JournalEntryForm';
import JournalEntryItem from '../components/JournalEntryItem';
import AudioConfigPanel from '../components/AudioConfigPanel';
import StarRating from '../components/StarRating';
import AppIcon from '../components/AppIcon';
import LexiconText from '../components/LexiconText';

// --- STYLED COMPONENTS ---
const PageContainer = styled.div` padding-bottom: calc(4rem + 60px + 1.5rem); `;
const HeaderImage = styled.div<{ imageUrl?: string }>`
  height: 25vh; min-height: 200px; max-height: 350px;
  margin: 1.5rem 1.5rem 0 1.5rem; border-radius: 20px;
  background-image: ${({ imageUrl, theme }) => imageUrl ? `url(${imageUrl})` : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`};
  background-size: cover; background-position: center;
  display: flex; align-items: center; justify-content: center; text-align: center;
  padding: 1.5rem; position: relative; color: ${({ theme }) => theme.textLight || '#fff'};
  &::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to top, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0.15) 60%, rgba(0,0,0,0) 100%); border-radius: inherit; z-index: 1; }
`;
const HeaderContent = styled.div`
  position: relative; z-index: 2; width: 100%; max-width: 800px; margin: 0 auto;
  h1 { font-size: 2.4rem; margin: 0 0 0.5rem 0; font-weight: 700; text-shadow: 0 2px 5px rgba(0,0,0,0.6); }
`;
const BackButton = styled(Link)`
  position: absolute; top: 1.5rem; left: 1.5rem; background: rgba(0,0,0,0.4);
  color: white; border: none; border-radius: 50%; width: 44px; height: 44px;
  display: flex; align-items: center; justify-content: center; cursor: pointer; z-index: 30;
  transition: background-color 0.2s; padding: 0;
  &:hover { background: rgba(0,0,0,0.6); }
`;
const SessionInfoBar = styled.div`
  display: flex; flex-wrap: wrap; gap: 0.5rem 1.2rem; align-items: center;
  justify-content: center; font-size: 0.95rem; opacity: 0.95;
  svg { margin-right: 0.4rem; vertical-align: middle; }
  span { display: flex; align-items: center; }
`;
const ContentWrapper = styled.div`
  padding: 1.5rem; max-width: 800px; margin: -2.5rem auto 0 auto;
  background-color: ${({ theme }) => theme.surface}; border-radius: 20px;
  box-shadow: 0 -5px 20px rgba(0,0,0,0.1); position: relative; z-index: 10;
`;
const Section = styled.section`
  margin-bottom: 2.5rem;
  &:last-child { margin-bottom: 0; }
  h2 { font-size: 1.6rem; color: ${({ theme }) => theme.primary}; margin-top: 0; margin-bottom: 1.2rem; padding-bottom: 0.6rem; border-bottom: 1px solid ${({ theme }) => theme.border}; display: flex; align-items: center; gap: 0.5rem; }
  p, li { color: ${({ theme }) => theme.textSecondary}; line-height: 1.75; font-size: 1rem; }
  ul { list-style: none; padding-left: 0; }
`;
const PlayButtonFloating = styled.button`
  position: fixed; bottom: calc(env(safe-area-inset-bottom, 0px) + 60px + 1.5rem);
  right: 1.5rem; background-color: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight || '#fff'};
  border: none; border-radius: 50%; width: 60px; height: 60px;
  display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(0,0,0,0.25);
  cursor: pointer; z-index: 100; transition: transform 0.2s, background-color 0.2s;
  &:hover:not(:disabled) { transform: scale(1.08); background-color: ${({ theme }) => theme.accent || theme.primary}; }
  &:disabled { cursor: not-allowed; opacity: 0.7; }
`;
const LoadingContainer = styled.div`
  display: flex; flex-direction: column; justify-content: center; align-items: center;
  padding: 3rem; font-size: 1.2rem; color: ${({ theme }) => theme.primary}; min-height: 50vh;
  .loader-icon { font-size: 2.5rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;
const ErrorMessage = styled.p` color: red; text-align: center; padding: 2rem; `;
const FormGroup = styled.div`
  margin-bottom: 1rem;
  &:last-of-type { margin-bottom: 0; }
  label { display: block; font-weight: 500; color: ${({ theme }) => theme.textSecondary}; margin-bottom: 0.5rem; font-size: 0.95rem; }
  select, input[type="range"] { width: 100%; padding: 0.7rem; border-radius: 8px; border: 1px solid ${({ theme }) => theme.border}; background: ${({ theme }) => theme.inputBackground}; color: ${({ theme }) => theme.text}; font-size: 1rem; &:disabled { opacity: 0.7; background: ${({ theme }) => theme.disabled}; } }
`;
const VoiceSelectionWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .voice-select { flex-grow: 1; }

  .test-button {
    flex-shrink: 0; padding: 0.6rem; height: 46px; width: 46px;
    border-radius: 8px; border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground}; color: ${({ theme }) => theme.primary};
    cursor: pointer; display: flex; align-items: center; justify-content: center;
    &:disabled { cursor: not-allowed; opacity: 0.5; }
    svg { width: 22px; height: 22px; }
  }
`;
const JournalEntriesListContainer = styled.div` margin-top: 2rem; display: flex; flex-direction: column; gap: 1.25rem; `;
const Subtitle = styled.h3` font-size: 1.2rem; color: ${({ theme }) => theme.textSecondary}; margin-top: 2rem; margin-bottom: 1rem; font-weight: 500; `;
const LastUpdatedText = styled.p` font-size: 0.8rem !important; font-style: italic; color: ${({ theme }) => theme.textSecondary} !important; opacity: 0.8; margin-top: 1rem; text-align: right; `;
const TagsContainer = styled.div` display: flex; flex-wrap: wrap; gap: 0.75rem; `;
const BenefitsList = styled.ul` padding-left: 1.2rem; list-style: none; li { position: relative; padding-left: 1.5rem; margin-bottom: 0.75rem; &:before { content: '✔'; position: absolute; left: 0; color: ${({ theme }) => theme.accent}; font-weight: bold; } } `;
const TagLink = styled(Link)`
  display: inline-flex; align-items: center; gap: 0.5rem;
  background-color: ${({ theme }) => theme.inputBackground}; color: ${({ theme }) => theme.primary};
  border: 1px solid ${({ theme }) => theme.border}; padding: 0.4rem 0.8rem; border-radius: 16px; font-size: 0.85rem; font-weight: 500; text-decoration: none;
  transition: all 0.2s ease-in-out;
  &:hover { background-color: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight || '#fff'}; border-color: ${({ theme }) => theme.primary}; transform: translateY(-2px); box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
`;
const LexiconIcon = styled.span`
  display: inline-flex; align-items: center; justify-content: center;
  margin-left: -0.25rem; padding: 0.2rem; border-radius: 50%; cursor: pointer; transition: background-color 0.2s;
  &:hover { background-color: rgba(0,0,0,0.15); }
`;
const RatingInfoContainer = styled.div`
  margin-bottom: 1.5rem;
  text-align: center;
`;
const AverageRatingText = styled.p`
  font-size: 1rem;
  color: ${({ theme }) => theme.textSecondary};
  margin: 0 0 0.5rem 0;

  strong { font-weight: 600; color: ${({ theme }) => theme.text}; }
`;
const RatingCountText = styled.p`
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  margin: 0;
`;

// --- Component ---
const SessionDetailPage: React.FC = () => {
    const { t, i18n } = useTranslation();
    const { lang: appLanguage } = useLang();
    const { sessionId } = useParams<{ sessionId: string }>();
    const navigate = useNavigate();
    const { user } = useAuth();
    
    const { subscription, allScripts, userMusicAssets, userAmbientAssets, activity, preferences, privateData, rateSession, updatePreferences, setSessionAudioConfig, addJournalEntry, showDefinitionInModal, dictionaryMap } = useAppStore(useShallow(state => ({
        subscription: state.subscription, allScripts: state.sessions.scripts,
        userMusicAssets: state.audioAssets?.musics ?? [], userAmbientAssets: state.audioAssets?.ambiants ?? [],
        activity: state.activity, preferences: state.preferences, privateData: state.privateData,
        rateSession: state.rateSession, updatePreferences: state.updatePreferences, setSessionAudioConfig: state.setSessionAudioConfig,
        addJournalEntry: state.addJournalEntry, showDefinitionInModal: state.showDefinitionInModal, dictionaryMap: state.definitions.dictionaryMap,
    })));

    const [browserVoices, setBrowserVoices] = useState<TTSVoice[]>([]);
    const [isFetchingVoices, setIsFetchingVoices] = useState(true);
    const [isTesting, setIsTesting] = useState(false);
    const testAudioRef = useRef<HTMLAudioElement | null>(null);
    
    const session = useMemo(() => {
        if (!sessionId || !allScripts) return null;
        for (const category in allScripts) {
            const found = allScripts[category].find(s => s.id === sessionId);
            if (found) return { ...found, category };
        }
        return null;
    }, [sessionId, allScripts]);

    const benefitsArray = useMemo(() => {
        const benefitsData = session?.benefits as unknown;
        if (!benefitsData) return [];
        if (Array.isArray(benefitsData)) return benefitsData;
        if (typeof benefitsData === 'string') return benefitsData.split(',').map((b: string) => b.trim());
        return [];
    }, [session?.benefits]);
    
    const lexiconKeyByTagName = useMemo(() => {
        const map = new Map<string, string>();
        if (dictionaryMap.size === 0) return map;
        const currentLang = i18n.language as 'en' | 'fr' | 'es';
        for (const [key, element] of dictionaryMap.entries()) {
            if (element.name && typeof element.name === 'object' && currentLang in element.name) {
                const tagName = element.name[currentLang];
                if(tagName) map.set(tagName.toLowerCase(), key);
            }
            map.set(element.key.toLowerCase(), key);
        }
        return map;
    }, [dictionaryMap, i18n.language]);

    const groupedCloudVoices = useMemo(() => {
        const groups: Record<string, TTSVoice[]> = {};
        cloudVoicesData.filter(v => v.lang.startsWith(appLanguage)).forEach(voice => {
            const regionCode = voice.lang;
            if (!groups[regionCode]) groups[regionCode] = [];
            groups[regionCode].push(voice);
        });
        return Object.entries(groups).sort(([keyA], [keyB]) => keyA.localeCompare(keyB));
    }, [appLanguage]);

    useEffect(() => {
        const fetchBrowserVoices = async () => {
            setIsFetchingVoices(true);
            try {
                const result = await getBrowserVoices();
                const formattedVoices: TTSVoice[] = result.map((v): TTSVoice => ({
                    id: v.name,
                    label: `${v.name.split('(')[0].trim()} (${v.lang})`,
                    lang: v.lang,
                    tier: 'standard', // All browser voices are effectively standard tier in this context
                    gender: 'female', // Placeholder, as this info is not reliably available
                    previewUrl: '' // Not applicable for browser voices
                }));
                setBrowserVoices(formattedVoices);
            } catch (error) {
                console.error("Error fetching browser voices:", error);
            } finally {
                setIsFetchingVoices(false);
            }
        };
        fetchBrowserVoices();
    }, []);

    useEffect(() => {
        if (!preferences || isFetchingVoices || !user?.uid) return;

        const isPremium = subscription?.premiumFeatures.cloudTTS;
        let provider = preferences.ttsConfig.provider;
        let voiceId = preferences.ttsConfig.voice;

        // If not premium and current provider is cloud, force to browser
        if (!isPremium && provider === 'cloud') {
            provider = 'browser';
            updatePreferences(user.uid, { ttsConfig: { ...preferences.ttsConfig, provider: 'browser' } });
            // The voice will be validated in the next step based on the new provider
        }
        
        const voiceList = provider === 'browser' ? browserVoices.filter(v => v.lang.startsWith(appLanguage)) : cloudVoicesData.filter(v => v.lang.startsWith(appLanguage));
        const currentVoiceIsValid = voiceList.some(v => v.id === voiceId);

        if (!currentVoiceIsValid && voiceList.length > 0) {
            updatePreferences(user.uid, { ttsConfig: { ...preferences.ttsConfig, voice: voiceList[0].id, provider } });
        } else if (!currentVoiceIsValid && voiceList.length === 0) {
            // Handle case where no voices are available for the selected language/provider
            // For now, do nothing, but consider setting a default or showing a message
            console.warn(`No TTS voices found for language ${appLanguage} with provider ${provider}.`);
        }
    }, [preferences, browserVoices, isFetchingVoices, appLanguage, user?.uid, subscription?.premiumFeatures.cloudTTS]);

    const handleTtsConfigChange = (updates: Partial<UserPreferences['ttsConfig']>) => {
        if (user?.uid && preferences) {
            updatePreferences(user.uid, { ttsConfig: { ...preferences.ttsConfig, ...updates } });
        }
    };

    const handleTestSelectedVoice = async () => {
        if (!preferences) return;

        if (isTesting) {
            ttsStop();
            if (testAudioRef.current) {
                testAudioRef.current.pause();
                testAudioRef.current = null;
            }
            setIsTesting(false);
            return;
        }

        const { provider, voice: voiceId } = preferences.ttsConfig;
        const voiceList = provider === 'browser' ? browserVoices : cloudVoicesData;
        const voiceToTest = voiceList.find(v => v.id === voiceId);
        
        if (!voiceToTest) return;

        setIsTesting(true);

        if (provider === 'cloud') {
            // Dynamically construct the public URL for the MP3 preview.
            // This relies on the bucket name and the file naming convention from your `manageVoices.js` script.
            const bucketName = 'piknowkyo-777.firebasestorage.app'; // Your Firebase storage bucket
            const previewUrl = `https://storage.googleapis.com/${bucketName}/tts_previews/${voiceToTest.id}.mp3`;
            
            if (testAudioRef.current) {
                testAudioRef.current.pause();
            }

            const audio = new Audio(previewUrl);
            testAudioRef.current = audio;

            const onEnd = () => setIsTesting(false);
            
            audio.play().catch(err => {
                console.error("Cloud TTS Preview Error:", err);
                alert(t('audioConfig.testVoiceError', 'The test could not be completed.'));
                setIsTesting(false);
            });
            audio.onended = onEnd;
            audio.onpause = onEnd; // Also stop loading state if paused by user
            audio.onerror = () => {
                console.error(`Failed to load audio preview from: ${previewUrl}`);
                alert(t('audioConfig.testVoiceError', 'The test could not be completed.'));
                setIsTesting(false);
            };

        } else { // Browser TTS
            const testText = t('audioConfig.testVoiceText', 'This is a test of the selected voice.');
            const ttsConfig: TTSConfig = { volume: preferences.ttsConfig.volume };
            try {
                await ttsPlay(provider, testText, voiceToTest.id, voiceToTest.lang, ttsConfig);
            } catch (error) {
                console.error("TTS test failed:", error);
                alert(t('audioConfig.testVoiceError', 'The test could not be completed.'));
            } finally {
                setIsTesting(false);
            }
        }
    };
    
    useEffect(() => { return () => { ttsStop(); if (testAudioRef.current) testAudioRef.current.pause(); }; }, []);

    const handleSetRating = (newRating: number) => { if (!user?.uid || !session || !appLanguage) return; rateSession(user.uid, appLanguage, session.category, session.id, newRating); };
    const handleSaveJournalEntry = async (note: string) => { if (!user || !sessionId) return; await addJournalEntry(user.uid, { sessionId, note, mood: 'neutral' }); };

    const userRating = activity?.ratedSessions?.[sessionId!] ?? 0;
    const isLoading = !session || !preferences || isFetchingVoices;

    const handlePlaySession = () => {
        if (!session || !preferences) return;
        setSessionAudioConfig({ enableMusic: preferences.musicConfig.enabled, music: { url: preferences.musicConfig.url, volume: preferences.musicConfig.volume }, enableAmbient: preferences.ambientConfig.enabled, ambient: { url: preferences.ambientConfig.url, volume: preferences.ambientConfig.volume }, enableBinaural: preferences.binauralConfig.enabled, binaural: { baseFreq: preferences.binauralConfig.baseFrequency, beatFreq: preferences.binauralConfig.beatFrequency, volume: preferences.binauralConfig.volume }, voice: { volume: preferences.ttsConfig.volume } });
        navigate(`/player/${session.id}`);
    };
    
    const sessionJournalEntries = useMemo(() => {
        if (!privateData || !sessionId) return [];
        return privateData
            .filter(doc => doc?.data?.type === 'journal' && doc.data.sessionId === sessionId)
            .map(doc => ({ id: doc.id, sessionId: doc.data.sessionId, note: doc.data.note, date: doc.data.date, mood: doc.data.mood } as JournalEntry))
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    }, [privateData, sessionId]);

    if (isLoading) return <LoadingContainer><AppIcon name="loader" className="loader-icon" />{t('loading.sessions')}</LoadingContainer>;
    if (!session || !preferences) return <ErrorMessage>{t('errors.sessionNotFound', 'Session not found.')}</ErrorMessage>;
    
    const isPremium = subscription?.premiumFeatures.cloudTTS;
    const currentProvider = preferences.ttsConfig.provider;

    return (
        <PageContainer>
            <HeaderImage imageUrl={session.imageUrl}>
                <BackButton to="/sessions" title={t('actions.backToSessions')}><AppIcon name="chevron-left" size={36} /></BackButton>
                <HeaderContent><h1>{session.title}</h1><SessionInfoBar><span><AppIcon name="clock" /> {session.duration} {t('units.min')}</span><span><AppIcon name="heart" /> {t(`sessionTypes.${session.type}`, session.type)}</span></SessionInfoBar></HeaderContent>
            </HeaderImage>
            <ContentWrapper>
                <Section>
                    <h2><AppIcon name="info" /> {t('sessionDetails.description')}</h2>
                    <p><LexiconText text={session.description} /></p>
                    {session.updatedAt && (<LastUpdatedText>{t('sessionDetails.lastUpdated', { date: new Date(session.updatedAt as any).toLocaleDateString(appLanguage, { year: 'numeric', month: 'long', day: 'numeric' }) })}</LastUpdatedText>)}
                </Section>
                
                {benefitsArray.length > 0 && (
                    <Section>
                        <h2><AppIcon name="award" /> {t('sessionDetails.benefitsTitle')}</h2>
                        <BenefitsList>{benefitsArray.map((benefit, index) => (<li key={index}><LexiconText text={benefit} /></li>))}</BenefitsList>
                    </Section>
                )}

                {session.tags && session.tags.length > 0 && (
                    <Section>
                        <h2><AppIcon name="tag" /> {t('sessionDetails.tagsTitle')}</h2>
                        <TagsContainer>
                            {session.tags.map((tag: string) => {
                                const lexiconKey = lexiconKeyByTagName.get(tag.toLowerCase());
                                return (
                                    <TagLink key={tag} to={`/sessions?tag=${encodeURIComponent(tag)}`} title={t('sessions.filter.filteringByTag') + ` ${tag}`}>
                                        {tag}
                                        {lexiconKey && (
                                            <LexiconIcon
                                                onClick={(e) => { e.preventDefault(); e.stopPropagation(); showDefinitionInModal(lexiconKey); }}
                                                title={t('sessionDetails.viewInLexicon', { term: tag })}
                                            >
                                                <AppIcon name="book-open" size={14} />
                                            </LexiconIcon>
                                        )}
                                    </TagLink>
                                );
                            })}
                        </TagsContainer>
                    </Section>
                )}

                <Section>
                    <h2><AppIcon name="volume-2" /> {t('sessionDetails.voiceConfigTitle', 'Voice Configuration')}</h2>
                    <FormGroup>
                        <label htmlFor="tts-provider">{t('settings.ttsProvider', 'TTS Provider:')}</label>
                        <select id="tts-provider" value={currentProvider} onChange={e => handleTtsConfigChange({ provider: e.target.value as TTSProvider })}>
                            <option value="browser">{t('settings.ttsProviderBrowser')}</option>
                            <option value="cloud" disabled={!isPremium}>{t('settings.ttsProviderCloud')} {!isPremium ? `(${t('settings.premiumOnly')})` : ''}</option>
                        </select>
                    </FormGroup>
                    
                    <FormGroup>
                        <label htmlFor="tts-voice">{t('settings.voice', 'Voice:')}</label>
                        <VoiceSelectionWrapper>
                            <select
                                id="tts-voice"
                                className="voice-select"
                                value={preferences.ttsConfig.voice}
                                onChange={e => handleTtsConfigChange({ voice: e.target.value })}
                                disabled={isFetchingVoices}
                            >
                                {isFetchingVoices ? <option>{t('loading.voices')}</option> :
                                  (currentProvider === 'browser' ? browserVoices.filter(v => v.lang.startsWith(appLanguage)).map(v => (
                                      <option key={v.id} value={v.id}>{v.label}</option>
                                  )) :
                                  groupedCloudVoices.map(([group, voices]) => (
                                      <optgroup key={group} label={t(`voiceSelector.groupTitle.${group}`, group)}>
                                          {voices.map(v => (
                                              <option key={v.id} value={v.id}>{v.label}</option>
                                          ))}
                                      </optgroup>
                                  )))
                                }
                            </select>
                            <button
                                className="test-button"
                                onClick={handleTestSelectedVoice}
                                disabled={isFetchingVoices || !preferences.ttsConfig.voice}
                                title={isTesting ? t('actions.stopTest') : t('actions.testVoice')}
                            >
                                {isTesting ? <AppIcon name="stop-circle" /> : <AppIcon name="volume-2" />}
                            </button>
                        </VoiceSelectionWrapper>
                    </FormGroup>

                    <FormGroup>
                        <label htmlFor="voice-volume">{t('audioConfig.volume')} : {Math.round(preferences.ttsConfig.volume * 100)}%</label>
                        <input id="voice-volume" type="range" min={0} max={1} step={0.01} value={preferences.ttsConfig.volume} onChange={e => handleTtsConfigChange({ volume: Number(e.target.value) })} />
                    </FormGroup>
                </Section>
                <Section>
                    <h2><AppIcon name="settings" /> {t('sessionDetails.audioConfigGlobal')}</h2>
                    <AudioConfigPanel userMusicAssets={userMusicAssets} userAmbientAssets={userAmbientAssets} subscription={subscription} />
                </Section>
                <Section>
                    <h2><AppIcon name="star" /> {t('sessionDetails.yourRating', "Your Rating")}</h2>
                    {user ? (
                        <>
                            <RatingInfoContainer>
                                {session.ratingCount && session.ratingCount > 0 ? (
                                    <>
                                        <AverageRatingText>
                                            {t('sessionDetails.averageRating', 'Average Rating')}: <strong>{(session.rating || 0).toFixed(1)} / 5</strong>
                                        </AverageRatingText>
                                        <RatingCountText>
                                            ({session.ratingCount} {t('units.ratings', { count: session.ratingCount })})
                                        </RatingCountText>
                                    </>
                                ) : (
                                    <AverageRatingText>
                                        {t('sessionDetails.noRatingsYet', 'No ratings yet. Be the first to rate it!')}
                                    </AverageRatingText>
                                )}
                            </RatingInfoContainer>
                            <StarRating rating={userRating} setRating={handleSetRating} size={32} />
                        </>
                    ) : (
                        <p>{t('rating.loginToRate', 'Log in to rate this session.')}</p>
                    )}
                </Section>
                <Section id="journal">
                    <h2><AppIcon name="edit-3" /> {t('sessionDetails.yourNotes')}</h2>
                    {user ? (<> <JournalEntryForm sessionId={sessionId!} onSave={handleSaveJournalEntry} /> {sessionJournalEntries.length > 0 && (<JournalEntriesListContainer><Subtitle>{t('journal.previousEntriesTitle', 'Previous Entries')}</Subtitle>{sessionJournalEntries.map(entry => (<JournalEntryItem key={entry.id} entry={entry} />))}</JournalEntriesListContainer>)} </>) : (<p>{t('journal.loginToUse', 'Log in to use the journal feature.')}</p>)}
                </Section>
            </ContentWrapper>
            <PlayButtonFloating onClick={handlePlaySession} title={t('actions.startSession')}><AppIcon name="play-circle" size={30} /></PlayButtonFloating>
        </PageContainer>
    );
};

export default SessionDetailPage;