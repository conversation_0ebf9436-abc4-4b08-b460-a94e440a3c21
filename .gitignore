# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# testing
/coverage

# production
/build
.firebase

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
# Environment variables
.env
.env.*
.env.development
.env.test
.env.production

npm-debug.log*
yarn-debug.log*
yarn-error.log*
Piknowkyo/dist/
Piknowkyo/public/dist/
Piknowkyo/dev-dist/
node_modules
Piknowkyo/node_modules/

# Firebase
serviceAccountKey.json
Piknowkyo/.env
Piknowkyo/.env
Piknowkyo/.env
