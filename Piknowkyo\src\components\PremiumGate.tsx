// src/components/PremiumGate.tsx

import React from 'react';
import styled, { css } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiLock, FiPlay, FiStar } from 'react-icons/fi';
import { useAds } from '../hooks/useAds';
import Button from './ui/Button';
import { SubscriptionState } from '../store/useAppStore';

// --- Styled Components (no changes here) ---

const GateContainer = styled.div<{ $viewMode?: 'grid' | 'list' }>`
  position: relative;
  width: 100%;
  /* height: 100%; // Removed to allow container to size to its content */
`;

const ListViewButton = styled(Button)`
  min-width: 100px;
`;

const LockedOverlay = styled.div<{ $viewMode?: 'grid' | 'list' }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.name === 'dark'
    ? 'rgba(0, 0, 0, 0.85)'
    : 'rgba(255, 255, 255, 0.9)'};
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  padding: 1.5rem;
  text-align: center;
  border-radius: 10px;
  
  ${({ $viewMode }) => $viewMode === 'list' && css`
    padding: 0 1rem;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    gap: 1rem;
    
    .lock-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        overflow: hidden;
    }
    
    ${Title} {
      flex-grow: 1;
      margin: 0;
      font-size: 1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    ${UnlockOptions} {
      flex-direction: row;
      gap: 0.5rem;
      width: auto;
      flex-shrink: 0;
    }
  `}
`;

const LockIcon = styled.div<{ $position?: 'top-left' | 'center' }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: white;
  
  ${({ $position }) => $position === 'top-left' && css`
    position: absolute;
    top: 12px;
    left: 12px;
    width: 32px;
    height: 32px;
    margin: 0;
  `}
`;

const Title = styled.h3<{ $viewMode?: 'grid' | 'list'; $context?: string }>`
  color: ${({ theme }) => theme.name === 'dark' ? 'white' : theme.text};
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  
  ${({ $viewMode }) => $viewMode === 'list' && css`
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
  `}

  ${({ $context, $viewMode }) => ($context === 'session' || $context === 'game') && $viewMode === 'grid' && css`
    padding: 0 2rem;
    margin-top: 1.5rem;
  `}
`;

const Description = styled.p<{ $context?: 'session' | 'feature' | 'game' | 'audio-asset' }>`
  color: ${({ theme }) => theme.name === 'dark'
    ? 'rgba(255, 255, 255, 0.8)'
    : 'rgba(0, 0, 0, 0.7)'};
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 100%;
  
  ${({ $context }) => !$context && css`
    font-style: italic;
    opacity: 0.9;
  `}
`;

const UnlockOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  align-items: center;

  & > button {
    padding-top: 0.6rem;
    padding-bottom: 0.6rem;
    font-size: 0.875rem;
    max-width: 220px;
  }
`;

const ContentWrapper = styled.div<{ $blurred: boolean }>`
  filter: ${({ $blurred }) => $blurred ? 'blur(2px)' : 'none'};
  pointer-events: ${({ $blurred }) => $blurred ? 'none' : 'auto'};
  transition: filter 0.3s ease;
  width: 100%;
  /* height: 100%; // Removed to allow wrapper to size to its content */
`;


export interface PremiumGateProps {
  feature: string;
  children: React.ReactNode;
  subscription: SubscriptionState | null;
  title?: string;
  description?: string;
  className?: string;
  gameId?: string;
  sessionId?: string;
  viewMode?: 'grid' | 'list';
  context?: 'session' | 'feature' | 'game' | 'audio-asset';
  lockPosition?: 'top-left' | 'center';
  showAdOption?: boolean;
}

const PremiumGate: React.FC<PremiumGateProps> = ({
  feature,
  children,
  subscription,
  title,
  description,
  className,
  gameId,
  sessionId,
  viewMode = 'grid',
  context = 'feature',
  lockPosition = 'center',
  showAdOption = true,
}) => {
  const { t } = useTranslation();
  const { isLoading: isLoadingAd, showAdToUnlockItem } = useAds();

  if (!subscription) {
    return null;
  }
  
  const isPremiumUser = subscription.isActive || subscription.isTrialActive;
  const hasSubscriptionAccess = isPremiumUser && subscription.premiumFeatures[feature as keyof typeof subscription.premiumFeatures];

  const itemId = sessionId || gameId || feature;

  const unlockedItemExpiry = subscription.adUnlocks?.unlockedItems?.[itemId];
  const isUnlockedByAd = !!unlockedItemExpiry && new Date(unlockedItemExpiry) > new Date();
  
  if (feature === '' || hasSubscriptionAccess || isUnlockedByAd) {
    return <div className={className}>{children}</div>;
  }

  const adUnlockableFeatures = ['advancedSessions', 'games'];
  const canShowAdOption = adUnlockableFeatures.includes(feature) && showAdOption;
  
  const getFeatureTitle = (): string => {
    if ((context === 'session' || context === 'game') && title) return title;
    return title || t(`premium.features.${feature}.title`, 'Fonctionnalité Premium');
  };

  const getFeatureDescription = (): string => {
      return description || t(`premium.features.${feature}.description`, 'Passez à Premium pour débloquer cette fonctionnalité et bien plus encore.');
  }

  const handleWatchAd = async () => {
    // Temporary: Show a "Coming Soon" message until ads are implemented.
    alert(t('premium.ads.comingSoon', 'Cette fonctionnalité sera bientôt disponible ! Merci de votre patience.'));

    // TODO: Re-enable ad functionality when ready.
    // The logic below is preserved for when the ad service is fully implemented.
    /*
    if (!itemId) {
      console.error("Ad unlock failed: No identifier (sessionId, gameId, or feature) was provided.");
      return;
    }
    await showAdToUnlockItem(itemId);
    */
  };

  const handleSubscribe = () => {
    window.location.href = '/monetization?action=subscribe';
  };

  const renderGridContent = () => (
    <>
      <LockIcon $position={(context === 'session' || context === 'game') ? 'top-left' : 'center'}>
        <FiLock size={(context === 'session' || context === 'game') ? 18 : 24} />
      </LockIcon>
      <Title $viewMode="grid" $context={context}>{getFeatureTitle()}</Title>
      <Description $context={context}>{getFeatureDescription()}</Description>
      <UnlockOptions>
        <Button variant="primary" onClick={handleSubscribe} fullWidth>
          <FiStar size={16} />
          {t('premium.actions.subscribe', 'S\'abonner à Premium')}
        </Button>
        {canShowAdOption && (
          <Button variant="secondary" onClick={handleWatchAd} disabled={isLoadingAd} fullWidth>
            <FiPlay size={16} />
            {isLoadingAd ? t('premium.actions.loadingAd', 'Chargement de la pub...') : t('premium.actions.watchAd', 'Voir pub pour débloquer')}
          </Button>
        )}
      </UnlockOptions>
    </>
  );

  const renderListContent = () => (
    <>
      <div className="lock-info">
        <LockIcon>
          <FiLock size={16} />
        </LockIcon>
        <Title $viewMode="list">{getFeatureTitle()}</Title>
      </div>
      <UnlockOptions>
        <ListViewButton variant="primary" onClick={handleSubscribe} size="small">
          <FiStar size={12} />
          {t('premium.actions.subscribeShort', 'S\'abonner')}
        </ListViewButton>
        {canShowAdOption && (
          <ListViewButton variant="secondary" onClick={handleWatchAd} disabled={isLoadingAd} size="small">
            <FiPlay size={12} />
            {isLoadingAd ? t('common.loading', 'Chargement...') : t('premium.actions.watchShort', 'Voir Pub')}
          </ListViewButton>
        )}
      </UnlockOptions>
    </>
  );

  return (
    <GateContainer className={className} $viewMode={viewMode}>
      <ContentWrapper $blurred={true}>{children}</ContentWrapper>
      <LockedOverlay $viewMode={viewMode}>
        {viewMode === 'list' ? renderListContent() : renderGridContent()}
      </LockedOverlay>
    </GateContainer>
  );
};

export default PremiumGate;