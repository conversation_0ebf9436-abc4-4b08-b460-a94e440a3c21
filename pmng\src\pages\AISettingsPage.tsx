// pmng/src/pages/AISettingsPage.tsx

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../firebase';

// --- Styled Components ---
const PageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
`;

const ProviderCard = styled.div`
  background-color: ${({ theme }) => theme.surface};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const CardHeader = styled.h3`
  margin: 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
`;

const FormControl = styled.div`
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  input, select {
    width: 100%;
    padding: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 4px;
    box-sizing: border-box;
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

const RadioLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
`;

const SaveButton = styled.button`
  padding: 12px 24px;
  border: none;
  background-color: ${({ theme }) => theme.primary};
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  align-self: flex-start;
  margin-top: 1rem;
`;

// --- Interfaces and Data ---
interface ProviderSetting {
  apiKey: string;
  selectedModel: string;
}
type Settings = Record<string, ProviderSetting>;
type FetchedModels = Record<string, { id: string }[]>;

// MODIFIED: Added canFetchModels property
const PROVIDERS = {
  groq: { name: 'Groq', envKey: 'VITE_GROQ_API_KEY', apiBase: 'https://api.groq.com/openai/v1', canFetchModels: true },
  mistral: { name: 'Mistral', envKey: 'VITE_MISTRAL_API_KEY', apiBase: 'https://api.mistral.ai/v1', canFetchModels: true },
  chutesai: { name: 'Chutes.ai', envKey: 'VITE_CHUTESAI_API_KEY', apiBase: 'https://api.chutes.ai/v1', canFetchModels: false },
};

const AISettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<Settings>({});
  const [fetchedModels, setFetchedModels] = useState<FetchedModels>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState<Record<string, boolean>>({});
  // NEW: State for the default provider
  const [defaultProvider, setDefaultProvider] = useState<string>('');

  // Load saved settings and default provider from Firestore
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);
      const docRef = doc(db, 'config', 'ai_settings');
      const docSnap = await getDoc(docRef);
      const initialSettings: Settings = {};

      for (const key in PROVIDERS) {
        const provider = PROVIDERS[key as keyof typeof PROVIDERS];
        initialSettings[key] = {
          apiKey: import.meta.env[provider.envKey] || '',
          selectedModel: '',
        };
      }
      
      if (docSnap.exists()) {
        const savedData = docSnap.data();
        // Set the default provider if it exists in the saved data
        if (savedData.defaultProvider) {
          setDefaultProvider(savedData.defaultProvider);
        }
        
        // Merge provider-specific settings
        for (const key in initialSettings) {
            if (savedData[key]) {
                initialSettings[key].selectedModel = savedData[key].selectedModel || '';
                if (!import.meta.env[PROVIDERS[key as keyof typeof PROVIDERS].envKey]) {
                    initialSettings[key].apiKey = savedData[key].apiKey || '';
                }
            }
        }
      }
      setSettings(initialSettings);
      setIsLoading(false);
    };
    loadSettings();
  }, []);

  const handleFetchModels = useCallback(async (providerId: string) => {
    const provider = PROVIDERS[providerId as keyof typeof PROVIDERS];
    // MODIFIED: Check if the provider supports fetching
    if (!provider.canFetchModels) {
        alert("Models for this provider must be entered manually.");
        return;
    }

    const apiKey = settings[providerId]?.apiKey;
    if (!apiKey) {
      alert(`Please provide an API key for ${provider.name}.`);
      return;
    }
    
    setIsFetching(prev => ({...prev, [providerId]: true}));

    try {
      const response = await fetch(`${provider.apiBase}/models`, {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error?.message || 'Unknown API error');
      setFetchedModels(prev => ({ ...prev, [providerId]: data.data }));
      alert(`Successfully fetched ${data.data.length} models from ${provider.name}!`);
    } catch (error) {
      alert(`Failed to fetch models from ${provider.name}: ${error}`);
    }

    setIsFetching(prev => ({...prev, [providerId]: false}));
  }, [settings]);

  const handleSettingChange = (providerId: string, field: keyof ProviderSetting, value: string) => {
    setSettings(prev => ({
      ...prev,
      [providerId]: { ...prev[providerId], [field]: value }
    }));
  };

  // MODIFIED: Save all settings including the default provider
  const handleSaveAllSettings = async () => {
    setIsLoading(true);
    const dataToSave = { ...settings, defaultProvider };
    try {
      await setDoc(doc(db, 'config', 'ai_settings'), dataToSave, { merge: true });
      alert('AI settings saved successfully!');
    } catch (error) {
      alert(`Error saving settings: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) return <p>Loading AI Settings...</p>;

  return (
    <PageWrapper>
      <h1>AI Provider Settings</h1>
      <p>Configure your Large Language Model providers. The "default" provider will be used for automated tasks like translations.</p>
      
      {/* NEW: Default Provider Selection Card */}
      <ProviderCard>
        <CardHeader>Default AI Provider</CardHeader>
        <RadioGroup>
          {Object.entries(PROVIDERS).map(([id, provider]) => (
            <RadioLabel key={id}>
              <input
                type="radio"
                name="default-provider"
                value={id}
                checked={defaultProvider === id}
                onChange={() => setDefaultProvider(id)}
              />
              {provider.name}
            </RadioLabel>
          ))}
        </RadioGroup>
      </ProviderCard>

      <SettingsGrid>
        {Object.entries(PROVIDERS).map(([id, provider]) => {
          const envApiKey = import.meta.env[provider.envKey];
          return (
            <ProviderCard key={id}>
              <CardHeader>{provider.name}</CardHeader>
              {!envApiKey && (
                <FormControl>
                  <label htmlFor={`${id}-apikey`}>API Key</label>
                  <input
                    id={`${id}-apikey`}
                    type="password"
                    placeholder={`Paste your ${provider.name} API key here`}
                    value={settings[id]?.apiKey || ''}
                    onChange={(e) => handleSettingChange(id, 'apiKey', e.target.value)}
                  />
                </FormControl>
              )}
              {/* MODIFIED: Conditional rendering for model input */}
              {provider.canFetchModels ? (
                <>
                  <button onClick={() => handleFetchModels(id)} disabled={isFetching[id] || !settings[id]?.apiKey}>
                    {isFetching[id] ? 'Fetching...' : 'Fetch Available Models'}
                  </button>
                  <FormControl>
                    <label htmlFor={`${id}-model`}>Select Model</label>
                    <select
                      id={`${id}-model`}
                      value={settings[id]?.selectedModel || ''}
                      onChange={(e) => handleSettingChange(id, 'selectedModel', e.target.value)}
                      disabled={!fetchedModels[id] && !settings[id]?.selectedModel}
                    >
                      <option value="">-- Select a model --</option>
                      {/* Show saved model even if not fetched yet */}
                      {settings[id]?.selectedModel && !fetchedModels[id]?.some(m => m.id === settings[id].selectedModel) && (
                          <option value={settings[id].selectedModel}>{settings[id].selectedModel} (saved)</option>
                      )}
                      {fetchedModels[id]?.map(model => (
                        <option key={model.id} value={model.id}>{model.id}</option>
                      ))}
                    </select>
                  </FormControl>
                </>
              ) : (
                <FormControl>
                  <label htmlFor={`${id}-model`}>Model Name</label>
                  <input
                    id={`${id}-model`}
                    type="text"
                    placeholder="Enter model name manually"
                    value={settings[id]?.selectedModel || ''}
                    onChange={(e) => handleSettingChange(id, 'selectedModel', e.target.value)}
                  />
                </FormControl>
              )}
            </ProviderCard>
          );
        })}
      </SettingsGrid>

      <SaveButton onClick={handleSaveAllSettings} disabled={isLoading}>
        {isLoading ? 'Saving...' : 'Save All Settings'}
      </SaveButton>
    </PageWrapper>
  );
};

export default AISettingsPage;