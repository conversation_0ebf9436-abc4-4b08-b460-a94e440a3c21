// src/components/CollapsibleSessionNotes.tsx

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom'; // Import Link
import { JournalEntry } from '../models';
import AppIcon from './AppIcon';

// --- Styled Components ---

const AccordionContainer = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  margin-bottom: 1.5rem;

  &:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.08);
  }
`;

const AccordionHeader = styled.button`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 0 1.5rem; /* Remove vertical padding to let link handle it */
  border: none;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  text-align: left;
  cursor: pointer;

  /* No hover effect here, it will be on the link */
`;

// This Link component will contain the title
const SessionTitleLink = styled(Link)`
  flex-grow: 1;
  padding: 1.2rem 0;
  text-decoration: none;
  transition: background-color 0.2s ease;
  
  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    font-weight: 600;
  }
  
  &:hover {
    h2 {
      text-decoration: underline;
    }
  }
`;

const HeaderInfo = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  padding: 1.2rem 0 1.2rem 1.5rem; /* Add padding here to make it a clickable area */

  .app-icon {
    margin-left: 0.75rem;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
  }

  &.open .app-icon {
    transform: rotate(90deg);
  }
`;

const AccordionContent = styled.div<{ $isOpen: boolean }>`
  max-height: ${({ $isOpen }) => ($isOpen ? '1000px' : '0')};
  overflow: hidden;
  transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
  padding: ${({ $isOpen }) => ($isOpen ? '1.5rem' : '0 1.5rem')};
  background: ${({ theme }) => theme.surface};
`;

const NoteList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const NoteListItem = styled.li`
  background: ${({ theme }) => theme.background};
  border-radius: 8px;
  padding: 1rem 1.2rem;
  border-left: 4px solid ${({ theme }) => theme.accent};
  
  p {
    margin: 0;
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.7;
    white-space: pre-wrap; 
  }

  span {
    display: block;
    font-size: 0.8rem;
    color: ${({ theme }) => theme.textMuted};
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
`;

// --- Component ---

interface CollapsibleSessionNotesProps {
  sessionId: string; // Add sessionId to props
  title: string;
  entries: JournalEntry[];
}

const CollapsibleSessionNotes: React.FC<CollapsibleSessionNotesProps> = ({ sessionId, title, entries }) => {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const noteCountText = entries.length > 1 
    ? t('journal.notesPlural', '{{count}} notes', { count: entries.length }) 
    : t('journal.noteSingular', '{{count}} note', { count: 1 });

  return (
    <AccordionContainer>
      <AccordionHeader onClick={() => setIsOpen(!isOpen)}>
        {/* The title is now a Link */}
        <SessionTitleLink 
          to={`/sessions/${sessionId}`} 
          onClick={(e) => e.stopPropagation()} // Prevents the accordion from toggling when clicking the link
        >
          <h2>{title}</h2>
        </SessionTitleLink>

        {/* The right side remains part of the button */}
        <HeaderInfo className={isOpen ? 'open' : ''}>
          {noteCountText}
          <AppIcon name="chevron-right" className="app-icon"/>
        </HeaderInfo>
      </AccordionHeader>
      <AccordionContent $isOpen={isOpen}>
        <NoteList>
          {entries.map((entry) => (
            <NoteListItem key={entry.id}>
              <span>
                {new Date(entry.date).toLocaleDateString(i18n.language, {
                  year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
                })}
              </span>
              <p>{entry.note}</p>
            </NoteListItem>
          ))}
        </NoteList>
      </AccordionContent>
    </AccordionContainer>
  );
};

export default CollapsibleSessionNotes;