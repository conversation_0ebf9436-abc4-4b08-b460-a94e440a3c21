// src/services/ttsVoices.ts
// This file contains the definitive list of Standard voices for the application.
// This list should reflect the files uploaded to Firebase Storage by the management script.

export interface TTSVoice {
  id: string;
  label: string;
  lang: string;
  gender: 'male' | 'female';
  tier: 'standard';
  previewUrl: string;
}

const STORAGE_BUCKET_URL = 'https://storage.googleapis.com/piknowkyo-777.appspot.com/tts_previews/';

// This list is now comprehensive, matching all 101 "Standard" tier voices
// (including Neural2, News, Polyglot) found by the script.
const RAW_VOICES = [
  // --- English (Australia) ---
  { id: 'en-AU-Neural2-A', label: 'Voix A (AUS, Neural2)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-Neural2-B', label: 'Voix B (AUS, Neural2)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  { id: 'en-AU-Neural2-C', label: 'Voix C (AUS, Neural2)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-Neural2-D', label: 'Voix D (AUS, Neural2)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  { id: 'en-AU-News-E', label: 'Voix E (AUS, News)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-News-F', label: 'Voix F (AUS, News)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-News-G', label: 'Voix G (AUS, News)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  { id: 'en-AU-Polyglot-1', label: 'Polyglot 1 (AUS)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  { id: 'en-AU-Standard-A', label: 'Lily (AUS, Standard)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-Standard-B', label: 'Noah (AUS, Standard)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  { id: 'en-AU-Standard-C', label: 'Zoe (AUS, Standard)', lang: 'en-AU', gender: 'female', tier: 'standard' },
  { id: 'en-AU-Standard-D', label: 'Liam (AUS, Standard)', lang: 'en-AU', gender: 'male', tier: 'standard' },
  // --- English (UK) ---
  { id: 'en-GB-Neural2-A', label: 'Voix A (UK, Neural2)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-Neural2-B', label: 'Voix B (UK, Neural2)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-Neural2-C', label: 'Voix C (UK, Neural2)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-Neural2-D', label: 'Voix D (UK, Neural2)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-Neural2-F', label: 'Voix F (UK, Neural2)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-News-G', label: 'Voix G (UK, News)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-News-H', label: 'Voix H (UK, News)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-News-I', label: 'Voix I (UK, News)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-News-J', label: 'Voix J (UK, News)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-News-K', label: 'Voix K (UK, News)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-News-L', label: 'Voix L (UK, News)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-News-M', label: 'Voix M (UK, News)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-Standard-A', label: 'Amelia (UK, Standard)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-Standard-B', label: 'Oliver (UK, Standard)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-Standard-C', label: 'Mia (UK, Standard)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  { id: 'en-GB-Standard-D', label: 'Harry (UK, Standard)', lang: 'en-GB', gender: 'male', tier: 'standard' },
  { id: 'en-GB-Standard-F', label: 'Isla (UK, Standard)', lang: 'en-GB', gender: 'female', tier: 'standard' },
  // --- English (India) ---
  { id: 'en-IN-Neural2-A', label: 'Voix A (IN, Neural2)', lang: 'en-IN', gender: 'female', tier: 'standard' },
  { id: 'en-IN-Neural2-B', label: 'Voix B (IN, Neural2)', lang: 'en-IN', gender: 'male', tier: 'standard' },
  { id: 'en-IN-Neural2-C', label: 'Voix C (IN, Neural2)', lang: 'en-IN', gender: 'male', tier: 'standard' },
  { id: 'en-IN-Neural2-D', label: 'Voix D (IN, Neural2)', lang: 'en-IN', gender: 'female', tier: 'standard' },
  { id: 'en-IN-Standard-A', label: 'Priya (IN, Standard)', lang: 'en-IN', gender: 'female', tier: 'standard' },
  { id: 'en-IN-Standard-B', label: 'Rohan (IN, Standard)', lang: 'en-IN', gender: 'male', tier: 'standard' },
  { id: 'en-IN-Standard-C', label: 'Aditi (IN, Standard)', lang: 'en-IN', gender: 'male', tier: 'standard' },
  { id: 'en-IN-Standard-D', label: 'Anjali (IN, Standard)', lang: 'en-IN', gender: 'female', tier: 'standard' },
  // --- English (US) ---
  { id: 'en-US-Casual-K', label: 'Voix K (USA, Casual)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Neural2-A', label: 'Voix A (USA, Neural2)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Neural2-C', label: 'Voix C (USA, Neural2)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Neural2-D', label: 'Voix D (USA, Neural2)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Neural2-E', label: 'Voix E (USA, Neural2)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Neural2-F', label: 'Voix F (USA, Neural2)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Neural2-G', label: 'Voix G (USA, Neural2)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Neural2-H', label: 'Voix H (USA, Neural2)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Neural2-I', label: 'Voix I (USA, Neural2)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Neural2-J', label: 'Voix J (USA, Neural2)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-News-K', label: 'Voix K (USA, News)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-News-L', label: 'Voix L (USA, News)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-News-N', label: 'Voix N (USA, News)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Polyglot-1', label: 'Polyglot 1 (USA)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Standard-A', label: 'David (USA, Standard)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Standard-B', label: 'John (USA, Standard)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Standard-C', label: 'Ava (USA, Standard)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Standard-D', label: 'Matthew (USA, Standard)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Standard-E', label: 'Emily (USA, Standard)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Standard-F', label: 'Jessica (USA, Standard)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Standard-G', label: 'Linda (USA, Standard)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Standard-H', label: 'Sarah (USA, Standard)', lang: 'en-US', gender: 'female', tier: 'standard' },
  { id: 'en-US-Standard-I', label: 'Robert (USA, Standard)', lang: 'en-US', gender: 'male', tier: 'standard' },
  { id: 'en-US-Standard-J', label: 'Michael (USA, Standard)', lang: 'en-US', gender: 'male', tier: 'standard' },
  // --- Spanish (Spain) ---
  { id: 'es-ES-Neural2-A', label: 'Voix A (ESP, Neural2)', lang: 'es-ES', gender: 'female', tier: 'standard' },
  { id: 'es-ES-Neural2-F', label: 'Voix F (ESP, Neural2)', lang: 'es-ES', gender: 'male', tier: 'standard' },
  { id: 'es-ES-Polyglot-1', label: 'Polyglot 1 (ESP)', lang: 'es-ES', gender: 'male', tier: 'standard' },
  { id: 'es-ES-Standard-F', label: 'Elena (ESP, Standard)', lang: 'es-ES', gender: 'female', tier: 'standard' },
  { id: 'es-ES-Standard-G', label: 'Mateo (ESP, Standard)', lang: 'es-ES', gender: 'male', tier: 'standard' },
  // --- Spanish (US) ---
  { id: 'es-US-Neural2-A', label: 'Voix A (USA, Neural2)', lang: 'es-US', gender: 'female', tier: 'standard' },
  { id: 'es-US-Neural2-B', label: 'Voix B (USA, Neural2)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-Neural2-C', label: 'Voix C (USA, Neural2)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-News-D', label: 'Voix D (USA, News)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-News-E', label: 'Voix E (USA, News)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-Polyglot-1', label: 'Polyglot 1 (USA)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-Standard-A', label: 'Isabella (USA, Standard)', lang: 'es-US', gender: 'female', tier: 'standard' },
  { id: 'es-US-Standard-B', label: 'Gabriel (USA, Standard)', lang: 'es-US', gender: 'male', tier: 'standard' },
  { id: 'es-US-Standard-C', label: 'Sofia (USA, Standard)', lang: 'es-US', gender: 'male', tier: 'standard' },
  // --- French (Canada) ---
  { id: 'fr-CA-Neural2-A', label: 'Voix A (CAN, Neural2)', lang: 'fr-CA', gender: 'female', tier: 'standard' },
  { id: 'fr-CA-Neural2-B', label: 'Voix B (CAN, Neural2)', lang: 'fr-CA', gender: 'male', tier: 'standard' },
  { id: 'fr-CA-Neural2-C', label: 'Voix C (CAN, Neural2)', lang: 'fr-CA', gender: 'female', tier: 'standard' },
  { id: 'fr-CA-Neural2-D', label: 'Voix D (CAN, Neural2)', lang: 'fr-CA', gender: 'male', tier: 'standard' },
  { id: 'fr-CA-Standard-A', label: 'Charlotte (CAN, Standard)', lang: 'fr-CA', gender: 'female', tier: 'standard' },
  { id: 'fr-CA-Standard-B', label: 'Félix (CAN, Standard)', lang: 'fr-CA', gender: 'male', tier: 'standard' },
  { id: 'fr-CA-Standard-C', label: 'Alice (CAN, Standard)', lang: 'fr-CA', gender: 'female', tier: 'standard' },
  { id: 'fr-CA-Standard-D', 'label': 'Louis (CAN, Standard)', lang: 'fr-CA', gender: 'male', tier: 'standard' },
  // --- French (France) ---
  { id: 'fr-FR-Neural2-F', label: 'Voix F (FRA, Neural2)', lang: 'fr-FR', gender: 'female', tier: 'standard' },
  { id: 'fr-FR-Neural2-G', label: 'Voix G (FRA, Neural2)', lang: 'fr-FR', gender: 'male', tier: 'standard' },
  { id: 'fr-FR-Polyglot-1', label: 'Polyglot 1 (FRA)', lang: 'fr-FR', gender: 'male', tier: 'standard' },
  { id: 'fr-FR-Standard-F', label: 'Clara (FRA, Standard)', lang: 'fr-FR', gender: 'female', tier: 'standard' },
  { id: 'fr-FR-Standard-G', label: 'Thomas (FRA, Standard)', lang: 'fr-FR', gender: 'male', tier: 'standard' },
] as const;


/**
 * The definitive list of Standard voices for the application.
 * It's created by mapping the raw, strictly-typed data.
 */
export const ALL_STANDARD_VOICES: TTSVoice[] = [...RAW_VOICES].map(voice => ({
    ...voice,
    previewUrl: `${STORAGE_BUCKET_URL}${voice.id}.mp3`
})).sort((a, b) => {
    // Sort by language, then by name for a clean display
    if (a.lang < b.lang) return -1;
    if (a.lang > b.lang) return 1;
    return a.label.localeCompare(b.label);
});

let sessionCache: TTSVoice[] | null = null;

/**
 * Returns a hard-coded list of available Google Cloud Standard TTS voices.
 * This function is async to maintain a consistent interface across the app.
 */
export async function fetchGoogleCloudVoices(): Promise<TTSVoice[]> {
  if (sessionCache) {
    return sessionCache;
  }
  
  console.log(`TTS Voices: Serving from definitive list of ${ALL_STANDARD_VOICES.length} Standard voices.`);
  sessionCache = ALL_STANDARD_VOICES;
  return Promise.resolve(sessionCache);
}