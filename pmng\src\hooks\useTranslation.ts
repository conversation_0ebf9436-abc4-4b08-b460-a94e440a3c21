// src/hooks/useTranslation.ts

import React, { useState, useEffect, useContext, createContext } from 'react';
import type { ReactNode } from 'react';
import {
  collection,
  doc,
  getDocs,
  setDoc,
  deleteDoc,
} from 'firebase/firestore';
import { db } from '../firebase';

// Define the translation structure
export interface Translation {
  key: string;
  values: { [lang: string]: string };
}

// Define the context type
interface TranslationContextType {
  translations: Translation[];
  loading: boolean;
  error: Error | null;
  t: (key: string) => string;
  addTranslation: (translation: Translation) => Promise<void>;
  updateTranslation: (key: string, values: { [lang: string]: string }) => Promise<void>;
  deleteTranslation: (key: string) => Promise<void>;
}

// Create the context
const TranslationContext = createContext<TranslationContextType | null>(null);

/**
 * A helper function to flatten nested translation objects.
 * Turns { a: { b: 'c' } } into { 'a.b': 'c' }.
 * @param obj The object to flatten.
 * @param prefix Internal use for recursion.
 * @returns A flattened object with dot-notation keys.
 */
const flattenObject = (obj: any, prefix = ''): { [key: string]: string } => {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? prefix + '.' : '';
    if (k === 'updatedAt') return acc; // Explicitly skip updatedAt field
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k], pre + k));
    } else {
      acc[pre + k] = obj[k];
    }
    return acc;
  }, {} as { [key: string]: string });
};


// Translation Provider component
export const TranslationProvider = ({ children }: { children: ReactNode }) => {
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // MODIFIED: Fetch translations from 'AppLanguage' and restructure the data
  const fetchTranslations = async () => {
    try {
      setLoading(true);
      // Fetch from the correct collection used by the user-facing app
      const querySnapshot = await getDocs(collection(db, 'AppLanguage'));
      
      const restructuredTranslations: { [key: string]: Translation } = {};

      // CORRECTED: Replaced .forEach with a for...of loop for better type inference.
      for (const doc of querySnapshot.docs) {
        const lang = doc.id;
        const rawData = doc.data();
        
        // Flatten the data for the current language to handle nested objects
        const flattenedData = flattenObject(rawData);

        // Restructure the data from per-language to per-key
        for (const flatKey in flattenedData) {
          if (!restructuredTranslations[flatKey]) {
            restructuredTranslations[flatKey] = { key: flatKey, values: {} };
          }
          restructuredTranslations[flatKey].values[lang] = flattenedData[flatKey];
        }
      }

      // Convert the restructured object into an array and sort by key
      const translationsData = Object.values(restructuredTranslations).sort((a, b) => a.key.localeCompare(b.key));

      setTranslations(translationsData);
      setError(null);
    } catch (err) {
      setError(err as Error);
      console.error('Error fetching translations:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTranslations();
  }, []);

  // Translation function - no changes needed here
  const t = (key: string): string => {
    const translation = translations.find(t => t.key === key);
    if (!translation) return key; // Return key if translation not found
    
    // Default to English for the manager
    return translation.values['en'] || key;
  };

  // NOTE: Add, update, and delete operations will need to be adjusted later
  // as they currently target the wrong data structure. Let's focus on reading first.

  // Placeholder for addTranslation
  const addTranslation = async (translation: Translation) => {
    console.error('addTranslation is not implemented for the new data structure yet.');
    // The logic here needs to be rewritten to update multiple documents
    // in the 'AppLanguage' collection using dot notation.
    await Promise.resolve(); 
  };

  // Placeholder for updateTranslation
  const updateTranslation = async (key: string, values: { [lang: string]: string }) => {
    console.error('updateTranslation is not implemented for the new data structure yet.');
    // The logic here needs to be rewritten to update multiple documents
    // in the 'AppLanguage' collection using dot notation.
    await Promise.resolve();
  };

  // Placeholder for deleteTranslation
  const deleteTranslation = async (key: string) => {
    console.error('deleteTranslation is not implemented for the new data structure yet.');
    // The logic here needs to be rewritten to delete a field from multiple
    // documents in the 'AppLanguage' collection.
    await Promise.resolve();
  };

  return React.createElement(
    TranslationContext.Provider,
    {
      value: {
        translations,
        loading,
        error,
        t,
        addTranslation,
        updateTranslation,
        deleteTranslation
      }
    },
    children
  );
};

// Custom hook to use the translation context - no changes needed
export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};