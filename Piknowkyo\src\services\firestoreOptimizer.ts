import { db } from '../firebase';
import {
  collection,
  query,
  where,
  writeBatch,
  initializeFirestore,
  persistentLocalCache,
  getDocsFromCache,
  getDocsFromServer,
  Firestore
} from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Enable offline persistence using the new cache object approach
async function enableIndexedDbPersistence(db: Firestore) {
  try {
    await initializeFirestore(db.app, {
      localCache: persistentLocalCache()
    });
    console.log('IndexedDB persistence enabled');
  } catch (error) {
    console.error('Error enabling IndexedDB persistence:', error);
  }
}

// Enable persistence for our Firebase app
enableIndexedDbPersistence(db);

export const firestoreOptimizer = {
  // Batch write operations
  batchWrite: async (operations: Array<{type: 'set' | 'update' | 'delete', ref: any, data?: any}>) => {
    const batch = writeBatch(db);
    
    operations.forEach(op => {
      if (op.type === 'set') {
        batch.set(op.ref, op.data!);
      } else if (op.type === 'update') {
        batch.update(op.ref, op.data!);
      } else if (op.type === 'delete') {
        batch.delete(op.ref);
      }
    });
    
    await batch.commit();
  },
  
  // Optimized query with caching
  queryWithCache: async (collectionName: string, conditions: Array<[string, string, any]>) => {
    const auth = getAuth();
    const userId = auth.currentUser?.uid;
    if (!userId) throw new Error('User not authenticated');
    
    const q = query(
      collection(db, collectionName),
      ...conditions.map(cond => where(cond[0], cond[1] as any, cond[2]))
    );
    
    try {
      // First try to get from cache
      const cacheSnapshot = await getDocsFromCache(q);
      if (!cacheSnapshot.empty) {
        return cacheSnapshot;
      }
    } catch (cacheError) {
      console.log('Cache miss, fetching from server');
    }
    
    // If not in cache, get from server
    return await getDocsFromServer(q);
  },
  
  // Optimize common queries with composite indexes
  getSubscriptionData: async (userId: string) => {
    return firestoreOptimizer.queryWithCache('subscriptions', [
      ['userId', '==', userId]
    ]);
  }
};