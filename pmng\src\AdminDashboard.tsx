// pmng/src/AdminDashboard.tsx
import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { db, auth } from './firebase';
import { collection, getDocs, doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { signOut } from 'firebase/auth';
import type { User as FirebaseUser } from 'firebase/auth';
import CryptoJS from 'crypto-js';

// Get the shared key from environment variables, essential for scripts
const SHARED_KEY = import.meta.env.VITE_SHARED_ENCRYPTION_KEY;

// --- Styled Components (Theme-aware) ---
const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
`;

const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: ${({ theme }) => theme.surface};
  border-bottom: 1px solid ${({ theme }) => theme.border};
`;

const MainContent = styled.div`
  display: flex;
  flex-grow: 1;
  overflow: hidden;
`;

const LeftPanel = styled.div`
  width: 350px;
  min-width: 350px;
  border-right: 1px solid ${({ theme }) => theme.border};
  overflow-y: auto;
  background: ${({ theme }) => theme.surface};
  padding: 10px;
`;

const ListItem = styled.div<{ $isSelected: boolean }>`
  padding: 12px 15px;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 5px;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: ${({ $isSelected, theme }) => ($isSelected ? theme.primary : 'transparent')};
  color: ${({ $isSelected, theme }) => ($isSelected ? theme.textLight : theme.text)};
  &:hover {
    background-color: ${({ $isSelected, theme }) => ($isSelected ? theme.primary : theme.background)};
  }
`;

const EditorPanel = styled.div`
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
`;

const DataSection = styled.div`
  margin-bottom: 30px;
  background: ${({ theme }) => theme.surface};
  padding: 20px;
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadowSmall};
`;

const StyledTextArea = styled.textarea`
  width: 100%;
  box-sizing: border-box;
  height: 400px;
  padding: 10px;
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' | 'success' }>`
  padding: 10px 20px;
  border: none;
  background-color: ${({ theme, $variant }) => 
    $variant === 'danger' ? theme.danger : 
    $variant === 'success' ? theme.success : 
    theme.primary};
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 10px;
`;

const ModeSwitcher = styled.div`
  button {
    padding: 8px 16px;
    margin-right: 10px;
    border: 1px solid ${({ theme }) => theme.border};
    cursor: pointer;
    background-color: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.text};
    border-radius: 6px;
  }
  button.active {
    background-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border-color: ${({ theme }) => theme.primary};
  }
`;

// --- Interfaces ---
interface User { id: string; }
interface UserData { profile: string; preferences: string; subscription: string; activity: string; }
interface Script { id: string; title?: string; }
type AdminMode = 'users' | 'scripts' | 'translations';

const AdminDashboard: React.FC<{ user: FirebaseUser }> = ({ user }) => {
  const [mode, setMode] = useState<AdminMode>('users');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State for all modes
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<Partial<UserData>>({});

  const [languages] = useState(['en', 'fr', 'es']);
  const [selectedLang, setSelectedLang] = useState('en');
  
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [scripts, setScripts] = useState<Script[]>([]);
  const [selectedScript, setSelectedScript] = useState<Script | null>(null);
  const [scriptData, setScriptData] = useState('');

  const [selectedTranslationLang, setSelectedTranslationLang] = useState('en');
  const [translationData, setTranslationData] = useState('');
  
  // --- Data Fetching Logic based on Mode ---
  useEffect(() => {
    const resetSelections = () => {
      setSelectedUser(null);
      setSelectedScript(null);
      setUserData({});
      setScriptData('');
      setTranslationData('');
    };
    resetSelections();

    setIsLoading(true);
    setError(null);
    
    switch (mode) {
      case 'users':
        getDocs(collection(db, 'users'))
          .then(snapshot => setUsers(snapshot.docs.map(doc => ({ id: doc.id }))))
          .catch(() => setError("Failed to fetch users."))
          .finally(() => setIsLoading(false));
        break;
      case 'scripts':
      case 'translations':
        // No initial data fetch needed for these modes, it's triggered by selection
        setIsLoading(false);
        break;
    }
  }, [mode]);

  // --- Handlers for User Management ---
  const handleSelectUser = useCallback(async (user: User) => {
    setSelectedUser(user);
    setIsLoading(true);
    setUserData({});
    const dataTypes: (keyof UserData)[] = ['profile', 'preferences', 'subscription', 'activity'];
    try {
      const dataPromises = dataTypes.map(async (type) => {
        const docRef = doc(db, 'users', user.id, type, 'main');
        const docSnap = await getDoc(docRef);
        if (docSnap.exists() && docSnap.data().encrypted) {
          const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, user.id).toString(CryptoJS.enc.Utf8);
          return { [type]: JSON.stringify(JSON.parse(decrypted), null, 2) };
        }
        return { [type]: `{"message": "No document for ${type}."}` };
      });
      const allData = await Promise.all(dataPromises);
      setUserData(Object.assign({}, ...allData));
    } catch {
      setError(`Failed to fetch data for user ${user.id}.`);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  const handleSaveUserData = async (type: keyof UserData) => {
    if (!selectedUser || !userData[type]) return;
    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(JSON.parse(userData[type]!)), selectedUser.id).toString();
      await setDoc(doc(db, 'users', selectedUser.id, type, 'main'), { encrypted });
      alert(`${type} data saved!`);
    } catch { alert(`Error saving ${type}. Is the JSON valid?`); }
  };

  // --- Handlers for Script Management ---
  const handleSelectScriptLang = useCallback(async (lang: string) => {
    setSelectedLang(lang);
    setSelectedCategory(null);
    setScripts([]);
    setSelectedScript(null);
    setScriptData('');
    setIsLoading(true);
    try {
        const docRef = doc(db, 'SessionScripts', lang);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8);
            setCategories(JSON.parse(decrypted).categories || []);
        }
    } catch { setError('Failed to decrypt categories.'); }
    finally { setIsLoading(false); }
  }, []);

  const handleSelectCategory = useCallback(async (category: string) => {
    setSelectedCategory(category);
    setSelectedScript(null);
    setScriptData('');
    setIsLoading(true);
    try {
      const snapshot = await getDocs(collection(db, `SessionScripts/${selectedLang}/${category}`));
      const scriptList = snapshot.docs.map(d => {
        const data = JSON.parse(CryptoJS.AES.decrypt(d.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8));
        return { id: d.id, title: data.title || 'Untitled' };
      });
      setScripts(scriptList);
    } catch { setError('Failed to fetch scripts for this category.'); }
    finally { setIsLoading(false); }
  }, [selectedLang]);

  const handleSelectScript = useCallback(async (script: Script) => {
    setSelectedScript(script);
    setIsLoading(true);
    try {
      const docRef = doc(db, `SessionScripts/${selectedLang}/${selectedCategory}`, script.id);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const decrypted = CryptoJS.AES.decrypt(docSnap.data().encrypted, SHARED_KEY).toString(CryptoJS.enc.Utf8);
        setScriptData(JSON.stringify(JSON.parse(decrypted), null, 2));
      }
    } catch { setError('Failed to decrypt script data.'); }
    finally { setIsLoading(false); }
  }, [selectedLang, selectedCategory]);

  const handleSaveScript = async () => {
    if (!selectedScript || !selectedCategory) return;
    try {
      const dataToEncrypt = JSON.parse(scriptData);
      dataToEncrypt.updatedAt = serverTimestamp(); // Add timestamp on save
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(dataToEncrypt), SHARED_KEY).toString();
      const docRef = doc(db, `SessionScripts/${selectedLang}/${selectedCategory}`, selectedScript.id);
      await setDoc(docRef, { encrypted });
      alert('Script saved!');
    } catch { alert('Error saving script. Is the JSON valid?'); }
  };

  // --- Handlers for Translation Management ---
  const handleSelectTranslationLang = useCallback(async (lang: string) => {
    setSelectedTranslationLang(lang);
    setIsLoading(true);
    try {
      const docRef = doc(db, 'AppLanguage', lang);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const data = docSnap.data();
        delete data.updatedAt; // Don't show the timestamp in the editor
        setTranslationData(JSON.stringify(data, null, 2));
      }
    } catch { setError('Failed to load translation file.'); }
    finally { setIsLoading(false); }
  }, []);
  
  const handleSaveTranslation = async () => {
    try {
      const dataToSave = JSON.parse(translationData);
      const docRef = doc(db, 'AppLanguage', selectedTranslationLang);
      await setDoc(docRef, { ...dataToSave, updatedAt: serverTimestamp() });
      alert('Translation saved!');
    } catch { alert('Error saving translation. Is the JSON valid?'); }
  };

  return (
    <DashboardContainer>
      <Header>
        <ModeSwitcher>
          <button onClick={() => setMode('users')} className={mode === 'users' ? 'active' : ''}>Users</button>
          <button onClick={() => setMode('scripts')} className={mode === 'scripts' ? 'active' : ''}>Scripts</button>
          <button onClick={() => setMode('translations')} className={mode === 'translations' ? 'active' : ''}>Translations</button>
        </ModeSwitcher>
        <div>
          <span>{user.email}</span>
          <Button $variant="danger" onClick={() => signOut(auth)} style={{ marginLeft: '15px' }}>Logout</Button>
        </div>
      </Header>
      <MainContent>
        {/* --- USERS MODE --- */}
        {mode === 'users' && (
          <>
            <LeftPanel>
              <h3>Users ({users.length})</h3>
              {users.map(u => <ListItem key={u.id} $isSelected={selectedUser?.id === u.id} onClick={() => handleSelectUser(u)} title={u.id}>{u.id}</ListItem>)}
            </LeftPanel>
            <EditorPanel>
              {isLoading && <p>Loading...</p>}
              {selectedUser ? Object.entries(userData).map(([key, value]) => (
                <DataSection key={key}>
                  <h3>{key.charAt(0).toUpperCase() + key.slice(1)}</h3>
                  <StyledTextArea value={value} onChange={e => setUserData(prev => ({ ...prev, [key]: e.target.value }))} />
                  <Button $variant="success" onClick={() => handleSaveUserData(key as keyof UserData)}>Save {key}</Button>
                </DataSection>
              )) : !isLoading && <h2>Select a user to begin editing</h2>}
            </EditorPanel>
          </>
        )}

        {/* --- SCRIPTS MODE --- */}
        {mode === 'scripts' && (
          <>
            <LeftPanel>
              <h3>Languages</h3>
              {languages.map(l => <ListItem key={l} $isSelected={selectedLang === l} onClick={() => handleSelectScriptLang(l)}>{l.toUpperCase()}</ListItem>)}
              {categories.length > 0 && <><hr/><h3>Categories</h3></>}
              {categories.map(c => <ListItem key={c} $isSelected={selectedCategory === c} onClick={() => handleSelectCategory(c)}>{c}</ListItem>)}
              {scripts.length > 0 && <><hr/><h3>Scripts</h3></>}
              {scripts.map(s => <ListItem key={s.id} $isSelected={selectedScript?.id === s.id} onClick={() => handleSelectScript(s)} title={s.id}>{s.title}</ListItem>)}
            </LeftPanel>
            <EditorPanel>
              {isLoading && <p>Loading...</p>}
              {selectedScript ? (
                <DataSection>
                  <h3>Editing Script: {selectedScript.title}</h3>
                  <StyledTextArea value={scriptData} onChange={e => setScriptData(e.target.value)} />
                  <Button $variant="success" onClick={handleSaveScript}>Save Script</Button>
                </DataSection>
              ) : !isLoading && <h2>Select a language, category, and script to edit.</h2>}
            </EditorPanel>
          </>
        )}

        {/* --- TRANSLATIONS MODE --- */}
        {mode === 'translations' && (
            <>
                <LeftPanel>
                    <h3>Languages</h3>
                    {languages.map(l => <ListItem key={l} $isSelected={selectedTranslationLang === l} onClick={() => handleSelectTranslationLang(l)}>{l.toUpperCase()}</ListItem>)}
                </LeftPanel>
                <EditorPanel>
                    {isLoading && <p>Loading...</p>}
                    {translationData ? (
                        <DataSection>
                            <h3>Editing Translation for: {selectedTranslationLang.toUpperCase()}</h3>
                            <StyledTextArea value={translationData} onChange={e => setTranslationData(e.target.value)} />
                            <Button $variant="success" onClick={handleSaveTranslation}>Save Translation</Button>
                        </DataSection>
                    ) : !isLoading && <h2>Select a language to edit its translation file.</h2>}
                </EditorPanel>
            </>
        )}
      </MainContent>
      {error && <div style={{position: 'fixed', bottom: 0, left: 0, right: 0, background: 'red', color: 'white', padding: '10px', textAlign: 'center'}}>{error}</div>}
    </DashboardContainer>
  );
};

export default AdminDashboard;