// src/components/AdRewardModal.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiPlay, FiStar, FiClock, FiGift, FiX } from 'react-icons/fi';
import { useAds } from '../hooks/useAds';
import Button from './ui/Button';

const ModalOverlay = styled.div<{ $visible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: ${({ $visible }) => ($visible ? 1 : 0)};
  visibility: ${({ $visible }) => ($visible ? 'visible' : 'hidden')};
  transition: all 0.3s ease;
  padding: 1rem;
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  text-align: center;
  position: relative;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.border};
    color: ${({ theme }) => theme.text};
  }
`;

const IconContainer = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: #000;
`;

const Title = styled.h2`
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.text};
`;

const Description = styled.p`
  margin: 0 0 2rem 0;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.5;
  font-size: 0.95rem;
`;

const Actions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const LoadingText = styled.div`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const ErrorText = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

interface AdRewardModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string; // The ID of the item (game, session) to unlock
  featureName?: string; // Optional name of the feature for display
}

const AdRewardModal: React.FC<AdRewardModalProps> = ({
  isOpen,
  onClose,
  itemId,
  featureName
}) => {
  const { t } = useTranslation();
  // Correctly use showAdToUnlockItem from the useAds hook
  const { showAdToUnlockItem, isLoading, error, clearError } = useAds();
  const [isWatching, setIsWatching] = useState(false);

  const handleUnlockItem = async () => {
    if (!itemId) {
      console.error('AdRewardModal: itemId is required to unlock an item.');
      return;
    }
    setIsWatching(true);
    clearError();

    try {
      // Call the correct function with the itemId
      const success = await showAdToUnlockItem(itemId);
      
      if (success) {
        // Close modal after successful ad watch
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (err) {
      console.error('Failed to watch ad:', err);
    } finally {
      setIsWatching(false);
    }
  };

  const handleClose = () => {
    if (!isWatching && !isLoading) {
      clearError();
      onClose();
    }
  };

  return (
    <ModalOverlay $visible={isOpen} onClick={handleClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <CloseButton onClick={handleClose} disabled={isWatching || isLoading}>
          <FiX size={20} />
        </CloseButton>
        
        <IconContainer>
          <FiPlay size={32} />
        </IconContainer>
        
        <Title>{t('adReward.unlockItemTitle', 'Unlock Feature')}</Title>
        
        <Description>
          {t('adReward.unlockItemDescription', 'Watch a short ad to unlock this feature for a limited time.')}
        </Description>
        
        <Actions>
          <Button
            variant="primary"
            onClick={handleUnlockItem}
            disabled={isWatching || isLoading}
            fullWidth
          >
            <FiPlay size={16} />
            {isWatching 
              ? t('adReward.watching', 'Watching Ad...')
              : t('adReward.unlockNow', 'Unlock with Ad')
            }
          </Button>
          
          <Button
            variant="ghost"
            onClick={handleClose}
            disabled={isWatching || isLoading}
            fullWidth
          >
            {t('common.cancel', 'Cancel')}
          </Button>
        </Actions>
        
        {isLoading && (
          <LoadingText>
            {t('adReward.loading', 'Loading ad...')}
          </LoadingText>
        )}
        
        {error && (
          <ErrorText>
            {t('adReward.error', 'Failed to load ad. Please try again.')}
          </ErrorText>
        )}
      </ModalContent>
    </ModalOverlay>
  );
};

export default AdRewardModal;