// scripts/upload-translations.js

// This script uploads ONLY the translation files from /public/locales/[lang]/translation.json
// to the 'AppLanguage' collection in Firestore. It is a simplified version
// of the main upload script, focused solely on localization content.

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import admin from 'firebase-admin';

// --- Environment Configuration ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- Firebase Admin Initialization ---
// IMPORTANT: Update this path to point to your actual service account key file.
const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');
let serviceAccount;

try {
  serviceAccount = fs.readJsonSync(serviceAccountPath);
} catch (error) {
  console.error(`❌ Error reading service account key file at: ${serviceAccountPath}`);
  console.error("Please make sure the file exists and the path is correct in the script.");
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
const db = admin.firestore();
const { FieldValue } = admin.firestore;

// --- Path Configuration ---
const TRANSLATIONS_SOURCE_DIR = path.join(__dirname, '../public/locales');
const LANGUAGES = ['fr', 'en', 'es'];

/**
 * Reads local translation files and uploads them to the 'AppLanguage' collection in Firestore.
 */
async function uploadTranslations() {
    console.log('Starting translations upload...');
    const batch = db.batch();

    for (const lang of LANGUAGES) {
        const translationFilePath = path.join(TRANSLATIONS_SOURCE_DIR, lang, 'translation.json');
        
        if (!fs.existsSync(translationFilePath)) {
            console.warn(`- Translation file not found for '${lang}' at ${translationFilePath}, skipping.`);
            continue;
        }

        try {
            const translationData = fs.readJsonSync(translationFilePath);
            const docRef = db.collection('AppLanguage').doc(lang);
            
            // Set the document with the translation data and add a server timestamp for tracking.
            batch.set(docRef, { ...translationData, updatedAt: FieldValue.serverTimestamp() }, { merge: true });
            
            console.log(`  - Prepared translation for language '${lang}'.`);
        } catch (error) {
            console.error(`- Error processing translation for '${lang}':`, error);
        }
    }

    try {
        await batch.commit();
        console.log(`\n✅ Successfully committed batch for translations.`);
    } catch (error) {
        console.error(`\n❌ Failed to commit translations batch:`, error);
    }
}

async function main() {
  console.log('--- Connecting to Firestore and starting translations upload ---\n');
  await uploadTranslations();
  console.log('\n--- Translations upload process finished. ---');
}

main().catch(console.error);