// src/games/cardiac-coherence/CardiacCoherenceSetupModal.tsx

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useShallow } from 'zustand/react/shallow';
import { useAppStore, BinauralConfig } from '../../store/useAppStore';
import { baseFrequencyPresetCategories, beatFrequencyPresetCategories, findPresetById, FrequencyPreset } from '../../config/frequencyPresets';
import ReusableModal from '../../components/ReusableModal';
import AppIcon from '../../components/AppIcon';

// --- Enhanced Settings Interfaces ---
interface GameSpecificAudioConfig {
  url: string;
  volume: number;
}

export interface CardiacCoherenceSettings {
  mode: 'adult' | 'child';
  duration: number; // in minutes
  useTTS: boolean;
  useSoundEffects: boolean;
  
  enableMusic: boolean;
  musicConfig: GameSpecificAudioConfig;
  
  enableAmbient: boolean;
  ambientConfig: GameSpecificAudioConfig;

  enableBinaural: boolean;
  binauralConfig: BinauralConfig;
}

// --- Component Props ---
interface CardiacCoherenceSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStart: (settings: CardiacCoherenceSettings) => void;
}

// --- Manifest Types ---
interface AudioManifestItem { id: string; name: string; url: string; }

// --- Styled Components ---
const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: ${({ theme }) => theme.text};
`;

const SettingSection = styled.div`
  padding: 0.75rem 0;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  &:last-of-type { border-bottom: none; }
`;

const SettingRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SettingLabel = styled.label`
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const AudioDetails = styled.div`
  padding-left: 2.25rem;
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  select, input[type="range"] {
    width: 100%;
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid ${({ theme }) => theme.border};
    background-color: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
  }
  .description-text { font-size: 0.85rem; color: ${({ theme }) => theme.textSecondary}; margin-top: 0.5rem; min-height: 2em; }
`;

const ToggleSwitchContainer = styled.label`
  position: relative; display: inline-block; width: 50px; height: 28px;
`;
const ToggleInput = styled.input`
  opacity: 0; width: 0; height: 0;
  &:checked + .slider { background-color: ${({ theme }) => theme.primary}; }
  &:checked + .slider:before { transform: translateX(22px); }
`;
const ToggleSlider = styled.span`
  position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border: 1px solid ${({ theme }) => theme.border};
  transition: .4s; border-radius: 28px;
  &:before {
    position: absolute; content: ""; height: 20px; width: 20px; left: 3px; bottom: 3px;
    background-color: white; transition: .4s; border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }
`;
const SegmentedControl = styled.div`
  display: flex; border: 1px solid ${({ theme }) => theme.primary}; border-radius: 8px; overflow: hidden;
  button {
    flex: 1; padding: 0.6rem 1rem; border: none; background-color: transparent;
    color: ${({ theme }) => theme.text}; cursor: pointer; font-size: 0.9rem; transition: background-color 0.2s;
    &.active { background-color: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight}; font-weight: 600; }
    &:not(:last-child) { border-right: 1px solid ${({ theme }) => theme.primary}; }
  }
`;
const DurationSelector = styled.div`
  display: flex; flex-direction: column; gap: 0.5rem; padding: 0.5rem 0;
  input[type="range"] { width: 100%; }
  .duration-label { text-align: center; font-weight: 500; color: ${({ theme }) => theme.primary}; }
`;
const PlayButton = styled.button`
  background: ${({ theme }) => theme.primary}; color: ${({ theme }) => theme.textLight}; border: none;
  border-radius: 8px; padding: 0.8rem 1.5rem; font-size: 1rem; font-weight: 500; cursor: pointer;
  transition: all 0.2s ease; display: inline-flex; align-items: center; gap: 0.5rem;
  &:hover { opacity: 0.9; transform: translateY(-2px); }
`;

const CardiacCoherenceSetupModal: React.FC<CardiacCoherenceSetupModalProps> = ({ isOpen, onClose, onStart }) => {
  const { t } = useTranslation();
  
  const { preferences, subscription, audioAssets } = useAppStore(useShallow(state => ({
    preferences: state.preferences,
    subscription: state.subscription,
    audioAssets: state.audioAssets,
  })));

  const [settings, setSettings] = useState<CardiacCoherenceSettings>(() => ({
    mode: 'adult', duration: 5, useTTS: !!preferences?.ttsConfig?.provider, useSoundEffects: true,
    enableMusic: preferences?.musicConfig?.enabled ?? false,
    musicConfig: { url: preferences?.musicConfig?.url ?? '', volume: preferences?.musicConfig?.volume ?? 0.5 },
    enableAmbient: (preferences?.ambientConfig?.enabled && subscription?.premiumFeatures.ambientSounds) ?? false,
    ambientConfig: { url: preferences?.ambientConfig?.url ?? '', volume: preferences?.ambientConfig?.volume ?? 0.3 },
    enableBinaural: (preferences?.binauralConfig?.enabled && subscription?.premiumFeatures.binauralBeats) ?? false,
    binauralConfig: { ...preferences?.binauralConfig } as BinauralConfig,
  }));

  const [manifestOptions, setManifestOptions] = useState<{ music: AudioManifestItem[], ambient: AudioManifestItem[] }>({ music: [], ambient: [] });

  useEffect(() => {
    fetch('/assets/audio_manifests/audio_manifest.json')
      .then(res => res.ok ? res.json() : Promise.reject(res))
      .then((data) => {
        const music = [{ id: 'none-music', name: t('audioConfig.music.none', 'None'), url: '' }, ...data.musics, ...(audioAssets?.musics || [])];
        const ambient = [{ id: 'none-ambient', name: t('audioConfig.ambient.none', 'None'), url: '' }, ...data.ambiants, ...(audioAssets?.ambiants || [])];
        setManifestOptions({ music, ambient });
      }).catch(() => {
        const music = [{ id: 'none-music', name: t('audioConfig.music.none', 'None'), url: '' }, ...(audioAssets?.musics || [])];
        const ambient = [{ id: 'none-ambient', name: t('audioConfig.ambient.none', 'None'), url: '' }, ...(audioAssets?.ambiants || [])];
        setManifestOptions({ music, ambient });
      });
  }, [t, audioAssets]);


  const handleSettingChange = (key: keyof CardiacCoherenceSettings, value: any) => setSettings(prev => ({ ...prev, [key]: value }));
  const handleAudioConfigChange = (type: 'musicConfig' | 'ambientConfig' | 'binauralConfig', config: Partial<GameSpecificAudioConfig | BinauralConfig>) => setSettings(prev => ({ ...prev, [type]: { ...prev[type], ...config } }));

  const handleBinauralPresetChange = (type: 'base' | 'beat', presetId: string) => {
    const categories = type === 'base' ? baseFrequencyPresetCategories : beatFrequencyPresetCategories;
    const preset = findPresetById(categories, presetId);
    if(preset) {
        handleAudioConfigChange('binauralConfig', type === 'base' ? { baseFrequency: preset.value, baseFrequencyPresetId: presetId } : { beatFrequency: preset.value, beatFrequencyPresetId: presetId });
    } else {
        handleAudioConfigChange('binauralConfig', type === 'base' ? { baseFrequencyPresetId: null } : { beatFrequencyPresetId: null });
    }
  };

  return (
    <ReusableModal isOpen={isOpen} onClose={onClose} title={t('games.cardiacCoherence.setup.title')} titleIcon={<AppIcon name="sliders" />}
      footerContent={<PlayButton onClick={() => onStart(settings)}><AppIcon name="play" />{t('game.modal.start')}</PlayButton>} >
      <SettingsContainer>
        {/* General Settings */}
        <SettingSection>
            <SettingRow><SettingLabel><AppIcon name="users" />{t('games.cardiacCoherence.setup.mode')}</SettingLabel><SegmentedControl><button className={settings.mode === 'adult' ? 'active' : ''} onClick={() => handleSettingChange('mode', 'adult')}>{t('games.cardiacCoherence.setup.adult')}</button><button className={settings.mode === 'child' ? 'active' : ''} onClick={() => handleSettingChange('mode', 'child')}>{t('games.cardiacCoherence.setup.child')}</button></SegmentedControl></SettingRow>
        </SettingSection>
        <SettingSection><DurationSelector><SettingLabel><AppIcon name="clock" />{t('games.cardiacCoherence.setup.duration')}</SettingLabel><input type="range" min="1" max="15" step="1" value={settings.duration} onChange={(e) => handleSettingChange('duration', parseInt(e.target.value, 10))} /><div className="duration-label">{settings.duration} {t('units.minutes', 'min')}</div></DurationSelector></SettingSection>

        {/* Audio Settings */}
        <SettingSection>
          <SettingRow><SettingLabel><AppIcon name="music" />{t('audioConfig.musicTitle')}</SettingLabel><ToggleSwitchContainer><ToggleInput type="checkbox" checked={settings.enableMusic} onChange={(e) => handleSettingChange('enableMusic', e.target.checked)} /><ToggleSlider className="slider" /></ToggleSwitchContainer></SettingRow>
          {settings.enableMusic && <AudioDetails><select value={settings.musicConfig.url} onChange={e => handleAudioConfigChange('musicConfig', { url: e.target.value })}>{manifestOptions.music.map(opt => <option key={opt.id} value={opt.url}>{opt.name}</option>)}</select><input type="range" min="0" max="1" step="0.01" value={settings.musicConfig.volume} onChange={e => handleAudioConfigChange('musicConfig', { volume: parseFloat(e.target.value) })} /></AudioDetails>}
        </SettingSection>

        <SettingSection>
          <SettingRow><SettingLabel><AppIcon name="wind" />{t('audioConfig.ambientTitle')}</SettingLabel><ToggleSwitchContainer><ToggleInput type="checkbox" disabled={!subscription?.premiumFeatures.ambientSounds} checked={settings.enableAmbient} onChange={(e) => handleSettingChange('enableAmbient', e.target.checked)} /><ToggleSlider className="slider" /></ToggleSwitchContainer></SettingRow>
           {settings.enableAmbient && <AudioDetails><select value={settings.ambientConfig.url} onChange={e => handleAudioConfigChange('ambientConfig', { url: e.target.value })}>{manifestOptions.ambient.map(opt => <option key={opt.id} value={opt.url}>{opt.name}</option>)}</select><input type="range" min="0" max="1" step="0.01" value={settings.ambientConfig.volume} onChange={e => handleAudioConfigChange('ambientConfig', { volume: parseFloat(e.target.value) })} /></AudioDetails>}
        </SettingSection>
        
        <SettingSection>
          <SettingRow><SettingLabel><AppIcon name="volume-2" />{t('audioConfig.binauralBeats')}</SettingLabel><ToggleSwitchContainer><ToggleInput type="checkbox" disabled={!subscription?.premiumFeatures.binauralBeats} checked={settings.enableBinaural} onChange={(e) => handleSettingChange('enableBinaural', e.target.checked)} /><ToggleSlider className="slider" /></ToggleSwitchContainer></SettingRow>
          {settings.enableBinaural && settings.binauralConfig && <AudioDetails>
              <div>
                  <label>{t('audioConfig.baseFrequencyPreset')}</label>
                  <select value={settings.binauralConfig.baseFrequencyPresetId || 'custom'} onChange={e => handleBinauralPresetChange('base', e.target.value)}>
                    <option value="custom">{t('audioConfig.customOption')}</option>
                    {baseFrequencyPresetCategories.map(cat => <optgroup key={cat.categoryId} label={t(cat.categoryLabelKey)}>{cat.presets.map(p => <option key={p.id} value={p.id}>{t(p.labelKey)}</option>)}</optgroup>)}
                  </select>
                  <input type="range" min="30" max="1000" step="0.1" value={settings.binauralConfig.baseFrequency} onChange={e => handleAudioConfigChange('binauralConfig', { baseFrequency: parseFloat(e.target.value), baseFrequencyPresetId: null })} />
              </div>
              <div>
                  <label>{t('audioConfig.beatFrequencyPreset')}</label>
                  <select value={settings.binauralConfig.beatFrequencyPresetId || 'custom'} onChange={e => handleBinauralPresetChange('beat', e.target.value)}>
                    <option value="custom">{t('audioConfig.customOption')}</option>
                    {beatFrequencyPresetCategories.map(cat => <optgroup key={cat.categoryId} label={t(cat.categoryLabelKey)}>{cat.presets.map(p => <option key={p.id} value={p.id}>{t(p.labelKey)}</option>)}</optgroup>)}
                  </select>
                  <input type="range" min="0.5" max="50" step="0.1" value={settings.binauralConfig.beatFrequency} onChange={e => handleAudioConfigChange('binauralConfig', { beatFrequency: parseFloat(e.target.value), beatFrequencyPresetId: null })} />
              </div>
              <input type="range" min="0" max="1" step="0.01" value={settings.binauralConfig.volume} onChange={e => handleAudioConfigChange('binauralConfig', { volume: parseFloat(e.target.value) })} />
          </AudioDetails>}
        </SettingSection>

        <SettingSection>
          <SettingRow><SettingLabel><AppIcon name="mic" />{t('games.cardiacCoherence.setup.useTTS')}</SettingLabel><ToggleSwitchContainer><ToggleInput type="checkbox" checked={settings.useTTS} onChange={(e) => handleSettingChange('useTTS', e.target.checked)} /><ToggleSlider className="slider" /></ToggleSwitchContainer></SettingRow>
        </SettingSection>
        <SettingSection>
          <SettingRow><SettingLabel><AppIcon name="bell" />{t('games.cardiacCoherence.setup.useSoundEffects')}</SettingLabel><ToggleSwitchContainer><ToggleInput type="checkbox" checked={settings.useSoundEffects} onChange={(e) => handleSettingChange('useSoundEffects', e.target.checked)} /><ToggleSlider className="slider" /></ToggleSwitchContainer></SettingRow>
        </SettingSection>
      </SettingsContainer>
    </ReusableModal>
  );
};

export default CardiacCoherenceSetupModal;