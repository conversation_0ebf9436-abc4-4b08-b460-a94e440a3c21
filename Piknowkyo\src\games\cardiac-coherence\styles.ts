// src/games/cardiac-coherence/styles.ts

import styled, { keyframes } from 'styled-components';

interface GameContainerProps {
  $backgroundUrl: string;
}

export const GameContainer = styled.div<GameContainerProps>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1500;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url(${({ $backgroundUrl }) => $backgroundUrl});
  background-size: cover;
  background-position: center;
  transition: background-image 0.5s ease-in-out;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.35);
    z-index: 1;
  }
`;

export const UIContainer = styled.div`
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  color: #fff;
  width: 100%;
  height: 80vh;
`;

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

export const InstructionText = styled.h2`
  font-size: 2.8rem;
  font-weight: 300;
  margin: 0;
  padding: 0rem;
  color: #f0e8ff;
  text-shadow:
    0 0 8px rgba(255, 255, 255, 0.7),
    0 0 10px rgba(255, 255, 255, 0.7),
    0 2px 12px rgba(0, 0, 0, 0.6);
  animation: ${fadeIn} 0.8s ease-out;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 2.2rem;
  }
`;

export const VisualizerContainer = styled.div`
  width: 100%;
  flex-grow: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  position: relative;
`;

export const BreathingVisualizer = styled.div`
  width: 120px;
  height: 120px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 50px rgba(255, 255, 255, 0.3);
  
  // The transform is updated on every frame by React, so no CSS transition is needed for the movement.
  // This ensures the animation is perfectly in sync with the game state.
  transition: none; 
  position: absolute;
  bottom: 0;

  @media (max-width: 768px) {
    width: 100px;
    height: 100px;
  }
`;

export const TimerDisplay = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 500;
  z-index: 5;
`;

export const PauseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;