// src/components/SignupForm.tsx
import React, { useState } from 'react';
import { createUserWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { auth } from '../firebase';
import { FcGoogle } from 'react-icons/fc';
import { useTranslation } from 'react-i18next'; // Importez useTranslation
import {
  Form,
  Input,
  Button,
  Separator,
  GoogleButton,
  Message,
  ToggleLink
} from './common/AuthStyles';
  
  interface SignupFormProps {
    onToggleForm: (formType: 'login' | 'signup') => void;
  }
  
  const SignupForm: React.FC<SignupFormProps> = ({ onToggleForm }) => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation(); // Initialisez useTranslation
  
    const handleSignup = async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      setError(null);
      setSuccess(null);
  
      if (password !== confirmPassword) {
        setError(t('auth.signup.error_password_mismatch')); // Traduction ici
        setLoading(false);
        return;
      }
  
      try {
        await createUserWithEmailAndPassword(auth, email, password);
        console.log("User registered successfully");
        setSuccess(t('auth.common.success_redirect')); // Traduction ici
      } catch (err: any) {
        console.error("Error signing up:", err);
        if (err.code === 'auth/email-already-in-use') {
          setError(t('auth.signup.error_email_in_use')); // Traduction ici
        } else if (err.code === 'auth/weak-password') {
          setError(t('auth.signup.error_weak_password')); // Traduction ici
        } else {
          setError(t('auth.signup.error_general')); // Traduction ici
        }
      } finally {
        setLoading(false);
      }
    };
  
    const handleGoogleSignup = async () => {
      setLoading(true);
      setError(null);
      setSuccess(null);
      const provider = new GoogleAuthProvider();
      try {
        await signInWithPopup(auth, provider);
        console.log("User signed up successfully with Google");
        setSuccess(t('auth.common.success_redirect')); // Traduction ici
      } catch (err: any) {
        console.error("Error signing up with Google:", err);
        if (err.code === 'auth/popup-closed-by-user') {
          setError(t('auth.login.error_google_popup_closed')); // Utilisez la même clé si le message est identique
        } else {
          setError(t('auth.signup.error_general')); // Traduction ici
        }
      } finally {
        setLoading(false);
      }
    };
  
    return (
      <>
        <Form onSubmit={handleSignup}>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={t('auth.common.email_placeholder')} // Traduction ici
            required
            disabled={loading}
          />
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder={t('auth.common.password_placeholder')} // Traduction ici
            required
            disabled={loading}
          />
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder={t('auth.signup.confirm_password_placeholder')} // Traduction ici
            required
            disabled={loading}
          />
          <Button type="submit" disabled={loading}>
            {loading ? t('auth.signup.button_loading') : t('auth.signup.button')} {/* Traduction ici */}
          </Button>
        </Form>
  
        <Separator>{t('auth.common.or_separator')}</Separator> {/* Traduction ici */}
  
        <GoogleButton
          onClick={handleGoogleSignup}
          disabled={loading}
        >
          <FcGoogle size={24} />
          {loading ? t('auth.signup.google_button_loading') : t('auth.signup.google_button')} {/* Traduction ici */}
        </GoogleButton>
  
        {error && <Message type="error">{error}</Message>}
        {success && <Message type="success">{success}</Message>}
        {loading && !error && !success && <Message type="loading">{t('auth.common.please_wait_loading')}</Message>} {/* Traduction ici */}
  
        <ToggleLink onClick={() => onToggleForm('login')} disabled={loading}>
          {t('auth.signup.toggle_login')} {/* Traduction ici */}
        </ToggleLink>
      </>
    );
  };
  
  export default SignupForm;