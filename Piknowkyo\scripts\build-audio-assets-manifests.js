// scripts/build-audio-assets-manifests.js
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const PUBLIC_ASSETS_DIR = path.join(__dirname, '../public/assets');
const MUSICS_DIR = path.join(PUBLIC_ASSETS_DIR, 'musics');
const AMBIANTS_DIR = path.join(PUBLIC_ASSETS_DIR, 'ambiants');
const AUDIO_MANIFESTS_DIR = path.join(PUBLIC_ASSETS_DIR, 'audio_manifests');

async function buildAudioAssetsManifests() {
  fs.ensureDirSync(AUDIO_MANIFESTS_DIR); // Crée le dossier s'il n'existe pas

  const musicAssets = [];
  const ambientAssets = [];

  // Fonction pour scanner un répertoire et collecter les assets
  const scanDirectory = (dirPath, type) => {
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      files.forEach(file => {
        if (file.endsWith('.mp3')) { // Ou d'autres formats audio
          const assetName = path.basename(file, '.mp3');
          const assetUrl = `/assets/${type}/${file}`; // Chemin URL public
          (type === 'musics' ? musicAssets : ambientAssets).push({
            id: assetName,
            name: assetName.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase()), // Formate le nom
            url: assetUrl,
            isUserUploaded: false // Ces assets sont par défaut
          });
          console.log(`Scanned ${type}/${file}`);
        }
      });
    } else {
      console.warn(`Directory not found: ${dirPath}. No assets will be listed for ${type}.`);
    }
  };

  scanDirectory(MUSICS_DIR, 'musics');
  scanDirectory(AMBIANTS_DIR, 'ambiants');

  const manifestData = {
    musics: musicAssets,
    ambiants: ambientAssets
  };

  const outputPath = path.join(AUDIO_MANIFESTS_DIR, 'audio_manifest.json');
  fs.writeFileSync(outputPath, JSON.stringify(manifestData, null, 2));
  console.log(`Audio assets manifest written to ${outputPath}`);
}

buildAudioAssetsManifests();