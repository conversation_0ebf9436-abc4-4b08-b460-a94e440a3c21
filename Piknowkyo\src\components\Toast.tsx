import React from 'react';
import styled from 'styled-components';

const ToastContainer = styled.div`
  position: fixed;
  left: 50%;
  bottom: 80px;
  transform: translateX(-50%);
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 1rem 2rem;
  z-index: 200;
  font-size: 1rem;
  min-width: 200px;
  text-align: center;
`;

const Toast: React.FC<{ message: string }> = ({ message }) => {
  if (!message) return null;
  return <ToastContainer>{message}</ToastContainer>;
};

export default Toast;
