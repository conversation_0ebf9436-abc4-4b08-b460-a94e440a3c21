// src/ThemeProvider.tsx

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { lightTheme, darkTheme } from './themes';
import { useAuth } from './hooks/useAuth';
import { useAppStore } from './store/useAppStore';

type ThemeContextType = {
  darkMode: boolean;
  toggleTheme: () => void;
  isThemeLoading: boolean;
};

const ThemeContext = createContext<ThemeContextType>({
  darkMode: false,
  toggleTheme: () => console.warn('toggleTheme called before ThemeProvider was initialized'),
  isThemeLoading: true,
});

export const useTheme = () => useContext(ThemeContext);

const getInitialLocalTheme = (): boolean => {
  const storedTheme = window.localStorage.getItem('theme');
  if (storedTheme) {
    return storedTheme === 'dark';
  }
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const userPreferences = useAppStore((state) => state.preferences);
  const updatePreferences = useAppStore((state) => state.updatePreferences);
  const isStoreInitialized = useAppStore((state) => state.isInitialized);

  const [darkMode, setDarkMode] = useState<boolean>(getInitialLocalTheme);

  // This is the core logic for syncing the theme upon login.
  useEffect(() => {
    // Wait until the user is logged in and their preferences are loaded.
    if (!user || !isStoreInitialized || !userPreferences) {
      return;
    }

    const localThemeIsDark = darkMode;
    const firestoreTheme = userPreferences.appTheme;

    if (firestoreTheme) {
      // Case 1: A theme preference is already saved in Firestore.
      // We make the UI match this saved preference.
      const firestoreThemeIsDark = firestoreTheme === 'dark';
      if (firestoreThemeIsDark !== localThemeIsDark) {
        setDarkMode(firestoreThemeIsDark);
      }
    } else {
      // Case 2: NO theme preference is saved in Firestore.
      // This means we should save the user's CURRENT choice (from the login page) to their profile.
      console.log(`[ThemeProvider] No theme preference found in Firestore. Saving current local theme: ${localThemeIsDark ? 'dark' : 'light'}`);
      const newPrefs = JSON.parse(JSON.stringify(userPreferences));
      newPrefs.appTheme = localThemeIsDark ? 'dark' : 'light';
      updatePreferences(user.uid, newPrefs);
      // We also update localStorage so everything is in sync for the next logout.
      window.localStorage.setItem('theme', newPrefs.appTheme);
    }
    // This effect should only run when the user's session status changes, not on every theme toggle.
    // That's why `darkMode` is not in the dependency array.
  }, [user, isStoreInitialized, userPreferences, updatePreferences]);

  const toggleTheme = useCallback(() => {
    // We calculate the next state based on the current state.
    const newMode = !darkMode;
    // Update the UI state immediately for responsiveness.
    setDarkMode(newMode);

    if (user?.uid && isStoreInitialized && userPreferences) {
      // If the user is logged in, we update their preference in Firestore.
      const newPrefs = JSON.parse(JSON.stringify(userPreferences));
      newPrefs.appTheme = newMode ? 'dark' : 'light';
      updatePreferences(user.uid, newPrefs);
    } else {
      // If logged out, we just save to localStorage.
      window.localStorage.setItem('theme', newMode ? 'dark' : 'light');
    }
  }, [darkMode, user, isStoreInitialized, userPreferences, updatePreferences]);

  const value = {
    darkMode,
    toggleTheme,
    isThemeLoading: user ? !isStoreInitialized : false,
  };

  return (
    <ThemeContext.Provider value={value}>
      <StyledThemeProvider theme={darkMode ? darkTheme : lightTheme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};