// src/games/zen-tetris/logic.ts

// --- Définitions de types et constantes ---
export type TetrisGrid = (number | null)[][]; // 0 pour vide, 1-7 pour type de pièce
export type TetrominoShape = (1 | 0)[][];

export interface Tetromino {
  shape: TetrominoShape;
  color: number; // Index de couleur
  x: number; // Position X sur la grille
  y: number; // Position Y sur la grille
}

export interface TetrisGameState {
  grid: TetrisGrid;
  currentPiece: Tetromino | null;
  nextPiece: Tetromino | null;
  score: number;
  level: number;
  linesCleared: number;
  gameOver: boolean;
  isDropping: boolean; // Indique si la pièce est en train de chuter (soft drop)
  // ... autres états si besoin
}

// Couleurs des pièces pour un thème Zen (index 1-7 pour les 7 pièces)
// Utilisez un mapping vers vos couleurs de thème dans le composant React
export const TETROMINO_COLORS = [
  null, // Index 0 pour vide
  '#2ecc71', // I (vert clair)
  '#3498db', // J (bleu clair)
  '#9b59b6', // L (violet)
  '#f1c40f', // O (jaune)
  '#e67e22', // S (orange)
  '#e74c3c', // T (rouge)
  '#1abc9c', // Z (cyan)
];


export const GRID_WIDTH = 10;
export const GRID_HEIGHT = 20; // 20 rangées visibles

// Définition des formes de tétrominos (rotations de base)
const TETROMINOS_SHAPES: { [key: string]: TetrominoShape[] } = {
  I: [
    [[0, 0, 0, 0], [1, 1, 1, 1], [0, 0, 0, 0], [0, 0, 0, 0]],
    [[0, 1, 0, 0], [0, 1, 0, 0], [0, 1, 0, 0], [0, 1, 0, 0]],
  ],
  J: [
    [[1, 0, 0], [1, 1, 1], [0, 0, 0]],
    [[0, 1, 1], [0, 1, 0], [0, 1, 0]],
    [[0, 0, 0], [1, 1, 1], [0, 0, 1]],
    [[0, 1, 0], [0, 1, 0], [1, 1, 0]],
  ],
  L: [
    [[0, 0, 1], [1, 1, 1], [0, 0, 0]],
    [[0, 1, 0], [0, 1, 0], [0, 1, 1]],
    [[0, 0, 0], [1, 1, 1], [1, 0, 0]],
    [[1, 1, 0], [0, 1, 0], [0, 1, 0]],
  ],
  O: [
    [[1, 1], [1, 1]],
  ],
  S: [
    [[0, 1, 1], [1, 1, 0], [0, 0, 0]],
    [[0, 1, 0], [0, 1, 1], [0, 0, 1]],
  ],
  T: [
    [[0, 1, 0], [1, 1, 1], [0, 0, 0]],
    [[0, 1, 0], [0, 1, 1], [0, 1, 0]],
    [[0, 0, 0], [1, 1, 1], [0, 1, 0]],
    [[0, 1, 0], [1, 1, 0], [0, 1, 0]],
  ],
  Z: [
    [[1, 1, 0], [0, 1, 1], [0, 0, 0]],
    [[0, 0, 1], [0, 1, 1], [0, 1, 0]],
  ],
};

// --- Fonctions utilitaires internes ---

function createEmptyGrid(): TetrisGrid {
  return Array(GRID_HEIGHT).fill(null).map(() => Array(GRID_WIDTH).fill(0));
}

function getRandomPiece(): Tetromino {
  const pieceTypes = Object.keys(TETROMINOS_SHAPES);
  const randomType = pieceTypes[Math.floor(Math.random() * pieceTypes.length)];
  const shape = TETROMINOS_SHAPES[randomType][0]; // Commence avec la première rotation
  const colorIndex = pieceTypes.indexOf(randomType) + 1; // 1-indexed pour les couleurs
  return {
    shape,
    color: colorIndex,
    x: Math.floor(GRID_WIDTH / 2) - Math.floor(shape[0].length / 2),
    y: 0, // Commence en haut
  };
}

function collide(grid: TetrisGrid, piece: Tetromino): boolean {
  for (let y = 0; y < piece.shape.length; y++) {
    for (let x = 0; x < piece.shape[y].length; x++) {
      if (piece.shape[y][x] !== 0) { // Si c'est un bloc de la pièce
        const gridY = piece.y + y;
        const gridX = piece.x + x;

        // Collision avec les murs ou le bas
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY >= GRID_HEIGHT) {
          return true;
        }
        // Collision avec un bloc existant dans la grille (si pas hors limite Y)
        if (gridY >= 0 && grid[gridY] && grid[gridY][gridX] !== 0) {
          return true;
        }
      }
    }
  }
  return false;
}

function mergePieceToGrid(grid: TetrisGrid, piece: Tetromino): TetrisGrid {
  const newGrid = grid.map(row => [...row]); // Copie profonde
  piece.shape.forEach((row, y) => {
    row.forEach((cell, x) => {
      if (cell !== 0) {
        const gridY = piece.y + y;
        const gridX = piece.x + x;
        if (gridY >= 0 && gridY < GRID_HEIGHT && gridX >= 0 && gridX < GRID_WIDTH) {
          newGrid[gridY][gridX] = piece.color;
        }
      }
    });
  });
  return newGrid;
}

function clearFullLines(grid: TetrisGrid): { newGrid: TetrisGrid; linesCleared: number } {
  let linesCleared = 0;
  const newGrid = grid.filter(row => !row.every(cell => cell !== 0)); // Filtre les lignes complètes
  linesCleared = GRID_HEIGHT - newGrid.length;
  // Ajoute des lignes vides en haut pour remplacer celles effacées
  while (newGrid.length < GRID_HEIGHT) {
    newGrid.unshift(Array(GRID_WIDTH).fill(0));
  }
  return { newGrid, linesCleared };
}

function rotateShape(shape: TetrominoShape): TetrominoShape {
  const numRows = shape.length;
  const numCols = shape[0].length;
  const newShape: TetrominoShape = Array(numCols).fill(0).map(() => Array(numRows).fill(0));
  for (let y = 0; y < numRows; y++) {
    for (let x = 0; x < numCols; x++) {
      newShape[x][numRows - 1 - y] = shape[y][x];
    }
  }
  return newShape;
}


// --- Fonctions d'état du jeu (exposées) ---

export const initializeTetrisGame = (initialLevel: number = 1): TetrisGameState => {
  const initialGrid = createEmptyGrid();
  const firstPiece = getRandomPiece();
  const nextPiece = getRandomPiece();

  // Vérifier si la première pièce peut être placée
  if (collide(initialGrid, firstPiece)) {
    return {
      grid: initialGrid,
      currentPiece: null,
      nextPiece: null,
      score: 0,
      level: Math.max(1, initialLevel),
      linesCleared: 0,
      gameOver: true, // Game Over dès le début si la pièce ne peut pas apparaître
      isDropping: false,
    };
  }

  return {
    grid: initialGrid,
    currentPiece: firstPiece,
    nextPiece: nextPiece,
    score: 0,
    level: Math.max(1, initialLevel), // S'assurer que le niveau est au moins 1
    linesCleared: 0,
    gameOver: false,
    isDropping: false,
  };
};

export const movePiece = (state: TetrisGameState, direction: 'left' | 'right' | 'down'): TetrisGameState => {
  if (state.gameOver || !state.currentPiece) return state;

  const newPiece = { ...state.currentPiece };
  if (direction === 'left') newPiece.x--;
  else if (direction === 'right') newPiece.x++;
  else if (direction === 'down') newPiece.y++;

  if (!collide(state.grid, newPiece)) {
    // Le mouvement est valide
    return { ...state, currentPiece: newPiece };
  } else if (direction === 'down') {
    // La pièce ne peut plus descendre, la fixer
    const newGrid = mergePieceToGrid(state.grid, state.currentPiece);
    const { newGrid: clearedGrid, linesCleared } = clearFullLines(newGrid);

    let newScore = state.score;
    // Score simple basé sur le nombre de lignes
    if (linesCleared === 1) newScore += 100 * state.level;
    else if (linesCleared === 2) newScore += 300 * state.level;
    else if (linesCleared === 3) newScore += 500 * state.level;
    else if (linesCleared === 4) newScore += 800 * state.level; // Tetris!

    const newLinesCleared = state.linesCleared + linesCleared;
    const newLevel = Math.min(100, 1 + Math.floor(newLinesCleared / 10)); // 10 lignes par niveau, max 100

    const newCurrentPiece = state.nextPiece; // L'ancienne "nextPiece" devient la "currentPiece"
    const newNextPiece = getRandomPiece(); // Générer une NOUVELLE "nextPiece"

    // CORRECTION ICI: Vérifier que newCurrentPiece n'est pas null avant de le passer à collide
    // Si newCurrentPiece est null (ce qui ne devrait normalement pas arriver ici si la logique est saine,
    // mais TypeScript le signale), alors c'est une condition de Game Over immédiate.
    const isGameOver = !newCurrentPiece || collide(clearedGrid, newCurrentPiece); // <-- CORRECTION

    return {
      ...state,
      grid: clearedGrid,
      currentPiece: newCurrentPiece, // Assignation du nouveau currentPiece
      nextPiece: newNextPiece,       // Assignation du nouveau nextPiece
      score: newScore,
      level: newLevel,
      linesCleared: newLinesCleared,
      gameOver: isGameOver,
    };
  }
  return state; // Pas de mouvement si collision et pas une chute
};


export const rotatePiece = (state: TetrisGameState): TetrisGameState => {
  if (state.gameOver || !state.currentPiece) return state;

  const rotatedShape = rotateShape(state.currentPiece.shape);
  const newPiece = { ...state.currentPiece, shape: rotatedShape };

  // Simple collision check après rotation
  if (!collide(state.grid, newPiece)) {
    return { ...state, currentPiece: newPiece };
  }
  // Logique plus complexe pour le "kick" (décalage si collision) pourrait être ajoutée ici
  return state;
};


export const updateGame = (state: TetrisGameState): TetrisGameState => {
  if (state.gameOver || !state.currentPiece) return state;

  // Tentative de déplacer la pièce vers le bas
  return movePiece(state, 'down');
};