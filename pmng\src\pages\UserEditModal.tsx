// pmng/src/pages/UserEditModal.tsx

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { doc, getDoc, setDoc } from 'firebase/firestore'; 
import CryptoJS from 'crypto-js';
import { db } from '../firebase';
import Modal from '../components/Modal';

// --- Styled Components ---
const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  
  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const Fieldset = styled.fieldset`
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  legend {
    font-weight: 600;
    color: ${({ theme }) => theme.primary};
  }
`;

const FormControl = styled.div`
  label:not(.ToggleSwitch) { // Prevent applying to the styled component label
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
  }
  input, select, textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 4px;
    box-sizing: border-box;
    font-family: inherit;
  }
  textarea {
    min-height: 80px;
    font-family: monospace;
    font-size: 0.85rem;
    white-space: pre;
    background-color: ${({ theme }) => theme.background};
    // MODIFIED: Explicitly set text color for readability
    color: ${({ theme }) => theme.text};
  }
`;

// --- CORRECTED AND SIMPLIFIED TOGGLE SWITCH ---
const ToggleSwitch = styled.label`
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 1rem;

  // The actual checkbox is hidden, its state is what matters
  input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
  }
  
  // This is the visual part of the toggle
  .slider {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
  
  // When the hidden input is checked, its SIBLING .slider changes style
  input:checked + .slider {
    background-color: ${({ theme }) => theme.success};
  }
  
  input:checked + .slider:before {
    transform: translateX(20px);
  }
`;

const SaveButton = styled.button`
  padding: 12px 24px;
  border: none;
  background-color: ${({ theme }) => theme.primary};
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1.5rem;
  width: 100%;
  font-weight: 600;
`;

// --- Interfaces & Default Creators (no changes) ---
interface SubscriptionData { isActive: boolean; isTrialActive: boolean; trialEndDate: string | null; [key: string]: any; }
interface ProfileData { anonymousPseudo: string; [key: string]: any; }
interface PreferencesData { appTheme?: 'light' | 'dark'; grammaticalGender: 'masculine' | 'feminine' | 'neutral'; [key: string]: any; }
interface ActivityData { streak: number; totalMinutes: number; [key: string]: any; }
interface AudioAssetsData { [key: string]: any; }

interface FullUserData {
  subscription: SubscriptionData;
  profile: ProfileData;
  preferences: PreferencesData;
  activity: ActivityData;
  audioAssets: AudioAssetsData;
}

const createDefaultSubscription = (): SubscriptionData => ({ isActive: false, isTrialActive: false, trialEndDate: null });
const createDefaultProfile = (): ProfileData => ({ anonymousPseudo: '' });
const createDefaultPreferences = (): PreferencesData => ({ appTheme: 'light', grammaticalGender: 'neutral' });
const createDefaultActivity = (): ActivityData => ({ streak: 0, totalMinutes: 0 });
const createDefaultAudioAssets = (): AudioAssetsData => ({ musics: [], ambiants: [] });

interface UserEditModalProps { userId: string; onClose: () => void; onSave: () => void; }

const UserEditModal: React.FC<UserEditModalProps> = ({ userId, onClose, onSave }) => {
  const [data, setData] = useState<FullUserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true);
      
      const decryptOrDefault = <T,>(snap: any, key: string, creator: () => T): T => {
        if (snap.exists() && snap.data()?.encrypted) {
            try { return JSON.parse(CryptoJS.AES.decrypt(snap.data().encrypted, userId).toString(CryptoJS.enc.Utf8)); } 
            catch (e) { console.warn(`Could not decrypt ${key}.`, e); return creator(); }
        }
        console.warn(`${key} data not found for user ${userId}. Using default.`);
        return creator();
      };
      
      try {
        const docNames: (keyof FullUserData)[] = ['subscription', 'profile', 'preferences', 'activity', 'audioAssets'];
        const docRefs = docNames.map(name => doc(db, 'users', userId, name, 'main'));
        const docSnaps = await Promise.all(docRefs.map(ref => getDoc(ref)));
        
        setData({
            subscription: decryptOrDefault(docSnaps[0], 'subscription', createDefaultSubscription),
            profile: decryptOrDefault(docSnaps[1], 'profile', createDefaultProfile),
            preferences: decryptOrDefault(docSnaps[2], 'preferences', createDefaultPreferences),
            activity: decryptOrDefault(docSnaps[3], 'activity', createDefaultActivity),
            audioAssets: decryptOrDefault(docSnaps[4], 'audioAssets', createDefaultAudioAssets),
        });

      } catch (error) { console.error("Failed to fetch user data:", error); onClose(); } 
      finally { setIsLoading(false); }
    };
    fetchUserData();
  }, [userId, onClose]);

  const handleChange = <S extends keyof FullUserData, F extends keyof FullUserData[S]>(
    section: S,
    field: F,
    value: FullUserData[S][F]
  ) => {
    setData(prev => {
      if (!prev) return null;
      return { 
        ...prev, 
        [section]: { 
          ...prev[section], 
          [field]: value 
        } 
      };
    });
  };

  const handleSave = async () => {
    if (!data) return;
    setIsLoading(true);
    try {
      const promises = Object.entries(data).map(([key, sectionData]) => {
        const dataToEncrypt = { ...sectionData, updatedAt: new Date().toISOString() };
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(dataToEncrypt), userId).toString();
        return setDoc(doc(db, 'users', userId, key, 'main'), { encrypted });
      });
      await Promise.all(promises);
      onSave();
      onClose();
    } catch (error) { console.error("Failed to save user data:", error); alert("Error saving data."); } 
    finally { setIsLoading(false); }
  };
  
  const formatDateForInput = (isoDate: string | null) => {
    if (!isoDate) return '';
    const date = new Date(isoDate);
    return !isNaN(date.getTime()) ? date.toISOString().split('T')[0] : '';
  }

  return (
    <Modal title={`Edit User: ${userId}`} isOpen={true} onClose={onClose}>
      {isLoading ? <p>Loading data...</p> : data && (
        <>
          <FormGrid>
            <Fieldset>
              <legend>Subscription</legend>
              <FormControl>
                <ToggleSwitch className="ToggleSwitch">
                  <span>Premium Active</span>
                  <input 
                    type="checkbox" 
                    checked={!!data.subscription.isActive} 
                    onChange={e => handleChange('subscription', 'isActive', e.target.checked)} 
                  />
                  <span className="slider"></span>
                </ToggleSwitch>
              </FormControl>
              <FormControl>
                <ToggleSwitch className="ToggleSwitch">
                  <span>Trial Active</span>
                  <input 
                    type="checkbox"
                    checked={!!data.subscription.isTrialActive} 
                    onChange={e => handleChange('subscription', 'isTrialActive', e.target.checked)} 
                  />
                  <span className="slider"></span>
                </ToggleSwitch>
              </FormControl>
              <FormControl>
                <label htmlFor="trialEndDate">Trial End Date</label>
                <input 
                  id="trialEndDate" 
                  type="date" 
                  disabled={!data.subscription.isTrialActive} 
                  value={formatDateForInput(data.subscription.trialEndDate)} 
                  onChange={e => handleChange('subscription', 'trialEndDate', e.target.value ? new Date(e.target.value).toISOString() : null)} 
                />
              </FormControl>
            </Fieldset>
            
            <Fieldset>
              <legend>Profile</legend>
              <FormControl>
                <label htmlFor="pseudo">Anonymous Pseudo</label>
                <input 
                  id="pseudo" 
                  type="text" 
                  value={data.profile.anonymousPseudo || ''} 
                  onChange={e => handleChange('profile', 'anonymousPseudo', e.target.value)} 
                />
              </FormControl>
            </Fieldset>

            <Fieldset>
              <legend>Preferences</legend>
              <FormControl>
                <label htmlFor="appTheme">App Theme</label>
                <select 
                  id="appTheme" 
                  value={data.preferences.appTheme || 'light'} 
                  onChange={e => handleChange('preferences', 'appTheme', e.target.value as any)}
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </FormControl>
              <FormControl>
                <label htmlFor="grammaticalGender">Grammatical Gender</label>
                <select 
                  id="grammaticalGender" 
                  value={data.preferences.grammaticalGender || 'neutral'} 
                  onChange={e => handleChange('preferences', 'grammaticalGender', e.target.value as any)}
                >
                  <option value="masculine">Masculine</option>
                  <option value="feminine">Feminine</option>
                  <option value="neutral">Neutral</option>
                </select>
              </FormControl>
              <FormControl>
                <label>Other Preferences (Read-only)</label>
                <textarea 
                  readOnly 
                  value={JSON.stringify({ ttsConfig: data.preferences.ttsConfig, quickAccessLinks: data.preferences.quickAccessLinks }, null, 2)} 
                />
              </FormControl>
            </Fieldset>

            <Fieldset>
              <legend>Activity</legend>
              <FormControl>
                <label htmlFor="streak">Streak</label>
                <input 
                  id="streak" 
                  type="number" 
                  value={data.activity.streak || 0} 
                  onChange={e => handleChange('activity', 'streak', parseInt(e.target.value, 10) || 0)} 
                />
              </FormControl>
              <FormControl>
                <label htmlFor="totalMinutes">Total Minutes</label>
                <input 
                  id="totalMinutes" 
                  type="number" 
                  value={data.activity.totalMinutes || 0} 
                  onChange={e => handleChange('activity', 'totalMinutes', parseInt(e.target.value, 10) || 0)} 
                />
              </FormControl>
              <FormControl>
                <label>Other Activity (Read-only)</label>
                <textarea 
                  readOnly 
                  value={JSON.stringify({ favorites: data.activity.favoriteSessions?.length, completed: data.activity.completedSessions?.length }, null, 2)} 
                />
              </FormControl>
            </Fieldset>
          </FormGrid>
          <SaveButton onClick={handleSave} disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save All Changes'}
          </SaveButton>
        </>
      )}
    </Modal>
  );
};

export default UserEditModal;