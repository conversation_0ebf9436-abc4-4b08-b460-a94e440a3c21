// Utility functions for haptic feedback on mobile devices

export const vibrate = (pattern: number | number[]) => {
  if ('vibrate' in navigator) {
    navigator.vibrate(pattern);
  }
};

export const hapticFeedback = {
  // Light tap for piece movement
  light: () => vibrate(10),
  
  // Medium vibration for rotation
  medium: () => vibrate(25),
  
  // Strong vibration for line clear
  strong: () => vibrate([50, 50, 50]),
  
  // Game over vibration
  gameOver: () => vibrate([100, 50, 100, 50, 200]),
  
  // Level up vibration
  levelUp: () => vibrate([30, 30, 30, 30, 100])
};

export const isHapticSupported = () => {
  return 'vibrate' in navigator;
};
