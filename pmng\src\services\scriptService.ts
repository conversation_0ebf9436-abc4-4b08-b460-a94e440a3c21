// /src/services/scriptService.ts

// REMOVED: No longer using static, hardcoded settings.
// import { defaultAISettings } from '../config/aiSettings';

import { callGenerationAPI, callOptimizationAPI } from '../services/apiService';
import type { Script, ScriptSegment } from '../models/Script';

interface ScriptParams {
  title: string;
  description: string;
  language: string;
  clarity: number;
  efficiency: number;
  engagement: number;
  length: number;
}

/**
 * A helper function to get the settings for the default AI provider.
 * @param aiConfig The full AI configuration object from Firestore.
 * @returns The settings for the default provider.
 */
const getDefaultProviderSettings = (aiConfig: any) => {
  if (!aiConfig || !aiConfig.defaultProvider) {
    throw new Error("AI configuration is missing or a default provider has not been set.");
  }
  const providerId = aiConfig.defaultProvider;
  const providerSettings = aiConfig[providerId];
  if (!providerSettings || !providerSettings.selectedModel || !providerSettings.apiKey) {
    throw new Error(`Settings for the default provider "${providerId}" are incomplete. Check model and API key.`);
  }
  // This is a placeholder for where the provider's base URL would come from.
  // In a real app, this should be part of a shared config.
  providerSettings.apiBase = `https://api.${providerId === 'groq' ? 'groq.com/openai' : providerId}.com/v1`;
  return providerSettings;
};

// MODIFIED: Function now accepts aiConfig and uses it.
export const generateScript = async (params: ScriptParams, aiConfig: any): Promise<Script> => {
  const providerSettings = getDefaultProviderSettings(aiConfig);
  
  const response = await callGenerationAPI({
    ...params,
    model: providerSettings.selectedModel,
    apiKey: providerSettings.apiKey,
    apiBase: providerSettings.apiBase,
    // Assuming temperature and maxTokens are defaults for now
    temperature: 0.7, 
    maxTokens: params.length * 2 
  });
  
  const segment: ScriptSegment = {
    id: 'generated-' + Date.now().toString(),
    text: response.script,
    type: 'therapy'
  };

  return {
    title: params.title,
    description: params.description,
    language: params.language,
    segments: [segment],
    created_at: new Date()
  };
};

// MODIFIED: Function now accepts aiConfig and uses it.
export const optimizeScript = async (segment: ScriptSegment, aspect: keyof ScriptParams, currentParams: ScriptParams, aiConfig: any) => {
  const providerSettings = getDefaultProviderSettings(aiConfig);

  const optimizationParams = {
    script: segment.text,
    aspect,
    model: providerSettings.selectedModel,
    apiKey: providerSettings.apiKey,
    apiBase: providerSettings.apiBase,
  };
  
  const response = await callOptimizationAPI(optimizationParams);
  return {
    ...segment,
    text: response.optimizedScript
  };
};

// MODIFIED: Real implementation of translation using the AI provider.
export const translateScript = async (script: Script, targetLanguage: string, aiConfig: any): Promise<Script> => {
  const providerSettings = getDefaultProviderSettings(aiConfig);

  const translateSegment = async (text: string): Promise<string> => {
    const systemPrompt = `You are an expert translator. Translate the user's text to ${targetLanguage}. Respond ONLY with the translated text, without any additional comments, introductions, or quotation marks.`;
    
    try {
      const response = await fetch(`${providerSettings.apiBase}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${providerSettings.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: providerSettings.selectedModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: text }
          ],
          temperature: 0.1,
        })
      });
      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error?.message || 'Unknown API error');
      return data.choices[0]?.message?.content.trim() || '';
    } catch (e) {
      console.error(`Translation to ${targetLanguage} failed:`, e);
      throw e;
    }
  };

  const translatedSegments = await Promise.all(
    script.segments.map(async (segment) => ({
      ...segment,
      text: await translateSegment(segment.text),
    }))
  );

  return {
    ...script,
    segments: translatedSegments,
    language: targetLanguage,
    translated_to: [...(script.translated_to || []), targetLanguage]
  };
};