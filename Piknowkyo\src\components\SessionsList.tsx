// src/components/SessionsList.tsx

import React, { useMemo } from 'react';
import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppStore, SessionScript, SubscriptionState } from '../store/useAppStore';
import { useAuth } from '../hooks/useAuth';
import StarRating from './StarRating';
import PremiumGate from './PremiumGate';
import SessionTypeIcon from './SessionTypeIcon';
import { FiHeart, FiClock } from 'react-icons/fi';

// --- STYLED COMPONENTS ---

const SessionListContainer = styled.div<{ $viewMode: 'grid' | 'list' }>`
  display: grid;
  gap: ${({ $viewMode }) => $viewMode === 'list' ? '0.75rem' : '1.5rem'};
  grid-template-columns: ${({ $viewMode }) => $viewMode === 'grid' 
    ? 'repeat(auto-fill, minmax(280px, 1fr))' 
    : '1fr'};
`;

const RatingOverlay = styled.div`
  position: absolute;
  bottom: 8px;
  right: 10px;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  background-color: rgba(0, 0, 0, 0.25);
  padding: 3px 8px;
  border-radius: 12px;
  backdrop-filter: blur(4px);

  .star-icon {
    &.filled { color: #ffffff; fill: #ffffff; }
    &.empty { color: #ffffff; fill: none; opacity: 0.8; }
  }
`;

const RatingCount = styled.span`
  color: #fff;
  font-size: 0.75rem;
  font-weight: 500;
`;

const CardImage = styled.div`
  background: ${({ theme }) => `linear-gradient(135deg, ${theme.gradientStart}, ${theme.gradientEnd})`};
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
`;

const CardContent = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 0;

  h3 {
    font-size: 1.15rem; margin: 0; color: ${({ theme }) => theme.text};
    line-height: 1.4;
    overflow: hidden; 
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
  
  p {
    font-size: 0.9rem; color: ${({ theme }) => theme.textSecondary};
    line-height: 1.5; margin: 0.25rem 0 0 0;
    overflow: hidden; text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: ${({ theme }) => theme.textMuted};
  margin-top: auto;
  padding-top: 0.5rem;
`;

const CardInfo = styled.div`
  display: flex; align-items: center; gap: 0.5rem; font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary};
`;

const FavoriteButton = styled.button<{ $isFavorite: boolean; $viewMode: 'grid' | 'list' }>`
  display: flex; align-items: center; justify-content: center;
  cursor: pointer; transition: all 0.2s; border-radius: 50%;
  width: 40px; height: 40px; padding: 0;
  svg { transition: all 0.2s; }

  ${({ $viewMode, theme }) => $viewMode === 'grid' && css`
    position: absolute; top: 10px; right: 10px; z-index: 2;
    background: rgba(0, 0, 0, 0.3); border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    &:hover { background: rgba(0, 0, 0, 0.5); }
    svg { color: #fff; }
  `}
  
  ${({ $isFavorite }) => css`
    svg { fill: ${$isFavorite ? 'currentColor' : 'none'}; }
  `}

  ${({ $viewMode, $isFavorite, theme }) => $viewMode === 'list' && css`
    position: static; flex-shrink: 0;
    background: ${$isFavorite ? `${theme.primary}1A` : 'transparent'};
    border: 1px solid ${$isFavorite ? theme.primary : theme.border};
    svg { color: ${$isFavorite ? theme.primary : theme.textSecondary}; }
    &:hover { background: ${$isFavorite ? `${theme.primary}33` : `${theme.textSecondary}1A`}; }
  `}
`;

const ListActions = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
  width: 100%;
  justify-content: space-between;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid ${({ theme }) => theme.border};
`;

const ListInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ListTopContent = styled(Link)`
  display: flex;
  gap: 1rem;
  align-items: center;
  text-decoration: none;
  color: inherit;
  width: 100%;
  min-width: 0;
`;

const Card = styled.div<{ $viewMode: 'grid' | 'list' }>`
  background: ${({ theme }) => theme.surface}; border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow}; overflow: hidden;
  display: flex; transition: transform 0.2s ease-out, box-shadow 0.3s ease-out;
  border: 1px solid ${({ theme }) => theme.border}; position: relative;
  &:hover { transform: translateY(-4px); box-shadow: ${({ theme }) => theme.cardHoverShadow || theme.cardShadow}; }

  ${({ $viewMode }) => $viewMode === 'grid' && css`
    flex-direction: column;
    ${CardImage} { height: 140px; }
    ${CardContent} { 
      padding: 1rem 1.25rem; 
      h3 { -webkit-line-clamp: 2; } /* MODIFIED: Title limited to 2 lines in grid view */
      p { -webkit-line-clamp: 2; margin-bottom: 0.75rem; }
    }
    ${CardFooter} { padding: 0 1.25rem 1rem; }
  `}

  ${({ $viewMode }) => $viewMode === 'list' && css`
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
    gap: 0;

    ${ListTopContent} {
      ${CardImage} { width: 60px; height: 60px; border-radius: 8px; }
      ${CardContent} { 
        padding: 0;
        h3 { -webkit-line-clamp: 1; }
        p { -webkit-line-clamp: 1; }
      }
    }
  `}
`;

const CardLink = styled(Link)`
  text-decoration: none; color: inherit; display: contents;
`;

const NoResults = styled.div`
  text-align: center; padding: 2rem; color: ${({ theme }) => theme.textSecondary};
  grid-column: 1 / -1; width: 100%;
`;

// --- CHILD COMPONENT for rendering a single card ---
interface SessionCardProps {
  session: SessionScript;
  viewMode: 'grid' | 'list';
}

const SessionCard: React.FC<SessionCardProps> = ({ session, viewMode }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const favoriteSessions = useAppStore((state) => state.activity?.favoriteSessions);
  const toggleFavoriteSession = useAppStore((state) => state.toggleFavoriteSession);
  const isFavorite = favoriteSessions?.includes(session.id) ?? false;

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (user?.uid) {
      toggleFavoriteSession(user.uid, session.id);
    }
  };
  
  const hasRating = (session.ratingCount ?? 0) > 0;

  if (viewMode === 'list') {
    return (
      <Card $viewMode="list">
        <ListTopContent to={`/sessions/${session.id}`}>
          <CardImage><SessionTypeIcon type={session.type} size={30} /></CardImage>
          <CardContent><h3>{session.title}</h3><p>{session.description}</p></CardContent>
        </ListTopContent>
        <ListActions>
          <ListInfo>
            <CardInfo><FiClock size={14} /><span>{session.duration} {t('units.min', 'min')}</span></CardInfo>
            <StarRating rating={session.rating || 0} size={16} />
          </ListInfo>
          {user && (<FavoriteButton $isFavorite={isFavorite} $viewMode="list" onClick={handleToggleFavorite} title={t('sessions.card.toggleFavorite', 'Toggle favorite')}><FiHeart size={18} /></FavoriteButton>)}
        </ListActions>
      </Card>
    );
  }

  // Grid view
  return (
    <Card $viewMode="grid">
      <CardLink to={`/sessions/${session.id}`}>
        <CardImage>
          <SessionTypeIcon type={session.type} size={60} />
          <RatingOverlay>
            {hasRating && <RatingCount>({session.ratingCount})</RatingCount>}
            <StarRating rating={session.rating || 0} size={14} />
          </RatingOverlay>
        </CardImage>
        <CardContent>
          <h3>{session.title}</h3>
          <p>{session.description}</p>
          <CardFooter>
            <CardInfo><FiClock size={14} /><span>{session.duration} {t('units.min', 'min')}</span></CardInfo>
          </CardFooter>
        </CardContent>
      </CardLink>
      {user && (<FavoriteButton $isFavorite={isFavorite} $viewMode="grid" onClick={handleToggleFavorite} title={t('sessions.card.toggleFavorite', 'Toggle favorite')}><FiHeart size={18} /></FavoriteButton>)}
    </Card>
  );
};


// --- MAIN COMPONENT IMPLEMENTATION ---

interface SessionsListProps {
  sessions: SessionScript[];
  viewMode: 'grid' | 'list';
}

const SessionsList: React.FC<SessionsListProps> = ({ sessions, viewMode }) => {
  const { t } = useTranslation();
  const subscription = useAppStore((state) => state.subscription as SubscriptionState | null);
  
  const sortedSessions = useMemo(() => {
    return [...sessions].sort((a, b) => a.title.localeCompare(b.title));
  }, [sessions]);
  
  if (sortedSessions.length === 0) {
    return <NoResults>{t('sessions.noResults.filtered', 'No sessions match your current filters.')}</NoResults>;
  }

  return (
    <SessionListContainer $viewMode={viewMode}>
      {sortedSessions.map(session => (
        <PremiumGate
          key={session.id}
          feature={session.type === 'story' || session.type === 'meditation' ? '' : 'advancedSessions'}
          subscription={subscription}
          sessionId={session.id}
          title={session.title}
          viewMode={viewMode}
          context='session'
        >
          <SessionCard session={session} viewMode={viewMode} />
        </PremiumGate>
      ))}
    </SessionListContainer>
  );
};

export default SessionsList;