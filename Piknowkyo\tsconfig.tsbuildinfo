{"root": ["./src/app.test.tsx", "./src/app.tsx", "./src/globalstyles.tsx", "./src/langprovider.tsx", "./src/themeprovider.tsx", "./src/firebase.ts", "./src/i18n.ts", "./src/index.tsx", "./src/models.ts", "./src/pwa.d.ts", "./src/react-app-env.d.ts", "./src/reportwebvitals.ts", "./src/styled.d.ts", "./src/themes.ts", "./src/vite-env.d.ts", "./src/classes/interfaces/syncinterfaces.ts", "./src/components/adrewardmodal.tsx", "./src/components/appicon.tsx", "./src/components/audioconfigpanel.tsx", "./src/components/authpage.tsx", "./src/components/bottombar.tsx", "./src/components/collapsiblesessionnotes.tsx", "./src/components/definitionmodal.tsx", "./src/components/journalentryform.tsx", "./src/components/journalentryitem.tsx", "./src/components/languageswitcher.tsx", "./src/components/lexicontext.tsx", "./src/components/login.tsx", "./src/components/logout.tsx", "./src/components/mainmenu.tsx", "./src/components/networkstatusnotifier.tsx", "./src/components/notificationtest.tsx", "./src/components/paymenthistory.tsx", "./src/components/preferences.tsx", "./src/components/premiumgate.tsx", "./src/components/questionnaire.tsx", "./src/components/quickaccesssettingsmodal.tsx", "./src/components/recommendationmodal.tsx", "./src/components/reusablemodal.tsx", "./src/components/sessiontypeicon.tsx", "./src/components/sessionslist.tsx", "./src/components/signupform.tsx", "./src/components/splashscreen.tsx", "./src/components/starrating.tsx", "./src/components/subscriptionmanager.tsx", "./src/components/syncstatusindicator.tsx", "./src/components/toast.tsx", "./src/components/trialstatusbanner.tsx", "./src/components/updatenotification.tsx", "./src/components/delete_authstatus.tsx", "./src/components/common/authstyles.ts", "./src/components/common/button.tsx", "./src/components/common/card.tsx", "./src/components/common/index.ts", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/table.tsx", "./src/components/ui/index.ts", "./src/config/clientenvironment.ts", "./src/config/environment.ts", "./src/config/frequencypresets.ts", "./src/data/audioassets.ts", "./src/data/sessions.ts", "./src/games/gamedata.ts", "./src/games/cardiac-coherence/cardiaccoherencesetupmodal.tsx", "./src/games/cardiac-coherence/gamecomponent.tsx", "./src/games/cardiac-coherence/logic.ts", "./src/games/cardiac-coherence/styles.ts", "./src/games/common/models.tsx", "./src/games/common/components/gamemodal.tsx", "./src/games/common/components/gametimer.tsx", "./src/games/common/components/scoredisplay.tsx", "./src/games/zen-tetris/gamecomponent.tsx", "./src/games/zen-tetris/logic.ts", "./src/games/zen-tetris/styles.ts", "./src/games/zen-tetris/components/orientationhint.tsx", "./src/games/zen-tetris/hooks/useorientation.ts", "./src/games/zen-tetris/utils/haptics.ts", "./src/hooks/useads.ts", "./src/hooks/useauth.ts", "./src/hooks/usepwa.ts", "./src/hooks/usesubscription.ts", "./src/hooks/useuserpreferences.ts", "./src/lib/conversationflow.ts", "./src/lib/conversation/engine.ts", "./src/lib/conversation/states.ts", "./src/lib/conversation/types.ts", "./src/models/script.d.ts", "./src/models/script.model.ts", "./src/models/user.d.ts", "./src/models/user.model.ts", "./src/pages/aboutpage.tsx", "./src/pages/audioassetsconfigpage.tsx", "./src/pages/blogpage.tsx", "./src/pages/blogpostcommentspage.tsx", "./src/pages/categoriespage.tsx", "./src/pages/gamespage.tsx", "./src/pages/historypage.tsx", "./src/pages/homepage.tsx", "./src/pages/journalpage.tsx", "./src/pages/lexiconpage.tsx", "./src/pages/monetizationpage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/playerpage.tsx", "./src/pages/profilepage.tsx", "./src/pages/sessiondetailpage.tsx", "./src/pages/sessionspage.tsx", "./src/pages/statspage.tsx", "./src/services/adservice.ts", "./src/services/blogservice.ts", "./src/services/firestoreoptimizer.ts", "./src/services/firestoreservice.ts", "./src/services/pwaservice.ts", "./src/services/scriptsservice.ts", "./src/services/serviceworkermanager.ts", "./src/services/stripeservice.ts", "./src/services/syncservice.ts", "./src/services/tts.ts", "./src/services/ttsvoices.ts", "./src/services/updateservice.ts", "./src/services/usenetworkstatus.ts", "./src/services/usepushnotifications.ts", "./src/services/useractivityservice.ts", "./src/services/userservice.ts", "./src/store/useappstore.ts", "./src/types/blog.ts", "./src/types/definitions.ts", "./src/utils/translationutils.ts"], "version": "5.8.3"}