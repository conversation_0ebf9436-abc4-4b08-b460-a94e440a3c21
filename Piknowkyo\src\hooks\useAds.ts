// src/hooks/useAds.ts

import { useState } from 'react';
// --- ZUSTAND IMPORTS ---
import { useAppStore } from '../store/useAppStore';
import type { AdRewardPayload } from '../store/useAppStore'; // Import the type from its new location

// --- OTHER IMPORTS (Unchanged) ---
import { adService } from '../services/adService';
import { useAuth } from './useAuth';

export const useAds = () => {
  // --- ZUSTAND ACTION ---
  // Get the 'applyAdReward' action directly from our Zustand store.
  const applyAdRewardAction = useAppStore((state) => state.applyAdReward);
  
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Main function to show an ad for a specific item (session or game).
   */
  const showAdToUnlockItem = async (itemId: string): Promise<boolean> => {
    console.log('[useAds] showAdToUnlockItem called with itemId:', itemId);

    if (!user) {
      const errorMessage = "User must be logged in to watch an ad.";
      setError(errorMessage);
      console.error(`[useAds] Ad cancelled: ${errorMessage}`);
      return false;
    }

    console.log(`[useAds] User is valid (ID: ${user.uid}). Proceeding to show ad.`);
    setIsLoading(true);
    setError(null);

    try {
      const result = await adService.showRewardedAd(user.uid);
      
      console.log('[useAds] Ad service result:', result);

      if (result.success && result.rewarded && result.reward) {
        // Ad was watched successfully, now trigger the Zustand action
        const rewardPayload: AdRewardPayload = {
          ...result.reward,
          itemId: itemId,
        };

        // --- THE CORE CHANGE ---
        // Instead of dispatching, we call the action from the store directly.
        // We provide the UID as required by our new action's signature.
        await applyAdRewardAction(user.uid, rewardPayload);
        
        console.log(`[useAds] Ad reward for item ${itemId} applied via Zustand store.`);
        return true;

      } else {
        setError(result.error || 'Ad was not completed.');
        console.warn('[useAds] Ad was not rewarded. Reason:', result.error);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while showing the ad.';
      setError(errorMessage);
      console.error('[useAds] Failed to show ad:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    isLoading,
    error,
    showAdToUnlockItem,
    clearError,
  };
};

export default useAds;