// src/services/adService.ts

// --- TYPE DEFINITIONS (Unchanged) ---
export interface AdConfig {
  ayetStudiosSlotId: string;
  testMode: boolean;
}
export interface AdReward {
  type: 'premium_unlock';
  duration: number; // in milliseconds
  timestamp: number;
}
export interface AdLoadResult {
  success: boolean;
  error?: string;
}
export interface AdShowResult {
  success: boolean;
  rewarded: boolean;
  reward?: AdReward;
  error?: string;
}

// --- SERVICE CLASS ---
class AdService {
  private config: AdConfig;
  private isInitialized = false;

  constructor() {
    this.config = {
      ayetStudiosSlotId: import.meta.env.VITE_AYET_STUDIOS_SLOT_ID || '',
      testMode: import.meta.env.NODE_ENV !== 'production'
    };
  }

  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;
    if (!this.config.ayetStudiosSlotId) {
      console.error('[Ad Service] Initialization failed: VITE_AYET_STUDIOS_SLOT_ID is not set.');
      return false;
    }
    this.isInitialized = true;
    console.log('[Ad Service] Initialized successfully for ayet-studios.');
    return true;
  }

  async loadRewardedAd(): Promise<AdLoadResult> {
    if (!this.isInitialized) await this.initialize();
    return { success: true };
  }

  async showRewardedAd(userId: string): Promise<AdShowResult> {
    if (!this.isInitialized) {
      return { success: false, rewarded: false, error: 'Ad service not initialized.' };
    }
    if (!userId) {
      return { success: false, rewarded: false, error: 'User ID is required to show an ad.' };
    }

    return new Promise((resolve) => {
      const modalContainer = document.createElement('div');
      modalContainer.id = 'ayet-ad-modal-container';
      modalContainer.style.position = 'fixed';
      modalContainer.style.top = '0';
      modalContainer.style.left = '0';
      modalContainer.style.width = '100vw';
      modalContainer.style.height = '100vh';
      modalContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      modalContainer.style.zIndex = '9999';
      modalContainer.style.display = 'flex';
      modalContainer.style.alignItems = 'center';
      modalContainer.style.justifyContent = 'center';

      const iframe = document.createElement('iframe');
      iframe.src = `https://www.ayetstudios.com/offers/web_offerwall/${this.config.ayetStudiosSlotId}?external_identifier=${userId}`;
      iframe.style.width = '95%';
      iframe.style.height = '95%';
      iframe.style.maxWidth = '800px';
      iframe.style.maxHeight = '900px';
      iframe.style.border = 'none';
      iframe.style.borderRadius = '10px';

      // --- NEW: Add a manual close button ---
      const closeButton = document.createElement('button');
      closeButton.innerHTML = '×'; // A simple 'X' character
      closeButton.style.position = 'absolute';
      closeButton.style.top = '10px';
      closeButton.style.right = '15px';
      closeButton.style.fontSize = '2rem';
      closeButton.style.color = 'white';
      closeButton.style.backgroundColor = 'transparent';
      closeButton.style.border = 'none';
      closeButton.style.cursor = 'pointer';
      closeButton.style.zIndex = '10000'; // Make sure it's above the iframe

      modalContainer.appendChild(iframe);
      modalContainer.appendChild(closeButton); // Add the button to the modal
      document.body.appendChild(modalContainer);
      
      const cleanup = () => {
        window.removeEventListener('message', handleMessage);
        if (document.body.contains(modalContainer)) {
          document.body.removeChild(modalContainer);
        }
      };

      // The close button's action
      closeButton.onclick = () => {
        console.log('[Ad Service] Modal closed manually by user.');
        cleanup();
        resolve({ success: true, rewarded: false, error: 'User closed the ad modal.' });
      };

      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== 'https://www.ayetstudios.com') return;
        let data;
        try { data = JSON.parse(event.data); } catch (e) { return; }

        switch (data.event_name) {
          case 'REWARD_RECEIVED':
            console.log('[Ad Service] Reward received:', data);
            cleanup();
            resolve({
              success: true,
              rewarded: true,
              reward: { type: 'premium_unlock', duration: 60 * 60 * 1000, timestamp: Date.now() }
            });
            break;
          case 'OFFERWALL_CLOSE':
            console.log('[Ad Service] Offerwall closed by iframe event.');
            cleanup();
            resolve({ success: true, rewarded: false, error: 'User closed the offerwall.' });
            break;
        }
      };

      window.addEventListener('message', handleMessage);
    });
  }

  isRewardedAdReady(): boolean {
    return this.isInitialized;
  }
}

export const adService = new AdService();
export default adService;