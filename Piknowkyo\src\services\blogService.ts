import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  query,
  orderBy,
  doc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  where
} from 'firebase/firestore';
import { BlogPost, BlogComment } from '../types/blog';

const postsCollectionRef = collection(db, 'blogPosts');
const commentsCollectionRef = collection(db, 'blogComments');

export const fetchPosts = async (): Promise<BlogPost[]> => {
  const q = query(postsCollectionRef, orderBy('createdAt', 'desc'));
  const querySnapshot = await getDocs(q);
  
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as BlogPost[];
};

export const createPost = async (newPost: {
  title: string;
  content: string;
  authorId: string;
}): Promise<BlogPost> => {
  const postData = {
    ...newPost,
    authorPseudo: 'Anonymous',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    likes: []
  };
  
  const docRef = await addDoc(postsCollectionRef, postData);
  return { id: docRef.id, ...postData };
};

export const toggleLike = async (postId: string, userId: string): Promise<BlogPost> => {
  const postRef = doc(postsCollectionRef, postId);
  const postDoc = await getDoc(postRef);
  
  if (!postDoc.exists()) {
    throw new Error('Post not found');
  }
  
  const postData = postDoc.data() as BlogPost;
  const isLiked = postData.likes.includes(userId);
  
  if (isLiked) {
    await updateDoc(postRef, {
      likes: arrayRemove(userId)
    });
  } else {
    await updateDoc(postRef, {
      likes: arrayUnion(userId)
    });
  }
  
  return {
    ...postData,
    id: postId,
    likes: isLiked
      ? postData.likes.filter(id => id !== userId)
      : [...postData.likes, userId]
  };
};

export const addComment = async (
  postId: string,
  content: string,
  authorId: string
): Promise<BlogComment> => {
  const commentData = {
    postId,
    content,
    authorId,
    authorPseudo: 'Anonymous',
    createdAt: new Date().toISOString()
  };
  
  const docRef = await addDoc(commentsCollectionRef, commentData);
  return { id: docRef.id, ...commentData };
};

export const fetchComments = async (postId: string): Promise<BlogComment[]> => {
  const q = query(
    commentsCollectionRef,
    where('postId', '==', postId),
    orderBy('createdAt', 'asc')
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as BlogComment[];
};