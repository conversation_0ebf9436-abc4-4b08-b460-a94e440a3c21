// pmng/src/firebase.ts

// Value imports only - we will infer types directly from these functions
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { 
  getFirestore, 
  initializeFirestore,
  persistentLocalCache,
} from "firebase/firestore";
import { getStorage } from "firebase/storage";

import clientEnv from "./config/clientEnvironment";

// --- Type Inference using TypeScript's ReturnType utility ---
// This is a robust way to get the types without direct imports, avoiding naming conflicts.
type FirebaseApp = ReturnType<typeof initializeApp>;
type Auth = ReturnType<typeof getAuth>;
type Firestore = ReturnType<typeof getFirestore>;
type FirebaseStorage = ReturnType<typeof getStorage>;


// Your web app's Firebase configuration
const firebaseConfig = clientEnv.firebase;

// Initialize Firebase App
const firebaseApp: FirebaseApp = initializeApp(firebaseConfig);

// Initialize Authentication and Storage
const auth: Auth = getAuth(firebaseApp);
const storage: FirebaseStorage = getStorage(firebaseApp);

// --- Firestore Initialization with Persistence ---
let db: Firestore;

try {
  // This is the modern and recommended way to enable persistence.
  db = initializeFirestore(firebaseApp, {
    localCache: persistentLocalCache({})
  });
  console.log("Firebase persistence with multi-tab support enabled successfully.");

} catch (err: any) {
  // This block catches initialization errors
  console.error("Error initializing Firestore with persistence:", err);
  if (err.code === 'unimplemented') {
    console.error("Firebase persistence is not supported in this browser environment.");
  }
  
  // Fallback to a regular in-memory instance of Firestore if persistence fails.
  console.warn("Falling back to a regular in-memory Firestore instance.");
  db = getFirestore(firebaseApp);
}

// Export the initialized services to be used throughout the app
export { firebaseApp, db, auth, storage };